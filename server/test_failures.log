2025-08-07 18:05:36,096 - ERROR - Test Failed: tests/test_cleanup_utilities.py::TestCleanupUtilities::test_state_verification_with_multiple_entities
Failure Details:
tests/test_cleanup_utilities.py:108: in test_state_verification_with_multiple_entities
    assert isinstance(state_info["users"], int)
E   AssertionError: assert False
E    +  where False = isinstance('Error: (psycopg2.errors.InFailedSqlTransaction) current transaction is aborted, commands ignored until end of transaction block\n\n[SQL: SELECT COUNT(*) FROM users]\n(Background on this error at: https://sqlalche.me/e/20/2j85)', int)
2025-08-07 18:05:36,339 - ERROR - Test Failed: tests/test_cleanup_utilities.py::TestCleanupUtilities::test_transaction_state_tracking
Failure Details:
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1963: in _exec_single_context
    self.dialect.do_execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py:943: in do_execute
    cursor.execute(statement, parameters)
E   psycopg2.errors.InFailedSqlTransaction: current transaction is aborted, commands ignored until end of transaction block

The above exception was the direct cause of the following exception:
tests/test_cleanup_utilities.py:139: in test_transaction_state_tracking
    db_session.flush()
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4345: in flush
    self._flush(objects)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4480: in _flush
    with util.safe_reraise():
         ^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/util/langhelpers.py:224: in __exit__
    raise exc_value.with_traceback(exc_tb)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4441: in _flush
    flush_context.execute()
.venv/lib/python3.13/site-packages/sqlalchemy/orm/unitofwork.py:466: in execute
    rec.execute(self)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/unitofwork.py:642: in execute
    util.preloaded.orm_persistence.save_obj(
.venv/lib/python3.13/site-packages/sqlalchemy/orm/persistence.py:93: in save_obj
    _emit_insert_statements(
.venv/lib/python3.13/site-packages/sqlalchemy/orm/persistence.py:1233: in _emit_insert_statements
    result = connection.execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1415: in execute
    return meth(
.venv/lib/python3.13/site-packages/sqlalchemy/sql/elements.py:523: in _execute_on_connection
    return connection._execute_clauseelement(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1637: in _execute_clauseelement
    ret = self._execute_context(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1842: in _execute_context
    return self._exec_single_context(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1982: in _exec_single_context
    self._handle_dbapi_exception(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:2351: in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1963: in _exec_single_context
    self.dialect.do_execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py:943: in do_execute
    cursor.execute(statement, parameters)
E   sqlalchemy.exc.InternalError: (psycopg2.errors.InFailedSqlTransaction) current transaction is aborted, commands ignored until end of transaction block
E   
E   [SQL: INSERT INTO users (email, password_hash, is_active, is_superuser, last_login, name, notes, created_at, updated_at, is_deleted, deleted_at, deleted_by_user_id) VALUES (%(email)s, %(password_hash)s, %(is_active)s, %(is_superuser)s, %(last_login)s, %(name)s, %(notes)s, %(created_at)s, %(updated_at)s, %(is_deleted)s, %(deleted_at)s, %(deleted_by_user_id)s) RETURNING users.id]
E   [parameters: {'email': '<EMAIL>', 'password_hash': 'test_password', 'is_active': True, 'is_superuser': False, 'last_login': None, 'name': 'State Tracking User', 'notes': None, 'created_at': datetime.datetime(2025, 8, 7, 15, 5, 36, 134756), 'updated_at': datetime.datetime(2025, 8, 7, 15, 5, 36, 134760), 'is_deleted': False, 'deleted_at': None, 'deleted_by_user_id': None}]
E   (Background on this error at: https://sqlalche.me/e/20/2j85)
2025-08-07 18:05:36,524 - ERROR - Test Failed: tests/test_cleanup_utilities.py::TestCleanupUtilities::test_isolation_validation_integration
Failure Details:
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1963: in _exec_single_context
    self.dialect.do_execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py:943: in do_execute
    cursor.execute(statement, parameters)
E   psycopg2.errors.InFailedSqlTransaction: current transaction is aborted, commands ignored until end of transaction block

The above exception was the direct cause of the following exception:
tests/test_cleanup_utilities.py:157: in test_isolation_validation_integration
    db_session.flush()
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4345: in flush
    self._flush(objects)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4480: in _flush
    with util.safe_reraise():
         ^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/util/langhelpers.py:224: in __exit__
    raise exc_value.with_traceback(exc_tb)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4441: in _flush
    flush_context.execute()
.venv/lib/python3.13/site-packages/sqlalchemy/orm/unitofwork.py:466: in execute
    rec.execute(self)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/unitofwork.py:642: in execute
    util.preloaded.orm_persistence.save_obj(
.venv/lib/python3.13/site-packages/sqlalchemy/orm/persistence.py:93: in save_obj
    _emit_insert_statements(
.venv/lib/python3.13/site-packages/sqlalchemy/orm/persistence.py:1233: in _emit_insert_statements
    result = connection.execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1415: in execute
    return meth(
.venv/lib/python3.13/site-packages/sqlalchemy/sql/elements.py:523: in _execute_on_connection
    return connection._execute_clauseelement(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1637: in _execute_clauseelement
    ret = self._execute_context(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1842: in _execute_context
    return self._exec_single_context(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1982: in _exec_single_context
    self._handle_dbapi_exception(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:2351: in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1963: in _exec_single_context
    self.dialect.do_execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py:943: in do_execute
    cursor.execute(statement, parameters)
E   sqlalchemy.exc.InternalError: (psycopg2.errors.InFailedSqlTransaction) current transaction is aborted, commands ignored until end of transaction block
E   
E   [SQL: INSERT INTO users (email, password_hash, is_active, is_superuser, last_login, name, notes, created_at, updated_at, is_deleted, deleted_at, deleted_by_user_id) VALUES (%(email)s, %(password_hash)s, %(is_active)s, %(is_superuser)s, %(last_login)s, %(name)s, %(notes)s, %(created_at)s, %(updated_at)s, %(is_deleted)s, %(deleted_at)s, %(deleted_by_user_id)s) RETURNING users.id]
E   [parameters: {'email': '<EMAIL>', 'password_hash': 'test_password', 'is_active': True, 'is_superuser': False, 'last_login': None, 'name': 'Integration Test User', 'notes': None, 'created_at': datetime.datetime(2025, 8, 7, 15, 5, 36, 361016), 'updated_at': datetime.datetime(2025, 8, 7, 15, 5, 36, 361019), 'is_deleted': False, 'deleted_at': None, 'deleted_by_user_id': None}]
E   (Background on this error at: https://sqlalche.me/e/20/2j85)
