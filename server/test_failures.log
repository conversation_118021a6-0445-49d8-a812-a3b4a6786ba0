2025-08-07 18:16:27,684 - ERROR - Test Failed: tests/api/v1/test_project_routes.py::TestProjectMemberRoutes::test_add_project_member_user_not_found
Failure Details:
E   assert 201 == 404
     +  where 201 = <Response [201 Created]>.status_code
2025-08-07 18:16:34,129 - ERROR - Test Failed: tests/api/v1/test_project_routes.py::TestProjectRoutes::test_get_project_success
Failure Details:
+ Exception Group Traceback (most recent call last):
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 77, in collapse_excgroups
  |     yield
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 183, in __call__
  |     async with anyio.create_task_group() as task_group:
  |                ~~~~~~~~~~~~~~~~~~~~~~~^^
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/anyio/_backends/_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  |         "unhandled errors in a TaskGroup", self._exceptions
  |     ) from None
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 344, in from_call
    |     result: TResult | None = func()
    |                              ~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 246, in <lambda>
    |     lambda: runtest_hook(item=item, **kwds), when=when, reraise=reraise
    |             ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/logging.py", line 850, in pytest_runtest_call
    |     yield
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/capture.py", line 900, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/skipping.py", line 263, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 178, in pytest_runtest_call
    |     item.runtest()
    |     ~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 426, in runtest
    |     super().runtest()
    |     ~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 1671, in runtest
    |     self.ihook.pytest_pyfunc_call(pyfuncitem=self)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 157, in pytest_pyfunc_call
    |     result = testfunction(**testargs)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 642, in inner
    |     _loop.run_until_complete(task)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    |     return future.result()
    |            ~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/tests/api/v1/test_project_routes.py", line 117, in test_get_project_success
    |     response = await authenticated_client.get(f"/api/v1/projects/{async_test_project.id}")
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1768, in get
    |     return await self.request(
    |            ^^^^^^^^^^^^^^^^^^^
    |     ...<9 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    |     return await self.send(request, auth=auth, follow_redirects=follow_redirects)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    |     response = await self._send_handling_auth(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<4 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    |     response = await self._send_handling_redirects(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<3 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    |     response = await self._send_single_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    |     response = await transport.handle_async_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    |     await super().__call__(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    |     await self.app(scope, receive, _send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 1212, in async_wrapper
    |     raise HTTPException(
    |     ...<2 lines>...
    |     )
    | fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.
    +------------------------------------

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/ued/server/tests/api/v1/test_project_routes.py", line 117, in test_get_project_success
    response = await authenticated_client.get(f"/api/v1/projects/{async_test_project.id}")
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1768, in get
    return await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<9 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 1212, in async_wrapper
    raise HTTPException(
    ...<2 lines>...
    )
fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.

During handling of the above exception, another exception occurred:
E   fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.
2025-08-07 18:16:34,767 - ERROR - Test Failed: tests/api/v1/test_project_routes.py::TestProjectRoutes::test_get_project_not_found
Failure Details:
+ Exception Group Traceback (most recent call last):
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 77, in collapse_excgroups
  |     yield
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 183, in __call__
  |     async with anyio.create_task_group() as task_group:
  |                ~~~~~~~~~~~~~~~~~~~~~~~^^
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/anyio/_backends/_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  |         "unhandled errors in a TaskGroup", self._exceptions
  |     ) from None
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 344, in from_call
    |     result: TResult | None = func()
    |                              ~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 246, in <lambda>
    |     lambda: runtest_hook(item=item, **kwds), when=when, reraise=reraise
    |             ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/logging.py", line 850, in pytest_runtest_call
    |     yield
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/capture.py", line 900, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/skipping.py", line 263, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 178, in pytest_runtest_call
    |     item.runtest()
    |     ~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 426, in runtest
    |     super().runtest()
    |     ~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 1671, in runtest
    |     self.ihook.pytest_pyfunc_call(pyfuncitem=self)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 157, in pytest_pyfunc_call
    |     result = testfunction(**testargs)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 642, in inner
    |     _loop.run_until_complete(task)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    |     return future.result()
    |            ~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/tests/api/v1/test_project_routes.py", line 125, in test_get_project_not_found
    |     response = await authenticated_client.get("/api/v1/projects/999")
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1768, in get
    |     return await self.request(
    |            ^^^^^^^^^^^^^^^^^^^
    |     ...<9 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    |     return await self.send(request, auth=auth, follow_redirects=follow_redirects)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    |     response = await self._send_handling_auth(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<4 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    |     response = await self._send_handling_redirects(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<3 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    |     response = await self._send_single_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    |     response = await transport.handle_async_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    |     await super().__call__(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    |     await self.app(scope, receive, _send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 1212, in async_wrapper
    |     raise HTTPException(
    |     ...<2 lines>...
    |     )
    | fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.
    +------------------------------------

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/ued/server/tests/api/v1/test_project_routes.py", line 125, in test_get_project_not_found
    response = await authenticated_client.get("/api/v1/projects/999")
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1768, in get
    return await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<9 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 1212, in async_wrapper
    raise HTTPException(
    ...<2 lines>...
    )
fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.

During handling of the above exception, another exception occurred:
E   fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.
2025-08-07 18:16:36,054 - ERROR - Test Failed: tests/api/v1/test_project_routes.py::TestProjectRoutes::test_update_project_not_found
Failure Details:
+ Exception Group Traceback (most recent call last):
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 77, in collapse_excgroups
  |     yield
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 183, in __call__
  |     async with anyio.create_task_group() as task_group:
  |                ~~~~~~~~~~~~~~~~~~~~~~~^^
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/anyio/_backends/_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  |         "unhandled errors in a TaskGroup", self._exceptions
  |     ) from None
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 344, in from_call
    |     result: TResult | None = func()
    |                              ~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 246, in <lambda>
    |     lambda: runtest_hook(item=item, **kwds), when=when, reraise=reraise
    |             ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/logging.py", line 850, in pytest_runtest_call
    |     yield
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/capture.py", line 900, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/skipping.py", line 263, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 178, in pytest_runtest_call
    |     item.runtest()
    |     ~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 426, in runtest
    |     super().runtest()
    |     ~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 1671, in runtest
    |     self.ihook.pytest_pyfunc_call(pyfuncitem=self)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 157, in pytest_pyfunc_call
    |     result = testfunction(**testargs)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 642, in inner
    |     _loop.run_until_complete(task)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    |     return future.result()
    |            ~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/tests/api/v1/test_project_routes.py", line 139, in test_update_project_not_found
    |     response = await authenticated_client.put("/api/v1/projects/999", json=update_data)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1896, in put
    |     return await self.request(
    |            ^^^^^^^^^^^^^^^^^^^
    |     ...<13 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    |     return await self.send(request, auth=auth, follow_redirects=follow_redirects)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    |     response = await self._send_handling_auth(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<4 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    |     response = await self._send_handling_redirects(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<3 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    |     response = await self._send_single_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    |     response = await transport.handle_async_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    |     await super().__call__(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    |     await self.app(scope, receive, _send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 1181, in async_wrapper
    |     raise e
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 1172, in async_wrapper
    |     return await result
    |            ^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/security_middleware.py", line 290, in dispatch
    |     final_response: Response = await call_next_with_logging(request)
    |                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/security_middleware.py", line 157, in call_next_with_logging
    |     response = await original_call_next(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    |     raise app_exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/rate_limiting_middleware.py", line 147, in dispatch
    |     return await call_next(request)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    |     raise app_exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/monitoring/unified_performance_monitor.py", line 263, in async_wrapper
    |     return await result
    |            ^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/caching_middleware.py", line 159, in dispatch
    |     response = await call_next(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    |     raise app_exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/logging_middleware.py", line 110, in dispatch
    |     response = await call_next_with_logging(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/logging_middleware.py", line 93, in call_next_with_logging
    |     response = await original_call_next(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    |     raise app_exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/context_middleware.py", line 134, in dispatch
    |     return await call_next(request)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    |     raise app_exc
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/context_middleware.py", line 116, in dispatch
    |     response = await call_next_with_logging(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/context_middleware.py", line 79, in call_next_with_logging
    |     response = await original_call_next(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    |     raise app_exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/exceptions.py", line 63, in __call__
    |     await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    |     await app(scope, receive, sender)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 716, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 736, in app
    |     await route.handle(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 290, in handle
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 78, in app
    |     await wrap_app_handling_exceptions(app, request)(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    |     await app(scope, receive, sender)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 75, in app
    |     response = await f(request)
    |                ^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/routing.py", line 302, in app
    |     raw_response = await run_endpoint_function(
    |                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<3 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/routing.py", line 213, in run_endpoint_function
    |     return await dependant.call(**values)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 634, in async_wrapper
    |     return await func(*args, **kwargs)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/utils/crud_endpoint_factory.py", line 343, in update_entity
    |     updated_entity = await update_method(id, entity_data)
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/monitoring/unified_performance_monitor.py", line 263, in async_wrapper
    |     return await result
    |            ^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/services/general/project_service.py", line 141, in update_project
    |     existing_project = await self._get_project_by_id_or_code(project_id)
    |                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/services/general/project_service.py", line 264, in _get_project_by_id_or_code
    |     raise ProjectNotFoundError(project_id=project_id)
    | src.core.errors.exceptions.ProjectNotFoundError: Project with ID '999' not found.
    +------------------------------------

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/ued/server/tests/api/v1/test_project_routes.py", line 139, in test_update_project_not_found
    response = await authenticated_client.put("/api/v1/projects/999", json=update_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1896, in put
    return await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<13 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 1181, in async_wrapper
    raise e
  File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 1172, in async_wrapper
    return await result
           ^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/security_middleware.py", line 290, in dispatch
    final_response: Response = await call_next_with_logging(request)
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/security_middleware.py", line 157, in call_next_with_logging
    response = await original_call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    raise app_exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/rate_limiting_middleware.py", line 147, in dispatch
    return await call_next(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    raise app_exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/monitoring/unified_performance_monitor.py", line 263, in async_wrapper
    return await result
           ^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/caching_middleware.py", line 159, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    raise app_exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/logging_middleware.py", line 110, in dispatch
    response = await call_next_with_logging(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/logging_middleware.py", line 93, in call_next_with_logging
    response = await original_call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    raise app_exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/context_middleware.py", line 134, in dispatch
    return await call_next(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    raise app_exc
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/context_middleware.py", line 116, in dispatch
    response = await call_next_with_logging(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/context_middleware.py", line 79, in call_next_with_logging
    response = await original_call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    raise app_exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 75, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/routing.py", line 213, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 634, in async_wrapper
    return await func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/utils/crud_endpoint_factory.py", line 343, in update_entity
    updated_entity = await update_method(id, entity_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/monitoring/unified_performance_monitor.py", line 263, in async_wrapper
    return await result
           ^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/services/general/project_service.py", line 141, in update_project
    existing_project = await self._get_project_by_id_or_code(project_id)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/services/general/project_service.py", line 264, in _get_project_by_id_or_code
    raise ProjectNotFoundError(project_id=project_id)
src.core.errors.exceptions.ProjectNotFoundError: Project with ID '999' not found.

During handling of the above exception, another exception occurred:
E   src.core.errors.exceptions.ProjectNotFoundError: Project with ID '999' not found.
2025-08-07 18:16:36,700 - ERROR - Test Failed: tests/api/v1/test_project_routes.py::TestProjectRoutes::test_delete_project_success
Failure Details:
+ Exception Group Traceback (most recent call last):
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 77, in collapse_excgroups
  |     yield
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 183, in __call__
  |     async with anyio.create_task_group() as task_group:
  |                ~~~~~~~~~~~~~~~~~~~~~~~^^
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/anyio/_backends/_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  |         "unhandled errors in a TaskGroup", self._exceptions
  |     ) from None
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 344, in from_call
    |     result: TResult | None = func()
    |                              ~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 246, in <lambda>
    |     lambda: runtest_hook(item=item, **kwds), when=when, reraise=reraise
    |             ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/logging.py", line 850, in pytest_runtest_call
    |     yield
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/capture.py", line 900, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/skipping.py", line 263, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 178, in pytest_runtest_call
    |     item.runtest()
    |     ~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 426, in runtest
    |     super().runtest()
    |     ~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 1671, in runtest
    |     self.ihook.pytest_pyfunc_call(pyfuncitem=self)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 157, in pytest_pyfunc_call
    |     result = testfunction(**testargs)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 642, in inner
    |     _loop.run_until_complete(task)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    |     return future.result()
    |            ~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/tests/api/v1/test_project_routes.py", line 144, in test_delete_project_success
    |     response = await authenticated_client.delete(f"/api/v1/projects/{async_test_project.id}")
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1966, in delete
    |     return await self.request(
    |            ^^^^^^^^^^^^^^^^^^^
    |     ...<9 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    |     return await self.send(request, auth=auth, follow_redirects=follow_redirects)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    |     response = await self._send_handling_auth(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<4 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    |     response = await self._send_handling_redirects(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<3 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    |     response = await self._send_single_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    |     response = await transport.handle_async_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    |     await super().__call__(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    |     await self.app(scope, receive, _send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 1212, in async_wrapper
    |     raise HTTPException(
    |     ...<2 lines>...
    |     )
    | fastapi.exceptions.HTTPException: 500: Database error: Database infrastructure operation failed: delete_project
    +------------------------------------

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/ued/server/tests/api/v1/test_project_routes.py", line 144, in test_delete_project_success
    response = await authenticated_client.delete(f"/api/v1/projects/{async_test_project.id}")
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1966, in delete
    return await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<9 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 1212, in async_wrapper
    raise HTTPException(
    ...<2 lines>...
    )
fastapi.exceptions.HTTPException: 500: Database error: Database infrastructure operation failed: delete_project

During handling of the above exception, another exception occurred:
E   fastapi.exceptions.HTTPException: 500: Database error: Database infrastructure operation failed: delete_project
2025-08-07 18:16:37,311 - ERROR - Test Failed: tests/api/v1/test_project_routes.py::TestProjectRoutes::test_delete_project_not_found
Failure Details:
+ Exception Group Traceback (most recent call last):
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 77, in collapse_excgroups
  |     yield
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 183, in __call__
  |     async with anyio.create_task_group() as task_group:
  |                ~~~~~~~~~~~~~~~~~~~~~~~^^
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/anyio/_backends/_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  |         "unhandled errors in a TaskGroup", self._exceptions
  |     ) from None
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 344, in from_call
    |     result: TResult | None = func()
    |                              ~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 246, in <lambda>
    |     lambda: runtest_hook(item=item, **kwds), when=when, reraise=reraise
    |             ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/logging.py", line 850, in pytest_runtest_call
    |     yield
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/capture.py", line 900, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/skipping.py", line 263, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 178, in pytest_runtest_call
    |     item.runtest()
    |     ~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 426, in runtest
    |     super().runtest()
    |     ~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 1671, in runtest
    |     self.ihook.pytest_pyfunc_call(pyfuncitem=self)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 157, in pytest_pyfunc_call
    |     result = testfunction(**testargs)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 642, in inner
    |     _loop.run_until_complete(task)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    |     return future.result()
    |            ~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/tests/api/v1/test_project_routes.py", line 149, in test_delete_project_not_found
    |     response = await authenticated_client.delete("/api/v1/projects/999")
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1966, in delete
    |     return await self.request(
    |            ^^^^^^^^^^^^^^^^^^^
    |     ...<9 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    |     return await self.send(request, auth=auth, follow_redirects=follow_redirects)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    |     response = await self._send_handling_auth(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<4 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    |     response = await self._send_handling_redirects(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<3 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    |     response = await self._send_single_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    |     response = await transport.handle_async_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    |     await super().__call__(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    |     await self.app(scope, receive, _send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 1181, in async_wrapper
    |     raise e
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 1172, in async_wrapper
    |     return await result
    |            ^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/security_middleware.py", line 290, in dispatch
    |     final_response: Response = await call_next_with_logging(request)
    |                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/security_middleware.py", line 157, in call_next_with_logging
    |     response = await original_call_next(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    |     raise app_exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/rate_limiting_middleware.py", line 147, in dispatch
    |     return await call_next(request)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    |     raise app_exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/monitoring/unified_performance_monitor.py", line 263, in async_wrapper
    |     return await result
    |            ^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/caching_middleware.py", line 159, in dispatch
    |     response = await call_next(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    |     raise app_exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/logging_middleware.py", line 110, in dispatch
    |     response = await call_next_with_logging(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/logging_middleware.py", line 93, in call_next_with_logging
    |     response = await original_call_next(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    |     raise app_exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/context_middleware.py", line 134, in dispatch
    |     return await call_next(request)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    |     raise app_exc
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/context_middleware.py", line 116, in dispatch
    |     response = await call_next_with_logging(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/context_middleware.py", line 79, in call_next_with_logging
    |     response = await original_call_next(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    |     raise app_exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/exceptions.py", line 63, in __call__
    |     await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    |     await app(scope, receive, sender)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 716, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 736, in app
    |     await route.handle(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 290, in handle
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 78, in app
    |     await wrap_app_handling_exceptions(app, request)(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    |     await app(scope, receive, sender)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 75, in app
    |     response = await f(request)
    |                ^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/routing.py", line 302, in app
    |     raw_response = await run_endpoint_function(
    |                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<3 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/routing.py", line 213, in run_endpoint_function
    |     return await dependant.call(**values)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 634, in async_wrapper
    |     return await func(*args, **kwargs)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/utils/crud_endpoint_factory.py", line 386, in delete_entity
    |     result = await delete_method(id)
    |              ^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/monitoring/unified_performance_monitor.py", line 263, in async_wrapper
    |     return await result
    |            ^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/services/general/project_service.py", line 178, in delete_project
    |     existing_project = await self._get_project_by_id_or_code(project_id)
    |                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/services/general/project_service.py", line 264, in _get_project_by_id_or_code
    |     raise ProjectNotFoundError(project_id=project_id)
    | src.core.errors.exceptions.ProjectNotFoundError: Project with ID '999' not found.
    +------------------------------------

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/ued/server/tests/api/v1/test_project_routes.py", line 149, in test_delete_project_not_found
    response = await authenticated_client.delete("/api/v1/projects/999")
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1966, in delete
    return await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<9 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 1181, in async_wrapper
    raise e
  File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 1172, in async_wrapper
    return await result
           ^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/security_middleware.py", line 290, in dispatch
    final_response: Response = await call_next_with_logging(request)
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/security_middleware.py", line 157, in call_next_with_logging
    response = await original_call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    raise app_exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/rate_limiting_middleware.py", line 147, in dispatch
    return await call_next(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    raise app_exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/monitoring/unified_performance_monitor.py", line 263, in async_wrapper
    return await result
           ^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/caching_middleware.py", line 159, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    raise app_exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/logging_middleware.py", line 110, in dispatch
    response = await call_next_with_logging(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/logging_middleware.py", line 93, in call_next_with_logging
    response = await original_call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    raise app_exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/context_middleware.py", line 134, in dispatch
    return await call_next(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    raise app_exc
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/context_middleware.py", line 116, in dispatch
    response = await call_next_with_logging(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/context_middleware.py", line 79, in call_next_with_logging
    response = await original_call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    raise app_exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 75, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/routing.py", line 213, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 634, in async_wrapper
    return await func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/utils/crud_endpoint_factory.py", line 386, in delete_entity
    result = await delete_method(id)
             ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/monitoring/unified_performance_monitor.py", line 263, in async_wrapper
    return await result
           ^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/services/general/project_service.py", line 178, in delete_project
    existing_project = await self._get_project_by_id_or_code(project_id)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/services/general/project_service.py", line 264, in _get_project_by_id_or_code
    raise ProjectNotFoundError(project_id=project_id)
src.core.errors.exceptions.ProjectNotFoundError: Project with ID '999' not found.

During handling of the above exception, another exception occurred:
E   src.core.errors.exceptions.ProjectNotFoundError: Project with ID '999' not found.
2025-08-07 18:16:37,964 - ERROR - Test Failed: tests/api/v1/test_project_routes.py::TestProjectRoutes::test_get_projects_success
Failure Details:
+ Exception Group Traceback (most recent call last):
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 77, in collapse_excgroups
  |     yield
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 183, in __call__
  |     async with anyio.create_task_group() as task_group:
  |                ~~~~~~~~~~~~~~~~~~~~~~~^^
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/anyio/_backends/_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  |         "unhandled errors in a TaskGroup", self._exceptions
  |     ) from None
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 344, in from_call
    |     result: TResult | None = func()
    |                              ~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 246, in <lambda>
    |     lambda: runtest_hook(item=item, **kwds), when=when, reraise=reraise
    |             ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/logging.py", line 850, in pytest_runtest_call
    |     yield
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/capture.py", line 900, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/skipping.py", line 263, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 178, in pytest_runtest_call
    |     item.runtest()
    |     ~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 426, in runtest
    |     super().runtest()
    |     ~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 1671, in runtest
    |     self.ihook.pytest_pyfunc_call(pyfuncitem=self)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 157, in pytest_pyfunc_call
    |     result = testfunction(**testargs)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 642, in inner
    |     _loop.run_until_complete(task)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    |     return future.result()
    |            ~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/tests/api/v1/test_project_routes.py", line 154, in test_get_projects_success
    |     response = await authenticated_client.get("/api/v1/projects/")
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1768, in get
    |     return await self.request(
    |            ^^^^^^^^^^^^^^^^^^^
    |     ...<9 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    |     return await self.send(request, auth=auth, follow_redirects=follow_redirects)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    |     response = await self._send_handling_auth(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<4 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    |     response = await self._send_handling_redirects(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<3 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    |     response = await self._send_single_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    |     response = await transport.handle_async_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    |     await super().__call__(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    |     await self.app(scope, receive, _send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 1212, in async_wrapper
    |     raise HTTPException(
    |     ...<2 lines>...
    |     )
    | fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: Input validation failed.
    +------------------------------------

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/ued/server/tests/api/v1/test_project_routes.py", line 154, in test_get_projects_success
    response = await authenticated_client.get("/api/v1/projects/")
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1768, in get
    return await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<9 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 1212, in async_wrapper
    raise HTTPException(
    ...<2 lines>...
    )
fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: Input validation failed.

During handling of the above exception, another exception occurred:
E   fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: Input validation failed.
2025-08-07 18:16:38,931 - ERROR - Test Failed: tests/api/v1/test_project_routes.py::TestProjectRoutes::test_create_project_with_empty_name_returns_422
Failure Details:
+ Exception Group Traceback (most recent call last):
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 77, in collapse_excgroups
  |     yield
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 183, in __call__
  |     async with anyio.create_task_group() as task_group:
  |                ~~~~~~~~~~~~~~~~~~~~~~~^^
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/anyio/_backends/_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  |         "unhandled errors in a TaskGroup", self._exceptions
  |     ) from None
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 344, in from_call
    |     result: TResult | None = func()
    |                              ~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 246, in <lambda>
    |     lambda: runtest_hook(item=item, **kwds), when=when, reraise=reraise
    |             ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/logging.py", line 850, in pytest_runtest_call
    |     yield
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/capture.py", line 900, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/skipping.py", line 263, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 178, in pytest_runtest_call
    |     item.runtest()
    |     ~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 426, in runtest
    |     super().runtest()
    |     ~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 1671, in runtest
    |     self.ihook.pytest_pyfunc_call(pyfuncitem=self)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 157, in pytest_pyfunc_call
    |     result = testfunction(**testargs)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 642, in inner
    |     _loop.run_until_complete(task)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    |     return future.result()
    |            ~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/tests/api/v1/test_project_routes.py", line 175, in test_create_project_with_empty_name_returns_422
    |     response = await authenticated_client.post("/api/v1/projects/", json=invalid_project_data)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1859, in post
    |     return await self.request(
    |            ^^^^^^^^^^^^^^^^^^^
    |     ...<13 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    |     return await self.send(request, auth=auth, follow_redirects=follow_redirects)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    |     response = await self._send_handling_auth(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<4 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    |     response = await self._send_handling_redirects(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<3 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    |     response = await self._send_single_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    |     response = await transport.handle_async_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    |     await super().__call__(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    |     await self.app(scope, receive, _send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 1212, in async_wrapper
    |     raise HTTPException(
    |     ...<2 lines>...
    |     )
    | fastapi.exceptions.HTTPException: 500: An error occurred while processing the error response.
    +------------------------------------

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/ued/server/tests/api/v1/test_project_routes.py", line 175, in test_create_project_with_empty_name_returns_422
    response = await authenticated_client.post("/api/v1/projects/", json=invalid_project_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1859, in post
    return await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<13 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 1212, in async_wrapper
    raise HTTPException(
    ...<2 lines>...
    )
fastapi.exceptions.HTTPException: 500: An error occurred while processing the error response.

During handling of the above exception, another exception occurred:
E   fastapi.exceptions.HTTPException: 500: An error occurred while processing the error response.
2025-08-07 18:16:55,136 - ERROR - Test Failed: tests/api/v1/test_user_routes.py::TestUserRoutes::test_get_users_summary_admin
Failure Details:
+ Exception Group Traceback (most recent call last):
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 77, in collapse_excgroups
  |     yield
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 183, in __call__
  |     async with anyio.create_task_group() as task_group:
  |                ~~~~~~~~~~~~~~~~~~~~~~~^^
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/anyio/_backends/_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  |         "unhandled errors in a TaskGroup", self._exceptions
  |     ) from None
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 344, in from_call
    |     result: TResult | None = func()
    |                              ~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 246, in <lambda>
    |     lambda: runtest_hook(item=item, **kwds), when=when, reraise=reraise
    |             ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/logging.py", line 850, in pytest_runtest_call
    |     yield
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/capture.py", line 900, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/skipping.py", line 263, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 178, in pytest_runtest_call
    |     item.runtest()
    |     ~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 426, in runtest
    |     super().runtest()
    |     ~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 1671, in runtest
    |     self.ihook.pytest_pyfunc_call(pyfuncitem=self)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 157, in pytest_pyfunc_call
    |     result = testfunction(**testargs)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 642, in inner
    |     _loop.run_until_complete(task)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    |     return future.result()
    |            ~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/tests/api/v1/test_user_routes.py", line 63, in test_get_users_summary_admin
    |     response = await admin_client.get("/api/v1/users/summary")
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1768, in get
    |     return await self.request(
    |            ^^^^^^^^^^^^^^^^^^^
    |     ...<9 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    |     return await self.send(request, auth=auth, follow_redirects=follow_redirects)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    |     response = await self._send_handling_auth(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<4 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    |     response = await self._send_handling_redirects(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<3 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    |     response = await self._send_single_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    |     response = await transport.handle_async_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    |     await super().__call__(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    |     await self.app(scope, receive, _send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 1212, in async_wrapper
    |     raise HTTPException(
    |     ...<2 lines>...
    |     )
    | fastapi.exceptions.HTTPException: 500: API operation 'get_users_summary' failed: 1 validation error for UserReadSchema
    | name
    |   String should have at least 3 characters [type=string_too_short, input_value='', input_type=str]
    |     For further information visit https://errors.pydantic.dev/2.11/v/string_too_short
    +------------------------------------

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/ued/server/tests/api/v1/test_user_routes.py", line 63, in test_get_users_summary_admin
    response = await admin_client.get("/api/v1/users/summary")
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1768, in get
    return await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<9 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 1212, in async_wrapper
    raise HTTPException(
    ...<2 lines>...
    )
fastapi.exceptions.HTTPException: 500: API operation 'get_users_summary' failed: 1 validation error for UserReadSchema
name
  String should have at least 3 characters [type=string_too_short, input_value='', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/string_too_short

During handling of the above exception, another exception occurred:
E   fastapi.exceptions.HTTPException: 500: API operation 'get_users_summary' failed: 1 validation error for UserReadSchema
    name
      String should have at least 3 characters [type=string_too_short, input_value='', input_type=str]
        For further information visit https://errors.pydantic.dev/2.11/v/string_too_short
2025-08-07 18:16:56,767 - ERROR - Test Failed: tests/api/v1/test_user_routes.py::TestUserRoutes::test_get_users_summary_with_limit
Failure Details:
+ Exception Group Traceback (most recent call last):
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 77, in collapse_excgroups
  |     yield
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 183, in __call__
  |     async with anyio.create_task_group() as task_group:
  |                ~~~~~~~~~~~~~~~~~~~~~~~^^
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/anyio/_backends/_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  |         "unhandled errors in a TaskGroup", self._exceptions
  |     ) from None
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 344, in from_call
    |     result: TResult | None = func()
    |                              ~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 246, in <lambda>
    |     lambda: runtest_hook(item=item, **kwds), when=when, reraise=reraise
    |             ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/logging.py", line 850, in pytest_runtest_call
    |     yield
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/capture.py", line 900, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/skipping.py", line 263, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 178, in pytest_runtest_call
    |     item.runtest()
    |     ~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 426, in runtest
    |     super().runtest()
    |     ~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 1671, in runtest
    |     self.ihook.pytest_pyfunc_call(pyfuncitem=self)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 157, in pytest_pyfunc_call
    |     result = testfunction(**testargs)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 642, in inner
    |     _loop.run_until_complete(task)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    |     return future.result()
    |            ~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/tests/api/v1/test_user_routes.py", line 83, in test_get_users_summary_with_limit
    |     response = await admin_client.get("/api/v1/users/summary?limit=5")
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1768, in get
    |     return await self.request(
    |            ^^^^^^^^^^^^^^^^^^^
    |     ...<9 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    |     return await self.send(request, auth=auth, follow_redirects=follow_redirects)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    |     response = await self._send_handling_auth(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<4 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    |     response = await self._send_handling_redirects(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<3 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    |     response = await self._send_single_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    |     response = await transport.handle_async_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    |     await super().__call__(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    |     await self.app(scope, receive, _send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 1212, in async_wrapper
    |     raise HTTPException(
    |     ...<2 lines>...
    |     )
    | fastapi.exceptions.HTTPException: 500: API operation 'get_users_summary' failed: 1 validation error for UserReadSchema
    | name
    |   String should have at least 3 characters [type=string_too_short, input_value='', input_type=str]
    |     For further information visit https://errors.pydantic.dev/2.11/v/string_too_short
    +------------------------------------

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/ued/server/tests/api/v1/test_user_routes.py", line 83, in test_get_users_summary_with_limit
    response = await admin_client.get("/api/v1/users/summary?limit=5")
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1768, in get
    return await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<9 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 1212, in async_wrapper
    raise HTTPException(
    ...<2 lines>...
    )
fastapi.exceptions.HTTPException: 500: API operation 'get_users_summary' failed: 1 validation error for UserReadSchema
name
  String should have at least 3 characters [type=string_too_short, input_value='', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/string_too_short

During handling of the above exception, another exception occurred:
E   fastapi.exceptions.HTTPException: 500: API operation 'get_users_summary' failed: 1 validation error for UserReadSchema
    name
      String should have at least 3 characters [type=string_too_short, input_value='', input_type=str]
        For further information visit https://errors.pydantic.dev/2.11/v/string_too_short
