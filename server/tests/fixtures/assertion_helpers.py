"""Assertion helpers for ID-agnostic testing.

This module provides utilities for making assertions that don't depend on
specific database IDs, making tests more robust and predictable.
"""

from typing import Any, Dict, List, Optional, Union
from datetime import datetime
from decimal import Decimal


class AssertionHelpers:
    """Helpers for ID-agnostic assertions in integration tests."""
    
    @staticmethod
    def assert_valid_id(value: Any, field_name: str = "id") -> None:
        """Assert that a value is a valid positive integer ID."""
        assert isinstance(value, int), f"{field_name} should be an integer, got {type(value)}"
        assert value > 0, f"{field_name} should be positive, got {value}"
    
    @staticmethod
    def assert_valid_timestamp(value: Any, field_name: str = "timestamp") -> None:
        """Assert that a value is a valid timestamp string."""
        assert isinstance(value, str), f"{field_name} should be a string, got {type(value)}"
        # Try to parse as ISO format timestamp
        try:
            datetime.fromisoformat(value.replace('Z', '+00:00'))
        except ValueError:
            assert False, f"{field_name} should be a valid ISO timestamp, got {value}"
    
    @staticmethod
    def assert_user_properties(
        actual_user: Dict[str, Any],
        expected_name: str,
        expected_email: str,
        check_id: bool = True
    ) -> None:
        """Assert user properties without depending on specific ID."""
        if check_id:
            AssertionHelpers.assert_valid_id(actual_user.get("id"), "user.id")
        
        assert actual_user["name"] == expected_name, f"Expected name {expected_name}, got {actual_user['name']}"
        assert actual_user["email"] == expected_email, f"Expected email {expected_email}, got {actual_user['email']}"
        assert actual_user["is_active"] is True, f"User should be active, got {actual_user['is_active']}"
        
        # Check timestamps if present
        if "created_at" in actual_user:
            AssertionHelpers.assert_valid_timestamp(actual_user["created_at"], "user.created_at")
        if "updated_at" in actual_user:
            AssertionHelpers.assert_valid_timestamp(actual_user["updated_at"], "user.updated_at")
    
    @staticmethod
    def assert_project_properties(
        actual_project: Dict[str, Any],
        expected_name: str,
        user_id: int,
        expected_status: str = "ACTIVE",
        check_id: bool = True
    ) -> None:
        """Assert project properties with known user relationship."""
        if check_id:
            AssertionHelpers.assert_valid_id(actual_project.get("id"), "project.id")
        
        assert actual_project["name"] == expected_name, f"Expected name {expected_name}, got {actual_project['name']}"
        assert actual_project["created_by_user_id"] == user_id, f"Expected user_id {user_id}, got {actual_project['created_by_user_id']}"
        assert actual_project["status"] == expected_status, f"Expected status {expected_status}, got {actual_project['status']}"
        
        # Check timestamps if present
        if "created_at" in actual_project:
            AssertionHelpers.assert_valid_timestamp(actual_project["created_at"], "project.created_at")
        if "updated_at" in actual_project:
            AssertionHelpers.assert_valid_timestamp(actual_project["updated_at"], "project.updated_at")
    
    @staticmethod
    def assert_category_properties(
        actual_category: Dict[str, Any],
        expected_name: str,
        expected_description: str = None,
        expected_is_active: bool = True,
        expected_parent_id: Optional[int] = None,
        check_id: bool = True
    ) -> None:
        """Assert category properties without depending on specific ID."""
        if check_id:
            AssertionHelpers.assert_valid_id(actual_category.get("id"), "category.id")
        
        assert actual_category["name"] == expected_name, f"Expected name {expected_name}, got {actual_category['name']}"
        assert actual_category["is_active"] == expected_is_active, f"Expected is_active {expected_is_active}, got {actual_category['is_active']}"
        
        if expected_description is not None:
            assert actual_category["description"] == expected_description, f"Expected description {expected_description}, got {actual_category['description']}"
        
        if expected_parent_id is not None:
            assert actual_category["parent_category_id"] == expected_parent_id, f"Expected parent_id {expected_parent_id}, got {actual_category['parent_category_id']}"
        
        # Check timestamps if present
        if "created_at" in actual_category:
            AssertionHelpers.assert_valid_timestamp(actual_category["created_at"], "category.created_at")
        if "updated_at" in actual_category:
            AssertionHelpers.assert_valid_timestamp(actual_category["updated_at"], "category.updated_at")
    
    @staticmethod
    def assert_component_type_properties(
        actual_type: Dict[str, Any],
        expected_name: str,
        expected_category_id: int,
        expected_description: str = None,
        expected_is_active: bool = True,
        check_id: bool = True
    ) -> None:
        """Assert component type properties."""
        if check_id:
            AssertionHelpers.assert_valid_id(actual_type.get("id"), "component_type.id")
        
        assert actual_type["name"] == expected_name, f"Expected name {expected_name}, got {actual_type['name']}"
        assert actual_type["category_id"] == expected_category_id, f"Expected category_id {expected_category_id}, got {actual_type['category_id']}"
        assert actual_type["is_active"] == expected_is_active, f"Expected is_active {expected_is_active}, got {actual_type['is_active']}"
        
        if expected_description is not None:
            assert actual_type["description"] == expected_description, f"Expected description {expected_description}, got {actual_type['description']}"
        
        # Check timestamps if present
        if "created_at" in actual_type:
            AssertionHelpers.assert_valid_timestamp(actual_type["created_at"], "component_type.created_at")
        if "updated_at" in actual_type:
            AssertionHelpers.assert_valid_timestamp(actual_type["updated_at"], "component_type.updated_at")
    
    @staticmethod
    def assert_component_properties(
        actual_component: Dict[str, Any],
        expected_name: str,
        expected_manufacturer: str,
        expected_model_number: str,
        expected_type_id: int,
        expected_category_id: int,
        expected_unit_price: Union[float, Decimal] = None,
        check_id: bool = True
    ) -> None:
        """Assert component properties."""
        if check_id:
            AssertionHelpers.assert_valid_id(actual_component.get("id"), "component.id")
        
        assert actual_component["name"] == expected_name, f"Expected name {expected_name}, got {actual_component['name']}"
        assert actual_component["manufacturer"] == expected_manufacturer, f"Expected manufacturer {expected_manufacturer}, got {actual_component['manufacturer']}"
        assert actual_component["model_number"] == expected_model_number, f"Expected model_number {expected_model_number}, got {actual_component['model_number']}"
        assert actual_component["component_type_id"] == expected_type_id, f"Expected type_id {expected_type_id}, got {actual_component['component_type_id']}"
        assert actual_component["category_id"] == expected_category_id, f"Expected category_id {expected_category_id}, got {actual_component['category_id']}"
        
        if expected_unit_price is not None:
            actual_price = actual_component.get("unit_price")
            if isinstance(actual_price, str):
                actual_price = float(actual_price)
            expected_price = float(expected_unit_price)
            assert abs(actual_price - expected_price) < 0.01, f"Expected unit_price {expected_price}, got {actual_price}"
        
        # Check timestamps if present
        if "created_at" in actual_component:
            AssertionHelpers.assert_valid_timestamp(actual_component["created_at"], "component.created_at")
        if "updated_at" in actual_component:
            AssertionHelpers.assert_valid_timestamp(actual_component["updated_at"], "component.updated_at")
    
    @staticmethod
    def assert_task_properties(
        actual_task: Dict[str, Any],
        expected_title: str,
        expected_project_id: int,
        expected_assigned_to_user_id: int,
        expected_created_by_user_id: int,
        expected_status: str = "Not Started",
        check_id: bool = True
    ) -> None:
        """Assert task properties."""
        if check_id:
            AssertionHelpers.assert_valid_id(actual_task.get("id"), "task.id")
        
        assert actual_task["title"] == expected_title, f"Expected title {expected_title}, got {actual_task['title']}"
        assert actual_task["project_id"] == expected_project_id, f"Expected project_id {expected_project_id}, got {actual_task['project_id']}"
        assert actual_task["assigned_to_user_id"] == expected_assigned_to_user_id, f"Expected assigned_to_user_id {expected_assigned_to_user_id}, got {actual_task['assigned_to_user_id']}"
        assert actual_task["created_by_user_id"] == expected_created_by_user_id, f"Expected created_by_user_id {expected_created_by_user_id}, got {actual_task['created_by_user_id']}"
        assert actual_task["status"] == expected_status, f"Expected status {expected_status}, got {actual_task['status']}"
        
        # Check timestamps if present
        if "created_at" in actual_task:
            AssertionHelpers.assert_valid_timestamp(actual_task["created_at"], "task.created_at")
        if "updated_at" in actual_task:
            AssertionHelpers.assert_valid_timestamp(actual_task["updated_at"], "task.updated_at")
    
    @staticmethod
    def assert_pagination_response(
        response_data: Dict[str, Any],
        expected_items_key: str,
        expected_min_items: int = 0,
        expected_max_items: int = None
    ) -> None:
        """Assert pagination response structure."""
        # Check required pagination fields
        assert "total_count" in response_data, "Response should include total_count"
        assert "page" in response_data, "Response should include page"
        assert "per_page" in response_data, "Response should include per_page"
        assert "pages" in response_data, "Response should include pages"
        assert expected_items_key in response_data, f"Response should include {expected_items_key}"
        
        # Check types
        assert isinstance(response_data["total_count"], int), "total_count should be integer"
        assert isinstance(response_data["page"], int), "page should be integer"
        assert isinstance(response_data["per_page"], int), "per_page should be integer"
        assert isinstance(response_data["pages"], int), "pages should be integer"
        assert isinstance(response_data[expected_items_key], list), f"{expected_items_key} should be list"
        
        # Check values
        assert response_data["total_count"] >= 0, "total_count should be non-negative"
        assert response_data["page"] >= 1, "page should be at least 1"
        assert response_data["per_page"] >= 1, "per_page should be at least 1"
        assert response_data["pages"] >= 0, "pages should be non-negative"
        
        # Check items count
        items = response_data[expected_items_key]
        assert len(items) >= expected_min_items, f"Should have at least {expected_min_items} items, got {len(items)}"
        
        if expected_max_items is not None:
            assert len(items) <= expected_max_items, f"Should have at most {expected_max_items} items, got {len(items)}"
    
    @staticmethod
    def assert_error_response(
        response_data: Dict[str, Any],
        expected_error_type: str = None,
        expected_message_contains: str = None
    ) -> None:
        """Assert error response structure."""
        assert "error" in response_data, "Error response should include error field"
        
        error = response_data["error"]
        assert isinstance(error, dict), "Error should be a dictionary"
        
        if expected_error_type:
            assert "type" in error, "Error should include type field"
            assert error["type"] == expected_error_type, f"Expected error type {expected_error_type}, got {error['type']}"
        
        if expected_message_contains:
            assert "message" in error, "Error should include message field"
            assert expected_message_contains in error["message"], f"Expected message to contain '{expected_message_contains}', got '{error['message']}'"


class MockAssertionHelpers:
    """Helpers for asserting mock calls in unit tests."""
    
    @staticmethod
    def assert_mock_called_with_user(mock_call, expected_user_id: int) -> None:
        """Assert mock call was made with expected user ID."""
        args, kwargs = mock_call
        
        # Check if user_id is in kwargs
        if 'user_id' in kwargs:
            assert kwargs['user_id'] == expected_user_id, f"Expected user_id {expected_user_id}, got {kwargs['user_id']}"
        elif 'deleted_by_user_id' in kwargs:
            assert kwargs['deleted_by_user_id'] == expected_user_id, f"Expected deleted_by_user_id {expected_user_id}, got {kwargs['deleted_by_user_id']}"
        elif 'created_by_user_id' in kwargs:
            assert kwargs['created_by_user_id'] == expected_user_id, f"Expected created_by_user_id {expected_user_id}, got {kwargs['created_by_user_id']}"
        else:
            # Check positional arguments
            assert len(args) >= 2, f"Expected at least 2 arguments for user_id, got {len(args)}"
            assert args[1] == expected_user_id, f"Expected user_id {expected_user_id} in args[1], got {args[1]}"
    
    @staticmethod
    def assert_repository_create_called(mock_repo, expected_data_fields: Dict[str, Any]) -> None:
        """Assert repository create was called with expected data fields."""
        mock_repo.create.assert_called_once()
        
        # Get the call arguments
        args, kwargs = mock_repo.create.call_args
        
        # The data should be in the first argument or in kwargs
        if args:
            actual_data = args[0]
        elif 'data' in kwargs:
            actual_data = kwargs['data']
        else:
            assert False, "Expected create to be called with data argument"
        
        # Check expected fields
        for field, expected_value in expected_data_fields.items():
            if hasattr(actual_data, field):
                actual_value = getattr(actual_data, field)
            elif isinstance(actual_data, dict):
                actual_value = actual_data.get(field)
            else:
                assert False, f"Could not find field {field} in create data"
            
            assert actual_value == expected_value, f"Expected {field}={expected_value}, got {actual_value}"
