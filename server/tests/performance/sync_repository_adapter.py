"""Synchronous adapter for async repositories used in performance tests.

This module provides synchronous wrappers around async repositories to enable
their use in performance tests that require synchronous execution patterns,
particularly for threading and multiprocessing scenarios.
"""

import asyncio
from typing import Optional, List
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession

from src.core.models.general.user import User
from src.core.repositories.general.user_repository import UserRepository


class SyncUserRepositoryAdapter:
    """Synchronous adapter for UserRepository.

    This adapter wraps the async UserRepository methods and executes them
    synchronously using asyncio.run(). Each method creates a fresh async
    session to avoid event loop conflicts.
    """

    def __init__(self, sync_session: Session):
        """Initialize the adapter with a synchronous session.

        Note: The sync_session is kept for reference but each async operation
        will create its own fresh async session to avoid event loop issues.
        """
        self.sync_session = sync_session

    async def _create_async_session(self):
        """Create a fresh async session with isolated engine for each operation."""
        from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker, AsyncSession
        from src.config.settings import settings

        # Use the same database URL from settings to ensure consistency
        # The sync session should be using the same URL from settings
        db_url = settings.DATABASE_URL

        # Convert to async URL
        if "postgresql" in db_url and "asyncpg" not in db_url:
            async_url = db_url.replace("postgresql://", "postgresql+asyncpg://")
        else:
            async_url = db_url

        print(f"DEBUG: Using database URL from settings for async connection")

        # Create isolated async engine with minimal pool settings for performance tests
        isolated_engine = create_async_engine(
            async_url,
            echo=False,  # Disable echo for performance tests
            pool_size=1,  # Minimal pool for isolated operations
            max_overflow=0,  # No overflow for isolated operations
            pool_pre_ping=True,
            # Force new connections to ensure we see committed data
            pool_recycle=1,  # Recycle connections quickly
        )

        # Create session factory with the isolated engine
        session_factory = async_sessionmaker(
            bind=isolated_engine,
            class_=AsyncSession,
            expire_on_commit=False,
            autocommit=False,
            autoflush=False,
        )

        session = session_factory()
        # Return both session and engine so we can dispose the engine later
        return session, isolated_engine

    def create(self, user_data: dict) -> User:
        """Create a user synchronously."""

        async def _create():
            async_session, isolated_engine = await self._create_async_session()
            try:
                user_repo = UserRepository(async_session)
                result = await user_repo.create(user_data)
                await async_session.commit()
                return result
            finally:
                await async_session.close()
                await isolated_engine.dispose()

        return asyncio.run(_create())

    def get_by_id(self, user_id: int) -> Optional[User]:
        """Get user by ID synchronously."""

        async def _get_by_id():
            async_session, isolated_engine = await self._create_async_session()
            try:
                user_repo = UserRepository(async_session)
                return await user_repo.get_by_id(user_id)
            finally:
                await async_session.close()
                await isolated_engine.dispose()

        return asyncio.run(_get_by_id())

    def get_by_email(self, email: str) -> Optional[User]:
        """Get user by email synchronously."""

        async def _get_by_email():
            async_session, isolated_engine = await self._create_async_session()
            try:
                user_repo = UserRepository(async_session)
                return await user_repo.get_by_email(email)
            finally:
                await async_session.close()
                await isolated_engine.dispose()

        return asyncio.run(_get_by_email())

    def check_email_exists(self, email: str, exclude_user_id: Optional[int] = None) -> bool:
        """Check if email exists synchronously."""

        async def _check_email_exists():
            async_session, isolated_engine = await self._create_async_session()
            try:
                user_repo = UserRepository(async_session)
                return await user_repo.check_email_exists(email, exclude_user_id)
            finally:
                await async_session.close()
                await isolated_engine.dispose()

        return asyncio.run(_check_email_exists())

    def get_by_name(self, name: str) -> Optional[User]:
        """Get user by name synchronously."""

        async def _get_by_name():
            async_session, isolated_engine = await self._create_async_session()
            try:
                user_repo = UserRepository(async_session)
                return await user_repo.get_by_name(name)
            finally:
                await async_session.close()
                await isolated_engine.dispose()

        return asyncio.run(_get_by_name())

    def update(self, user_id: int, update_data: dict) -> Optional[User]:
        """Update user synchronously."""

        async def _update():
            async_session, isolated_engine = await self._create_async_session()
            try:
                user_repo = UserRepository(async_session)
                result = await user_repo.update(user_id, update_data)
                await async_session.commit()
                return result
            finally:
                await async_session.close()
                await isolated_engine.dispose()

        return asyncio.run(_update())

    def delete(self, user_id: int) -> bool:
        """Delete user synchronously."""

        async def _delete():
            async_session, isolated_engine = await self._create_async_session()
            try:
                user_repo = UserRepository(async_session)
                result = await user_repo.delete(user_id)
                await async_session.commit()
                return result
            finally:
                await async_session.close()
                await isolated_engine.dispose()

        return asyncio.run(_delete())

    def get_active_users(self, skip: int = 0, limit: int = 100) -> List[User]:
        """Get active users synchronously."""

        async def _get_active_users():
            async_session, isolated_engine = await self._create_async_session()
            try:
                user_repo = UserRepository(async_session)
                return await user_repo.get_active_users(skip, limit)
            finally:
                await async_session.close()
                await isolated_engine.dispose()

        return asyncio.run(_get_active_users())

    def search_users(self, search_term: str, skip: int = 0, limit: int = 100) -> List[User]:
        """Search users synchronously."""

        async def _search_users():
            async_session, isolated_engine = await self._create_async_session()
            try:
                user_repo = UserRepository(async_session)
                return await user_repo.search_users(search_term, skip, limit)
            finally:
                await async_session.close()
                await isolated_engine.dispose()

        return asyncio.run(_search_users())

    def count_active_users(self) -> int:
        """Count active users synchronously."""

        async def _count_active_users():
            async_session, isolated_engine = await self._create_async_session()
            try:
                user_repo = UserRepository(async_session)
                return await user_repo.count_active_users()
            finally:
                await async_session.close()
                await isolated_engine.dispose()

        return asyncio.run(_count_active_users())

    def close(self):
        """Close method for compatibility. No persistent session to close."""
        pass

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close()
