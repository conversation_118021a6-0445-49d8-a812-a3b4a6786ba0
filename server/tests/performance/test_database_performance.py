"""Database Performance and Stress Testing.

This module contains performance tests for database operations to ensure
the system can handle expected loads and maintains data integrity under stress.
"""

import pytest
import uuid
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

from src.core.errors.exceptions import DuplicateEntryError
from src.core.models.general.user import User
from src.core.models.general.project import Project, ProjectMember
from src.core.models.general.user_role import UserRole
from src.core.repositories.general.user_repository import UserRepository
from src.core.repositories.general.project_repository import ProjectRepository
from src.core.services.general.project_service import ProjectService
from tests.performance.sync_repository_adapter import SyncUserRepositoryAdapter
from src.core.schemas.general.project_schemas import ProjectCreateSchema
from src.core.enums import ProjectStatus

pytestmark = [pytest.mark.performance]


class TestDatabasePerformance:
    """Test database operation performance."""

    @pytest.mark.slow
    def test_bulk_user_creation_performance(self, db_session: Session):
        """Test performance of bulk user creation."""
        user_repo = SyncUserRepositoryAdapter(db_session)
        unique_suffix = str(uuid.uuid4())[:8]

        # Create 100 users and measure time
        start_time = time.time()
        users_created = 0

        for i in range(100):
            try:
                user_data = {
                    "email": f"bulk.user.{i}.{unique_suffix}@example.com",
                    "password_hash": f"password{i}",
                    "name": f"Bulk User {i} {unique_suffix}",
                }
                user_repo.create(user_data)
                users_created += 1

                # Commit every 10 users to test transaction performance
                if i % 10 == 9:
                    db_session.commit()

            except (IntegrityError, DuplicateEntryError):
                db_session.rollback()
                continue

        # Final commit
        try:
            db_session.commit()
        except (IntegrityError, DuplicateEntryError):
            db_session.rollback()

        end_time = time.time()
        duration = end_time - start_time

        # Performance assertions
        assert users_created > 90  # At least 90% should succeed
        assert duration < 30.0  # Should complete within 30 seconds

        # Calculate performance metrics
        users_per_second = users_created / duration
        assert users_per_second > 3.0  # Should create at least 3 users per second

    @pytest.mark.slow
    def test_bulk_project_creation_performance(self, db_session: Session):
        """Test performance of bulk project creation through service layer."""
        project_repo = ProjectRepository(db_session)
        project_service = ProjectService(project_repo)
        unique_suffix = str(uuid.uuid4())[:8]

        start_time = time.time()
        projects_created = 0

        for i in range(50):  # Fewer projects as they're more complex
            try:
                project_data = ProjectCreateSchema(
                    name=f"Bulk Project {i} {unique_suffix}",
                    description=f"Performance test project {i}",
                    status=ProjectStatus.DRAFT,
                    client=f"Client {i}",
                    location=f"Location {i}",
                )
                project_service.create_project(project_data)
                projects_created += 1

            except (IntegrityError, DuplicateEntryError):
                db_session.rollback()
                continue

        end_time = time.time()
        duration = end_time - start_time

        # Performance assertions
        assert projects_created > 45  # At least 90% should succeed
        assert duration < 60.0  # Should complete within 60 seconds

        # Calculate performance metrics
        projects_per_second = projects_created / duration
        assert projects_per_second > 0.8  # Should create at least 0.8 projects per second

    def test_query_performance_with_large_dataset(self, db_session: Session):
        """Test query performance with a larger dataset."""
        user_repo = SyncUserRepositoryAdapter(db_session)
        unique_suffix = str(uuid.uuid4())[:8]

        # Create a moderate dataset
        users_to_create = 200
        created_users = []

        for i in range(users_to_create):
            try:
                user_data = {
                    "email": f"query.test.{i}.{unique_suffix}@example.com",
                    "password_hash": f"password{i}",
                    "name": f"Query Test User {i} {unique_suffix}",
                }
                user = user_repo.create(user_data)
                created_users.append(user)

                if i % 20 == 19:  # Commit every 20 users
                    db_session.commit()

            except (IntegrityError, DuplicateEntryError):
                db_session.rollback()
                continue

        # Final commit
        try:
            db_session.commit()
        except (IntegrityError, DuplicateEntryError):
            db_session.rollback()

        # Test query performance
        start_time = time.time()

        # Perform various queries
        for i in range(10):  # Test multiple queries
            # Test email lookup (should be fast due to unique index)
            if created_users:
                test_user = created_users[i % len(created_users)]
                found_user = user_repo.get_by_email(test_user.email)
                assert found_user is not None

        end_time = time.time()
        query_duration = end_time - start_time

        # Queries should be fast
        assert query_duration < 5.0  # All queries within 5 seconds
        avg_query_time = query_duration / 10
        assert avg_query_time < 0.5  # Average query time under 0.5 seconds


class TestConcurrencyStress:
    """Test database behavior under concurrent access."""

    @pytest.mark.slow
    def test_concurrent_user_creation(self, db_session: Session):
        """Test concurrent user creation to simulate real-world load."""
        unique_suffix = str(uuid.uuid4())[:8]

        def create_user_batch(batch_id):
            """Create a batch of users in a separate thread."""
            user_repo = SyncUserRepositoryAdapter(db_session)
            created_count = 0

            for i in range(10):
                try:
                    user_data = {
                        "email": f"concurrent.{batch_id}.{i}.{unique_suffix}@example.com",
                        "password_hash": f"password{batch_id}{i}",
                        "name": f"Concurrent User {batch_id}-{i} {unique_suffix}",
                    }
                    user_repo.create(user_data)
                    created_count += 1

                except (IntegrityError, DuplicateEntryError):
                    db_session.rollback()
                    continue

            try:
                db_session.commit()
            except (IntegrityError, DuplicateEntryError):
                db_session.rollback()

            return created_count

        # Simulate concurrent access with multiple threads
        start_time = time.time()
        total_created = 0

        with ThreadPoolExecutor(max_workers=5) as executor:
            # Submit 5 batches of work
            futures = [executor.submit(create_user_batch, i) for i in range(5)]

            for future in as_completed(futures):
                try:
                    created_count = future.result(timeout=30)
                    total_created += created_count
                except Exception as e:
                    # Log the exception but continue
                    print(f"Batch failed: {e}")

        end_time = time.time()
        duration = end_time - start_time

        # Performance assertions
        assert total_created > 40  # At least 80% should succeed
        assert duration < 45.0  # Should complete within 45 seconds

    def test_duplicate_email_race_condition(self, db_session: Session):
        """Test handling of duplicate email race conditions."""
        unique_suffix = str(uuid.uuid4())[:8]
        base_email = f"race.condition.{unique_suffix}@example.com"

        def try_create_user_with_same_email(user_id):
            """Attempt to create user with the same email."""
            user_repo = SyncUserRepositoryAdapter(db_session)
            try:
                user_data = {
                    "email": base_email,  # Same email for all attempts
                    "password_hash": f"password{user_id}",
                    "name": f"Race User {user_id} {unique_suffix}",
                }
                user = user_repo.create(user_data)
                db_session.commit()
                return user.id
            except (IntegrityError, DuplicateEntryError):
                db_session.rollback()
                return None

        # Simulate race condition with multiple threads
        successful_creations = 0

        with ThreadPoolExecutor(max_workers=3) as executor:
            futures = [executor.submit(try_create_user_with_same_email, i) for i in range(3)]

            for future in as_completed(futures):
                try:
                    result = future.result(timeout=10)
                    if result is not None:
                        successful_creations += 1
                except Exception:
                    continue

        # Only one creation should succeed due to unique constraint
        assert successful_creations == 1


class TestMemoryAndResourceUsage:
    """Test memory usage and resource management."""

    @pytest.mark.slow
    def test_memory_usage_during_bulk_operations(self, db_session: Session):
        """Test memory usage during bulk database operations."""
        import psutil
        import os

        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        user_repo = SyncUserRepositoryAdapter(db_session)
        unique_suffix = str(uuid.uuid4())[:8]

        # Perform bulk operations
        for i in range(500):  # Create many users
            try:
                user_data = {
                    "email": f"memory.test.{i}.{unique_suffix}@example.com",
                    "password_hash": f"password{i}",
                    "name": f"Memory Test User {i} {unique_suffix}",
                }
                user_repo.create(user_data)

                # Commit periodically to manage transaction size
                if i % 50 == 49:
                    db_session.commit()

                    # Check memory usage periodically
                    current_memory = process.memory_info().rss / 1024 / 1024  # MB
                    memory_increase = current_memory - initial_memory

                    # Memory usage shouldn't grow excessively
                    assert memory_increase < 100  # Less than 100MB increase

            except (IntegrityError, DuplicateEntryError):
                db_session.rollback()
                continue

        # Final commit
        try:
            db_session.commit()
        except (IntegrityError, DuplicateEntryError):
            db_session.rollback()

        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        total_memory_increase = final_memory - initial_memory

        # Total memory increase should be reasonable
        assert total_memory_increase < 150  # Less than 150MB total increase

    def test_session_cleanup_after_errors(self, db_session: Session):
        """Test that database sessions are properly cleaned up after errors."""
        user_repo = SyncUserRepositoryAdapter(db_session)
        unique_suffix = str(uuid.uuid4())[:8]

        # Create a user successfully first
        valid_user_data = {
            "email": f"valid.session.{unique_suffix}@example.com",
            "password_hash": "password123",
            "name": f"Valid Session User {unique_suffix}",
        }
        valid_user = user_repo.create(valid_user_data)
        db_session.commit()

        # Now try to create multiple users with the same email (will fail)
        for i in range(10):
            try:
                duplicate_user_data = {
                    "email": valid_user.email,  # Duplicate email
                    "password_hash": f"password{i}",
                    "name": f"Duplicate User {i} {unique_suffix}",
                }
                user_repo.create(duplicate_user_data)
                db_session.commit()
            except (IntegrityError, DuplicateEntryError):
                db_session.rollback()
                continue

        # After all the errors, the session should still be usable
        final_user_data = {
            "email": f"final.session.{unique_suffix}@example.com",
            "password_hash": "password123",
            "name": f"Final Session User {unique_suffix}",
        }
        final_user = user_repo.create(final_user_data)
        db_session.commit()

        # Verify the final user was created successfully
        assert final_user.id is not None
        assert final_user.email == final_user_data["email"]


class TestIndexPerformance:
    """Test database index performance."""

    @pytest.mark.slow
    def test_email_index_performance(self, db_session: Session):
        """Test email lookup performance (should use index)."""
        user_repo = SyncUserRepositoryAdapter(db_session)
        unique_suffix = str(uuid.uuid4())[:8]

        # Create users with known emails
        test_emails = []
        for i in range(100):
            email = f"index.test.{i}.{unique_suffix}@example.com"
            test_emails.append(email)

            try:
                user_data = {
                    "email": email,
                    "password_hash": f"password{i}",
                    "name": f"Index Test User {i} {unique_suffix}",
                }
                user_repo.create(user_data)

                if i % 20 == 19:
                    db_session.commit()

            except (IntegrityError, DuplicateEntryError):
                db_session.rollback()
                continue

        try:
            db_session.commit()
        except (IntegrityError, DuplicateEntryError):
            db_session.rollback()

        # Test email lookup performance
        start_time = time.time()

        for email in test_emails[:20]:  # Test first 20 emails
            user = user_repo.get_by_email(email)
            assert user is not None
            assert user.email == email

        end_time = time.time()
        lookup_duration = end_time - start_time

        # Email lookups should be very fast (indexed)
        assert lookup_duration < 2.0  # All lookups within 2 seconds
        avg_lookup_time = lookup_duration / 20
        assert avg_lookup_time < 0.1  # Average lookup under 0.1 seconds
