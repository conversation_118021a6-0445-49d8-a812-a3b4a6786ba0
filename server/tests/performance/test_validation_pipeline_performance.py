"""Validation Pipeline Performance Tests.

This module provides comprehensive performance testing for the entire validation
pipeline, measuring end-to-end validation performance and identifying bottlenecks
in the multi-layer validation architecture.

Pipeline testing focus:
1. End-to-end validation pipeline performance measurement
2. Individual validation step latency analysis
3. Validation caching effectiveness testing
4. Schema validation vs service validation performance comparison
5. Validation error processing performance under load
"""

import pytest
import time
import uuid
import random
import asyncio
import statistics
from typing import List, Dict, Tuple, Any, Optional
from concurrent.futures import ThreadPoolExecutor
from sqlalchemy.orm import Session
from pydantic import ValidationError
import psutil
import threading
from collections import defaultdict

from src.core.models.general.user import User
from src.core.models.general.project import Project
from src.core.repositories.general.user_repository import UserRepository
from src.core.repositories.general.project_repository import ProjectRepository
from tests.performance.sync_repository_adapter import SyncUserRepositoryAdapter
from src.core.services.general.user_service import UserService
from src.core.services.general.project_service import ProjectService
from src.core.schemas.general.user_schemas import UserCreateSchema, UserUpdateSchema
from src.core.schemas.general.project_schemas import (
    ProjectCreateSchema,
    ProjectUpdateSchema,
)
from src.core.errors.exceptions import (
    InvalidInputError,
    DuplicateEntryError,
    DataValidationError,
    ServiceError,
)
from src.core.enums import ProjectStatus

pytestmark = [pytest.mark.performance]


class ValidationPipelineProfiler:
    """Profile validation pipeline performance with detailed metrics."""

    def __init__(self):
        self.stage_times: Dict[str, List[float]] = defaultdict(list)
        self.error_processing_times: List[float] = []
        self.success_processing_times: List[float] = []
        self.memory_samples: List[float] = []
        self.lock = threading.Lock()

    def record_stage_time(self, stage: str, duration_ms: float):
        """Record timing for a specific validation stage."""
        with self.lock:
            self.stage_times[stage].append(duration_ms)

    def record_error_processing_time(self, duration_ms: float):
        """Record time taken to process validation errors."""
        with self.lock:
            self.error_processing_times.append(duration_ms)

    def record_success_processing_time(self, duration_ms: float):
        """Record time taken to process successful validations."""
        with self.lock:
            self.success_processing_times.append(duration_ms)

    def record_memory_sample(self, memory_mb: float):
        """Record memory usage sample."""
        with self.lock:
            self.memory_samples.append(memory_mb)

    def get_performance_summary(self) -> Dict[str, Any]:
        """Generate comprehensive performance summary."""
        with self.lock:
            summary = {
                "stage_performance": {},
                "error_processing": {
                    "count": len(self.error_processing_times),
                    "avg_time_ms": statistics.mean(self.error_processing_times) if self.error_processing_times else 0,
                    "max_time_ms": max(self.error_processing_times) if self.error_processing_times else 0,
                },
                "success_processing": {
                    "count": len(self.success_processing_times),
                    "avg_time_ms": statistics.mean(self.success_processing_times)
                    if self.success_processing_times
                    else 0,
                    "max_time_ms": max(self.success_processing_times) if self.success_processing_times else 0,
                },
                "memory_usage": {
                    "samples": len(self.memory_samples),
                    "avg_mb": statistics.mean(self.memory_samples) if self.memory_samples else 0,
                    "peak_mb": max(self.memory_samples) if self.memory_samples else 0,
                },
            }

            for stage, times in self.stage_times.items():
                if times:
                    summary["stage_performance"][stage] = {
                        "count": len(times),
                        "avg_time_ms": statistics.mean(times),
                        "min_time_ms": min(times),
                        "max_time_ms": max(times),
                        "p95_time_ms": times[int(len(times) * 0.95)] if len(times) > 20 else max(times),
                        "std_dev_ms": statistics.stdev(times) if len(times) > 1 else 0,
                    }

            return summary


class TestValidationPipelinePerformance:
    """Comprehensive validation pipeline performance testing."""

    @pytest.fixture(autouse=True)
    def setup_profiler(self):
        """Set up profiler for validation pipeline performance testing."""
        self.profiler = ValidationPipelineProfiler()
        self.start_memory = psutil.Process().memory_info().rss / (1024 * 1024)
        yield

    def measure_validation_stage(self, stage_name: str, operation_func, *args, **kwargs):
        """Measure performance of a specific validation stage."""
        start_time = time.perf_counter()

        try:
            result = operation_func(*args, **kwargs)
            end_time = time.perf_counter()
            duration = (end_time - start_time) * 1000

            self.profiler.record_stage_time(stage_name, duration)
            self.profiler.record_success_processing_time(duration)

            return result, None

        except Exception as e:
            end_time = time.perf_counter()
            duration = (end_time - start_time) * 1000

            self.profiler.record_stage_time(f"{stage_name}_error", duration)
            self.profiler.record_error_processing_time(duration)

            return None, e

    def test_end_to_end_user_validation_pipeline_performance(self, db_session: Session):
        """Test complete user validation pipeline performance end-to-end.

        This test measures the entire validation pipeline from schema validation
        through service validation to database operations.
        """
        unique_suffix = str(uuid.uuid4())[:8]
        from src.core.repositories.general.user_repository import UserRepository
        from src.core.repositories.general.user_preference_repository import UserPreferenceRepository

        user_repo = SyncUserRepositoryAdapter(db_session)
        preference_repo = UserPreferenceRepository(db_session)
        user_service = UserService(user_repository=user_repo, preference_repository=preference_repo)

        print(f"\n🔄 Testing end-to-end user validation pipeline performance")

        # Test scenarios with different validation complexity
        test_scenarios = [
            {
                "name": "valid_user",
                "data": {
                    "name": f"Valid Pipeline User {unique_suffix}",
                    "email": f"valid.pipeline.{unique_suffix}@test.com",
                    "password": "ValidPipeline123!",
                },
                "expected_success": True,
            },
            {
                "name": "duplicate_email",
                "data": {
                    "name": f"Duplicate User {unique_suffix}",
                    "email": f"valid.pipeline.{unique_suffix}@test.com",  # Reuse email
                    "password": "ValidPipeline123!",
                },
                "expected_success": False,
            },
            {
                "name": "invalid_email_format",
                "data": {
                    "name": f"Invalid Email User {unique_suffix}",
                    "email": "invalid-email-format",
                    "password": "ValidPipeline123!",
                },
                "expected_success": False,
            },
            {
                "name": "short_name",
                "data": {
                    "name": "AB",  # Too short
                    "email": f"short.name.{unique_suffix}@test.com",
                    "password": "ValidPipeline123!",
                },
                "expected_success": False,
            },
            {
                "name": "case_insensitive_duplicate",
                "data": {
                    "name": f"Case Insensitive User {unique_suffix}",
                    "email": f"VALID.PIPELINE.{unique_suffix.upper()}@TEST.COM",  # Case variation
                    "password": "ValidPipeline123!",
                },
                "expected_success": False,
            },
        ]

        pipeline_results = []

        for scenario in test_scenarios:
            print(f"   Testing scenario: {scenario['name']}")

            # Stage 1: Schema validation
            schema_result, schema_error = self.measure_validation_stage(
                "schema_validation", lambda: UserCreateSchema(**scenario["data"])
            )

            if schema_error:
                print(f"      Schema validation failed: {type(schema_error).__name__}")
                pipeline_results.append(
                    {
                        "scenario": scenario["name"],
                        "success": False,
                        "stage_failed": "schema_validation",
                        "error_type": type(schema_error).__name__,
                    }
                )
                continue

            # Stage 2: Service validation (business rules)
            service_result, service_error = self.measure_validation_stage(
                "service_validation", user_service.create_user, schema_result
            )

            success = service_error is None
            stage_failed = "service_validation" if service_error else None
            error_type = type(service_error).__name__ if service_error else None

            pipeline_results.append(
                {
                    "scenario": scenario["name"],
                    "success": success,
                    "stage_failed": stage_failed,
                    "error_type": error_type,
                    "expected_success": scenario["expected_success"],
                }
            )

            if success:
                print(f"      ✅ Pipeline completed successfully")
            else:
                print(f"      ❌ Pipeline failed at {stage_failed}: {error_type}")

        # Analyze pipeline performance
        performance_summary = self.profiler.get_performance_summary()

        print(f"\n📊 Validation Pipeline Performance Summary:")
        for stage, metrics in performance_summary["stage_performance"].items():
            print(f"   {stage}:")
            print(f"      Count: {metrics['count']}")
            print(f"      Avg: {metrics['avg_time_ms']:.2f}ms")
            print(f"      Max: {metrics['max_time_ms']:.2f}ms")
            print(f"      P95: {metrics['p95_time_ms']:.2f}ms")

        print(f"\n📈 Processing Performance:")
        print(f"   Success processing: {performance_summary['success_processing']['avg_time_ms']:.2f}ms avg")
        print(f"   Error processing: {performance_summary['error_processing']['avg_time_ms']:.2f}ms avg")

        # Validate results
        correct_outcomes = sum(1 for result in pipeline_results if result["success"] == result["expected_success"])
        accuracy = (correct_outcomes / len(pipeline_results)) * 100

        print(f"   Pipeline accuracy: {accuracy:.1f}%")

        # Performance assertions
        schema_avg = performance_summary["stage_performance"].get("schema_validation", {}).get("avg_time_ms", 0)
        service_avg = performance_summary["stage_performance"].get("service_validation", {}).get("avg_time_ms", 0)

        assert schema_avg < 50.0, f"Schema validation should be <50ms avg, got {schema_avg:.2f}ms"
        assert service_avg < 200.0, f"Service validation should be <200ms avg, got {service_avg:.2f}ms"
        assert accuracy >= 100.0, f"Pipeline accuracy should be 100%, got {accuracy:.1f}%"

    def test_individual_validation_step_latency_analysis(self, db_session: Session):
        """Analyze latency of individual validation steps to identify bottlenecks.

        This test breaks down validation into granular steps to measure
        the performance contribution of each validation component.
        """
        unique_suffix = str(uuid.uuid4())[:8]
        from src.core.repositories.general.user_repository import UserRepository
        from src.core.repositories.general.user_preference_repository import UserPreferenceRepository

        user_repo = SyncUserRepositoryAdapter(db_session)
        preference_repo = UserPreferenceRepository(db_session)
        user_service = UserService(user_repository=user_repo, preference_repository=preference_repo)

        print(f"\n🔍 Analyzing individual validation step latencies")

        # Create baseline data for uniqueness validation testing
        baseline_users = []
        for i in range(100):
            baseline_email = f"baseline.{i}.{unique_suffix}@validation.com"
            baseline_user_data = {
                "name": f"Baseline User {i}",
                "email": baseline_email,
                "password_hash": f"hash_{i}",
            }
            user = user_repo.create(baseline_user_data)
            baseline_users.append(user)

        db_session.commit()
        print(f"   Created {len(baseline_users)} baseline users for testing")

        # Test individual validation steps
        test_iterations = 100

        # Step 1: Pydantic field validation
        field_validation_times = []
        print(f"   Testing Pydantic field validation ({test_iterations} iterations)...")

        for i in range(test_iterations):
            test_data = {
                "name": f"Field Test User {i} {unique_suffix}",
                "email": f"field.test.{i}.{unique_suffix}@validation.com",
                "password": "FieldTest123!",
            }

            start_time = time.perf_counter()
            try:
                schema = UserCreateSchema(**test_data)
                end_time = time.perf_counter()
                field_validation_times.append((end_time - start_time) * 1000)
            except ValidationError:
                end_time = time.perf_counter()
                field_validation_times.append((end_time - start_time) * 1000)

        # Step 2: Email normalization
        email_normalization_times = []
        print(f"   Testing email normalization ({test_iterations} iterations)...")

        test_emails = [f"NORMALIZE.TEST.{i}.{unique_suffix}@VALIDATION.COM" for i in range(test_iterations)]

        for email in test_emails:
            start_time = time.perf_counter()
            normalized = email.lower().strip()
            end_time = time.perf_counter()
            email_normalization_times.append((end_time - start_time) * 1000)

        # Step 3: Email uniqueness checking
        uniqueness_check_times = []
        print(f"   Testing email uniqueness checking ({test_iterations} iterations)...")

        for i in range(test_iterations):
            if i % 2 == 0:
                # Test existing email
                test_email = random.choice(baseline_users).email
            else:
                # Test non-existing email
                test_email = f"nonexistent.{i}.{unique_suffix}@validation.com"

            start_time = time.perf_counter()
            exists = user_repo.check_email_exists(test_email)
            end_time = time.perf_counter()
            uniqueness_check_times.append((end_time - start_time) * 1000)

        # Step 4: Password validation and hashing
        password_processing_times = []
        print(f"   Testing password validation and hashing ({test_iterations} iterations)...")

        for i in range(test_iterations):
            test_password = f"TestPassword{i}!"

            start_time = time.perf_counter()
            # Simulate password validation (length, complexity checks)
            is_valid = len(test_password) >= 8 and any(c.isupper() for c in test_password)
            if is_valid:
                # Simulate hashing (simplified)
                import hashlib

                hashed = hashlib.sha256(test_password.encode()).hexdigest()
            end_time = time.perf_counter()
            password_processing_times.append((end_time - start_time) * 1000)

        # Step 5: Database constraint validation
        db_constraint_times = []
        print(f"   Testing database constraint validation...")

        for i in range(50):  # Fewer iterations for DB operations
            unique_email = f"db.constraint.{i}.{unique_suffix}@validation.com"
            user_data = {
                "name": f"DB Constraint User {i}",
                "email": unique_email,
                "password_hash": f"hash_{i}",
            }

            start_time = time.perf_counter()
            try:
                user = user_repo.create(user_data)
                db_session.commit()
                end_time = time.perf_counter()
                db_constraint_times.append((end_time - start_time) * 1000)
            except Exception:
                db_session.rollback()
                end_time = time.perf_counter()
                db_constraint_times.append((end_time - start_time) * 1000)

        # Analyze step performance
        step_analysis = {
            "field_validation": {
                "times": field_validation_times,
                "description": "Pydantic field validation",
            },
            "email_normalization": {
                "times": email_normalization_times,
                "description": "Email normalization",
            },
            "uniqueness_checking": {
                "times": uniqueness_check_times,
                "description": "Email uniqueness checking",
            },
            "password_processing": {
                "times": password_processing_times,
                "description": "Password validation and hashing",
            },
            "db_constraints": {
                "times": db_constraint_times,
                "description": "Database constraint validation",
            },
        }

        print(f"\n📊 Individual Validation Step Performance Analysis:")

        total_pipeline_time = 0
        for step_name, step_data in step_analysis.items():
            times = step_data["times"]
            avg_time = statistics.mean(times)
            max_time = max(times)
            min_time = min(times)
            p95_time = times[int(len(times) * 0.95)] if len(times) > 20 else max_time

            print(f"   {step_data['description']}:")
            print(f"      Avg: {avg_time:.4f}ms")
            print(f"      Min: {min_time:.4f}ms")
            print(f"      Max: {max_time:.4f}ms")
            print(f"      P95: {p95_time:.4f}ms")

            total_pipeline_time += avg_time

            # Record in profiler
            for time_val in times:
                self.profiler.record_stage_time(step_name, time_val)

        print(f"\n🔄 Estimated total pipeline time: {total_pipeline_time:.2f}ms")

        # Performance assertions for each step
        field_avg = statistics.mean(field_validation_times)
        normalization_avg = statistics.mean(email_normalization_times)
        uniqueness_avg = statistics.mean(uniqueness_check_times)
        password_avg = statistics.mean(password_processing_times)
        db_avg = statistics.mean(db_constraint_times)

        assert field_avg < 10.0, f"Field validation should be <10ms, got {field_avg:.4f}ms"
        assert normalization_avg < 1.0, f"Email normalization should be <1ms, got {normalization_avg:.4f}ms"
        assert uniqueness_avg < 100.0, f"Uniqueness checking should be <100ms, got {uniqueness_avg:.2f}ms"
        assert password_avg < 50.0, f"Password processing should be <50ms, got {password_avg:.4f}ms"
        assert db_avg < 200.0, f"DB constraint validation should be <200ms, got {db_avg:.2f}ms"

        # Total pipeline should be reasonable
        assert total_pipeline_time < 300.0, f"Total pipeline time should be <300ms, got {total_pipeline_time:.2f}ms"

    def test_validation_caching_effectiveness(self, db_session: Session):
        """Test effectiveness of validation caching mechanisms.

        This test measures performance improvements from caching validation
        results and identifies opportunities for further optimization.
        """
        unique_suffix = str(uuid.uuid4())[:8]
        user_repo = SyncUserRepositoryAdapter(db_session)

        print(f"\n🗄️ Testing validation caching effectiveness")

        # Create test data for cache testing
        test_emails = [f"cache.test.{i}.{unique_suffix}@validation.com" for i in range(200)]

        # Create some existing users for cache hit testing
        existing_emails = test_emails[:100]
        for i, email in enumerate(existing_emails):
            user_data = {
                "name": f"Cache Test User {i}",
                "email": email,
                "password_hash": f"hash_{i}",
            }
            user_repo.create(user_data)

        db_session.commit()
        print(f"   Created {len(existing_emails)} users for cache testing")

        # Test 1: Cold cache performance (first-time lookups)
        cold_cache_times = []
        print(f"   Testing cold cache performance...")

        for email in test_emails[:50]:  # First 50 emails
            start_time = time.perf_counter()
            exists = user_repo.check_email_exists(email)
            end_time = time.perf_counter()
            cold_cache_times.append((end_time - start_time) * 1000)

        # Test 2: Warm cache performance (repeated lookups)
        warm_cache_times = []
        print(f"   Testing warm cache performance (repeated lookups)...")

        # Repeat the same lookups to simulate cache hits
        for email in test_emails[:50]:  # Same emails as cold cache test
            start_time = time.perf_counter()
            exists = user_repo.check_email_exists(email)
            end_time = time.perf_counter()
            warm_cache_times.append((end_time - start_time) * 1000)

        # Test 3: Mixed cache performance (random access pattern)
        mixed_cache_times = []
        print(f"   Testing mixed cache performance...")

        # Random access pattern with repeated emails
        for _ in range(100):
            email = random.choice(test_emails[:50])  # Reuse from previous tests
            start_time = time.perf_counter()
            exists = user_repo.check_email_exists(email)
            end_time = time.perf_counter()
            mixed_cache_times.append((end_time - start_time) * 1000)

        # Test 4: Cache miss performance (new emails)
        cache_miss_times = []
        print(f"   Testing cache miss performance...")

        for i in range(50):
            new_email = f"cache.miss.{i}.{unique_suffix}@validation.com"
            start_time = time.perf_counter()
            exists = user_repo.check_email_exists(new_email)
            end_time = time.perf_counter()
            cache_miss_times.append((end_time - start_time) * 1000)

        # Analyze caching performance
        cold_avg = statistics.mean(cold_cache_times)
        warm_avg = statistics.mean(warm_cache_times)
        mixed_avg = statistics.mean(mixed_cache_times)
        miss_avg = statistics.mean(cache_miss_times)

        print(f"\n📊 Cache Performance Analysis:")
        print(f"   Cold cache (first lookup): {cold_avg:.2f}ms avg")
        print(f"   Warm cache (repeated): {warm_avg:.2f}ms avg")
        print(f"   Mixed access pattern: {mixed_avg:.2f}ms avg")
        print(f"   Cache miss (new emails): {miss_avg:.2f}ms avg")

        # Calculate cache effectiveness
        cache_improvement = ((cold_avg - warm_avg) / cold_avg) * 100 if cold_avg > 0 else 0
        print(f"   Cache improvement: {cache_improvement:.1f}%")

        # Record in profiler
        cache_types = {
            "cold_cache": cold_cache_times,
            "warm_cache": warm_cache_times,
            "mixed_cache": mixed_cache_times,
            "cache_miss": cache_miss_times,
        }

        for cache_type, times in cache_types.items():
            for time_val in times:
                self.profiler.record_stage_time(cache_type, time_val)

        # Performance assertions
        assert cold_avg < 150.0, f"Cold cache lookups should be <150ms, got {cold_avg:.2f}ms"
        assert warm_avg < 100.0, f"Warm cache lookups should be <100ms, got {warm_avg:.2f}ms"
        assert mixed_avg < 120.0, f"Mixed cache access should be <120ms, got {mixed_avg:.2f}ms"
        assert miss_avg < 150.0, f"Cache miss lookups should be <150ms, got {miss_avg:.2f}ms"

        # Cache should provide some improvement (even if minimal)
        assert warm_avg <= cold_avg, f"Warm cache should not be slower than cold cache"

    def test_schema_vs_service_validation_performance_comparison(self, db_session: Session):
        """Compare performance between schema validation and service validation.

        This test helps understand the performance trade-offs between different
        validation layers and identifies optimization opportunities.
        """
        unique_suffix = str(uuid.uuid4())[:8]
        from src.core.repositories.general.user_repository import UserRepository
        from src.core.repositories.general.user_preference_repository import UserPreferenceRepository

        user_repo = SyncUserRepositoryAdapter(db_session)
        preference_repo = UserPreferenceRepository(db_session)
        user_service = UserService(user_repository=user_repo, preference_repository=preference_repo)

        print(f"\n⚖️ Comparing schema vs service validation performance")

        # Test data for different validation scenarios
        validation_test_cases = [
            {
                "name": "valid_data",
                "data": {
                    "name": f"Valid User {unique_suffix}",
                    "email": f"valid.{unique_suffix}@test.com",
                    "password": "ValidPass123!",
                },
                "should_pass_schema": True,
                "should_pass_service": True,
            },
            {
                "name": "invalid_email",
                "data": {
                    "name": f"Invalid Email User {unique_suffix}",
                    "email": "invalid-email",
                    "password": "ValidPass123!",
                },
                "should_pass_schema": False,
                "should_pass_service": False,
            },
            {
                "name": "short_name",
                "data": {
                    "name": "AB",
                    "email": f"short.{unique_suffix}@test.com",
                    "password": "ValidPass123!",
                },
                "should_pass_schema": False,
                "should_pass_service": False,
            },
            {
                "name": "long_name",
                "data": {
                    "name": "A" * 51,  # Too long
                    "email": f"long.{unique_suffix}@test.com",
                    "password": "ValidPass123!",
                },
                "should_pass_schema": False,
                "should_pass_service": False,
            },
            {
                "name": "weak_password",
                "data": {
                    "name": f"Weak Password User {unique_suffix}",
                    "email": f"weak.{unique_suffix}@test.com",
                    "password": "123",
                },
                "should_pass_schema": False,
                "should_pass_service": False,
            },
        ]

        schema_validation_results = []
        service_validation_results = []

        # Test schema validation performance
        print(f"   Testing schema validation performance...")
        iterations_per_case = 50

        for test_case in validation_test_cases:
            case_schema_times = []

            for _ in range(iterations_per_case):
                start_time = time.perf_counter()

                try:
                    schema = UserCreateSchema(**test_case["data"])
                    end_time = time.perf_counter()
                    duration = (end_time - start_time) * 1000
                    case_schema_times.append(duration)

                    success = True

                except ValidationError as e:
                    end_time = time.perf_counter()
                    duration = (end_time - start_time) * 1000
                    case_schema_times.append(duration)

                    success = False

            schema_validation_results.append(
                {
                    "case": test_case["name"],
                    "times": case_schema_times,
                    "avg_time": statistics.mean(case_schema_times),
                    "expected_success": test_case["should_pass_schema"],
                }
            )

        # Test service validation performance
        print(f"   Testing service validation performance...")

        for test_case in validation_test_cases:
            case_service_times = []

            # Modify email to avoid duplicates in service testing
            modified_data = test_case["data"].copy()

            for i in range(iterations_per_case):
                # Make email unique for each iteration
                if "email" in modified_data:
                    base_email = test_case["data"]["email"]
                    if "@" in base_email:
                        local, domain = base_email.split("@", 1)
                        modified_data["email"] = f"{local}.{i}@{domain}"

                start_time = time.perf_counter()

                try:
                    schema = UserCreateSchema(**modified_data)
                    user = user_service.create_user(schema)
                    end_time = time.perf_counter()
                    duration = (end_time - start_time) * 1000
                    case_service_times.append(duration)

                    success = True

                except (
                    ValidationError,
                    InvalidInputError,
                    DuplicateEntryError,
                    DataValidationError,
                ) as e:
                    end_time = time.perf_counter()
                    duration = (end_time - start_time) * 1000
                    case_service_times.append(duration)

                    success = False

            service_validation_results.append(
                {
                    "case": test_case["name"],
                    "times": case_service_times,
                    "avg_time": statistics.mean(case_service_times),
                    "expected_success": test_case["should_pass_service"],
                }
            )

        # Analyze performance comparison
        print(f"\n📊 Schema vs Service Validation Performance Comparison:")

        total_schema_time = 0
        total_service_time = 0

        for i, test_case in enumerate(validation_test_cases):
            schema_result = schema_validation_results[i]
            service_result = service_validation_results[i]

            schema_avg = schema_result["avg_time"]
            service_avg = service_result["avg_time"]
            overhead = ((service_avg - schema_avg) / schema_avg) * 100 if schema_avg > 0 else 0

            print(f"   {test_case['name']}:")
            print(f"      Schema validation: {schema_avg:.3f}ms")
            print(f"      Service validation: {service_avg:.2f}ms")
            print(f"      Service overhead: {overhead:.1f}%")

            total_schema_time += schema_avg
            total_service_time += service_avg

            # Record in profiler
            for time_val in schema_result["times"]:
                self.profiler.record_stage_time(f"schema_{test_case['name']}", time_val)
            for time_val in service_result["times"]:
                self.profiler.record_stage_time(f"service_{test_case['name']}", time_val)

        overall_overhead = ((total_service_time - total_schema_time) / total_schema_time) * 100
        print(f"\n🔄 Overall Performance Summary:")
        print(f"   Total schema validation time: {total_schema_time:.2f}ms")
        print(f"   Total service validation time: {total_service_time:.2f}ms")
        print(f"   Overall service overhead: {overall_overhead:.1f}%")

        # Performance assertions
        assert total_schema_time < 50.0, f"Total schema validation should be <50ms, got {total_schema_time:.2f}ms"
        assert total_service_time < 500.0, f"Total service validation should be <500ms, got {total_service_time:.2f}ms"
        assert overall_overhead < 2000.0, f"Service overhead should be reasonable, got {overall_overhead:.1f}%"

        # Individual case assertions
        for result in schema_validation_results:
            assert result["avg_time"] < 10.0, (
                f"Schema validation for {result['case']} should be <10ms, got {result['avg_time']:.3f}ms"
            )

        for result in service_validation_results:
            assert result["avg_time"] < 300.0, (
                f"Service validation for {result['case']} should be <300ms, got {result['avg_time']:.2f}ms"
            )

    def test_validation_error_processing_performance_under_load(self, db_session: Session):
        """Test validation error processing performance under concurrent load.

        This test ensures that error handling and processing doesn't become
        a bottleneck during high validation failure rates.
        """
        unique_suffix = str(uuid.uuid4())[:8]
        from src.core.repositories.general.user_repository import UserRepository
        from src.core.repositories.general.user_preference_repository import UserPreferenceRepository

        user_repo = SyncUserRepositoryAdapter(db_session)
        preference_repo = UserPreferenceRepository(db_session)
        user_service = UserService(user_repository=user_repo, preference_repository=preference_repo)

        print(f"\n🚨 Testing validation error processing performance under load")

        # Define error scenarios that will trigger different validation failures
        error_scenarios = [
            {
                "name": "schema_validation_errors",
                "generator": lambda i: {
                    "name": "AB",  # Too short
                    "email": f"schema.error.{i}.{unique_suffix}@test.com",
                    "password": "ValidPass123!",
                },
                "error_stage": "schema",
            },
            {
                "name": "email_format_errors",
                "generator": lambda i: {
                    "name": f"Email Format Error User {i}",
                    "email": f"invalid-email-format-{i}",
                    "password": "ValidPass123!",
                },
                "error_stage": "schema",
            },
            {
                "name": "duplicate_email_errors",
                "generator": lambda i: {
                    "name": f"Duplicate Email User {i}",
                    "email": f"duplicate.email.{unique_suffix}@test.com",  # Always same email
                    "password": "ValidPass123!",
                },
                "error_stage": "service",
            },
            {
                "name": "long_field_errors",
                "generator": lambda i: {
                    "name": "A" * 51,  # Too long
                    "email": f"long.field.{i}.{unique_suffix}@test.com",
                    "password": "ValidPass123!",
                },
                "error_stage": "schema",
            },
        ]

        # Create one valid user for duplicate email testing
        valid_user_data = UserCreateSchema(
            name=f"Valid User {unique_suffix}",
            email=f"duplicate.email.{unique_suffix}@test.com",
            password="ValidPass123!",
        )
        user_service.create_user(valid_user_data)

        def process_error_scenario_batch(scenario, start_index, batch_size):
            """Process a batch of error scenarios and measure timing."""
            batch_times = []
            batch_errors = []

            for i in range(start_index, start_index + batch_size):
                data = scenario["generator"](i)

                start_time = time.perf_counter()

                try:
                    if scenario["error_stage"] == "schema":
                        # Test schema validation errors
                        schema = UserCreateSchema(**data)
                        # If schema passes, test service
                        user_service.create_user(schema)
                    else:
                        # Test service validation errors
                        schema = UserCreateSchema(**data)
                        user_service.create_user(schema)

                    # Should not reach here for error scenarios
                    end_time = time.perf_counter()
                    batch_times.append((end_time - start_time) * 1000)
                    batch_errors.append("NoError")

                except Exception as e:
                    end_time = time.perf_counter()
                    batch_times.append((end_time - start_time) * 1000)
                    batch_errors.append(type(e).__name__)

            return batch_times, batch_errors

        # Test error processing under concurrent load
        print(f"   Testing concurrent error processing...")

        all_error_results = {}

        for scenario in error_scenarios:
            print(f"      Processing {scenario['name']}...")

            # Process errors concurrently
            num_threads = 10
            operations_per_thread = 20

            with ThreadPoolExecutor(max_workers=num_threads) as executor:
                futures = [
                    executor.submit(
                        process_error_scenario_batch,
                        scenario,
                        thread_id * operations_per_thread,
                        operations_per_thread,
                    )
                    for thread_id in range(num_threads)
                ]

                # Collect results
                all_times = []
                all_errors = []

                for future in futures:
                    batch_times, batch_errors = future.result()
                    all_times.extend(batch_times)
                    all_errors.extend(batch_errors)

            # Analyze error processing performance
            if all_times:
                avg_time = statistics.mean(all_times)
                max_time = max(all_times)
                p95_time = all_times[int(len(all_times) * 0.95)] if len(all_times) > 20 else max_time

                error_distribution = {}
                for error in all_errors:
                    error_distribution[error] = error_distribution.get(error, 0) + 1

                all_error_results[scenario["name"]] = {
                    "avg_time_ms": avg_time,
                    "max_time_ms": max_time,
                    "p95_time_ms": p95_time,
                    "total_operations": len(all_times),
                    "error_distribution": error_distribution,
                }

                print(f"         Avg processing time: {avg_time:.2f}ms")
                print(f"         Max processing time: {max_time:.2f}ms")
                print(f"         Error distribution: {error_distribution}")

                # Record in profiler
                for time_val in all_times:
                    self.profiler.record_error_processing_time(time_val)

        # Overall error processing analysis
        print(f"\n📊 Error Processing Performance Summary:")

        total_error_operations = 0
        total_error_time = 0

        for scenario_name, results in all_error_results.items():
            print(f"   {scenario_name}:")
            print(f"      Operations: {results['total_operations']}")
            print(f"      Avg time: {results['avg_time_ms']:.2f}ms")
            print(f"      P95 time: {results['p95_time_ms']:.2f}ms")

            total_error_operations += results["total_operations"]
            total_error_time += results["avg_time_ms"] * results["total_operations"]

        overall_avg_error_time = total_error_time / total_error_operations if total_error_operations > 0 else 0
        print(f"\n🔄 Overall Error Processing Performance:")
        print(f"   Total error operations: {total_error_operations}")
        print(f"   Overall avg time: {overall_avg_error_time:.2f}ms")

        # Performance assertions
        assert overall_avg_error_time < 200.0, (
            f"Overall error processing should be <200ms, got {overall_avg_error_time:.2f}ms"
        )

        for scenario_name, results in all_error_results.items():
            avg_time = results["avg_time_ms"]
            max_time = results["max_time_ms"]

            assert avg_time < 250.0, f"Error processing for {scenario_name} should be <250ms avg, got {avg_time:.2f}ms"
            assert max_time < 1000.0, (
                f"Max error processing for {scenario_name} should be <1000ms, got {max_time:.2f}ms"
            )


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short", "-s"])
