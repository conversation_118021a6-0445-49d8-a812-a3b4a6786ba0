"""Test fixtures for performance tests."""

import pytest
import asyncio
from typing import List, Dict
from decimal import Decimal

from src.core.models.general.component import Component
from src.core.models.general.component_category import ComponentCategory
from src.core.models.general.component_type import ComponentType
from src.core.repositories.general.component_repository import ComponentRepository
from tests.performance.sync_repository_adapter import SyncComponentRepositoryAdapter


@pytest.fixture(scope="function", autouse=True)
async def setup_performance_test_db():
    """Initialize database engine for performance tests.

    This fixture ensures that the async database engine is properly initialized
    before any performance tests run, preventing RuntimeError: Async database
    engine not initialized errors.
    """
    from src.core.database.engine import initialize_database_engine

    try:
        await initialize_database_engine()
    except Exception:
        # If already initialized, that's fine
        pass


@pytest.fixture
def component_repository(db_session):
    """Create a component repository instance."""
    return SyncComponentRepositoryAdapter(db_session)


@pytest.fixture
def test_component_categories(db_session) -> Dict[str, ComponentCategory]:
    """Create component categories for testing."""
    categories = {
        "protection_devices": ComponentCategory(
            name="PROTECTION_DEVICES",
            description="Electrical protection devices and components",
            is_active=True,
        ),
        "distribution_equipment": ComponentCategory(
            name="DISTRIBUTION_EQUIPMENT",
            description="Power distribution equipment and components",
            is_active=True,
        ),
    }

    for category in categories.values():
        db_session.add(category)
    db_session.commit()

    for category in categories.values():
        db_session.refresh(category)

    return categories


@pytest.fixture
def test_component_types(db_session, test_component_categories) -> Dict[str, ComponentType]:
    """Create component types for testing."""
    types = {
        "circuit_breaker": ComponentType(
            name="CIRCUIT_BREAKER",
            description="Circuit breaker components for protection",
            category=test_component_categories["protection_devices"],
            is_active=True,
        ),
        "fuse": ComponentType(
            name="FUSE",
            description="Fuse components for overcurrent protection",
            category=test_component_categories["protection_devices"],
            is_active=True,
        ),
    }

    for type_obj in types.values():
        db_session.add(type_obj)
    db_session.commit()

    for type_obj in types.values():
        db_session.refresh(type_obj)

    return types


@pytest.fixture
def large_component_dataset(
    db_session, component_repository, test_component_types, test_component_categories
) -> List[Component]:
    """Create a large dataset of components for performance testing."""
    import uuid

    unique_suffix = str(uuid.uuid4())[:8]
    components = []

    # Create 1000 test components
    for i in range(1000):
        # Alternate between component types
        types = list(test_component_types.values())
        type_obj = types[i % 2]  # Circuit breaker and fuse alternation
        category = type_obj.category

        component_data = {
            "name": f"Test Component {i:04d} {unique_suffix}",
            "manufacturer": f"Manufacturer {i % 10} {unique_suffix}",
            "model_number": f"MODEL-{i:04d}-{unique_suffix}",
            "description": f"Test component {i} for performance testing",
            "component_type_id": type_obj.id,
            "category_id": category.id,
            "specifications": {
                "rated_current": 16 + (i % 50),
                "rated_voltage": 230 + (i % 100),
                "breaking_capacity": 6000 + (i % 1000),
                "curve_type": "C",
                "poles": 1 + (i % 3),
            },
            "unit_price": Decimal(str(100.0 + (i % 500))),
            "currency": "EUR",
            "supplier": f"Supplier {i % 5} {unique_suffix}",
            "part_number": f"PART-{i:04d}-{unique_suffix}",
            "weight_kg": 0.5 + (i % 10) * 0.1,
            "is_active": True,
            "is_preferred": i % 100 == 0,  # Every 100th component is preferred
            "stock_status": "available" if i % 10 != 9 else "limited",
            "version": "1.0",
        }

        component = component_repository.create(component_data)
        components.append(component)

        # Commit every 100 components to avoid memory issues
        if (i + 1) % 100 == 0:
            db_session.commit()

    # Final commit
    db_session.commit()

    return components


@pytest.fixture
def sample_component_data_list(test_component_types, test_component_categories) -> List[Dict]:
    """Generate a list of sample component data for bulk operations."""
    import uuid

    unique_suffix = str(uuid.uuid4())[:8]

    circuit_breaker_type = test_component_types["circuit_breaker"]
    protection_category = test_component_categories["protection_devices"]

    component_data_list = []

    for i in range(100):
        component_data = {
            "name": f"Bulk Component {i:03d} {unique_suffix}",
            "manufacturer": f"Bulk Manufacturer {i % 5} {unique_suffix}",
            "model_number": f"BULK-{i:03d}-{unique_suffix}",
            "description": f"Bulk test component {i}",
            "component_type_id": circuit_breaker_type.id,
            "category_id": protection_category.id,
            "specifications": {
                "rated_current": 10 + (i % 20),
                "rated_voltage": 230,
                "breaking_capacity": 6000,
                "curve_type": "B",
                "poles": 1,
            },
            "unit_price": Decimal(str(50.0 + i)),
            "currency": "EUR",
            "supplier": f"Bulk Supplier {i % 3} {unique_suffix}",
            "part_number": f"BULK-PART-{i:03d}-{unique_suffix}",
            "weight_kg": 0.3 + (i % 5) * 0.1,
            "is_active": True,
            "is_preferred": False,
            "stock_status": "available",
            "version": "1.0",
        }
        component_data_list.append(component_data)

    return component_data_list
