"""Integration tests for middleware-safe error handling.

Tests the middleware-safe error handling system with real API endpoints
to ensure no ExceptionGroup errors occur in the Starlette middleware stack.
"""

import asyncio
import os
import sys
from typing import Any, Dict
from unittest.mock import patch

import pytest
from fastapi import FastAPI
from fastapi.testclient import TestClient
from httpx import AsyncClient

# Add backend to path for imports
server_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "..")
sys.path.insert(0, server_path)

from src.app import create_app
from src.core.errors.exceptions import (
    SecurityMiddlewareException,
    MiddlewareSafeException,
)

pytestmark = [pytest.mark.integration]


class TestMiddlewareSafeErrorHandlingIntegration:
    """Integration tests for middleware-safe error handling."""

    @pytest.fixture
    def app(self) -> FastAPI:
        """Create test FastAPI application."""
        return create_app()

    @pytest.fixture
    def client(self, app: FastAPI) -> TestClient:
        """Create test client."""
        return TestClient(app)

    @pytest.fixture
    async def async_client(self, app: FastAPI) -> AsyncClient:
        """Create async test client."""
        from httpx import ASGITransport

        async with AsyncClient(transport=ASGITransport(app=app), base_url="http://test") as ac:
            yield ac

    def test_middleware_safe_exception_handler_registration(self, app: FastAPI):
        """Test that middleware-safe exception handler is properly registered."""
        # Check that the exception handler is registered
        exception_handlers = app.exception_handlers

        # Should have handlers for MiddlewareSafeException and general Exception
        assert len(exception_handlers) >= 2

        # Check if MiddlewareSafeException handler is registered
        middleware_safe_handler_found = False
        for exc_type, handler in exception_handlers.items():
            if exc_type == MiddlewareSafeException or (
                hasattr(exc_type, "__name__") and exc_type.__name__ == "MiddlewareSafeException"
            ):
                middleware_safe_handler_found = True
                break

        assert middleware_safe_handler_found, "MiddlewareSafeException handler not found"

    def test_security_middleware_with_valid_request(self, client: TestClient):
        """Test security middleware with valid request (no errors)."""
        # Test a simple endpoint that should work
        response = client.get("/health")

        # Should not get ExceptionGroup errors
        assert response.status_code in [200, 404]  # 404 is fine if endpoint doesn't exist

        # Response should be JSON, not an error page
        try:
            response.json()
        except ValueError:
            # If not JSON, at least should not contain ExceptionGroup
            assert "ExceptionGroup" not in response.text
            assert "unhandled errors in a TaskGroup" not in response.text

    def test_security_middleware_with_authentication_error(self, client: TestClient):
        """Test security middleware with authentication error."""
        # Try to access a protected endpoint without authentication
        response = client.get("/api/v1/projects", headers={})

        # Should get proper HTTP error response, not ExceptionGroup
        assert response.status_code in [401, 403, 404, 422]

        # Response should be JSON error response
        try:
            error_data = response.json()
            # Should have proper error structure (either "detail" or "details")
            assert "detail" in error_data or "details" in error_data or "error" in error_data
            # Should not contain ExceptionGroup information
            assert "ExceptionGroup" not in str(error_data)
            assert "unhandled errors in a TaskGroup" not in str(error_data)
        except ValueError:
            # If not JSON, check text doesn't contain ExceptionGroup
            assert "ExceptionGroup" not in response.text
            assert "unhandled errors in a TaskGroup" not in response.text

    def test_api_endpoint_with_invalid_data(self, client: TestClient):
        """Test API endpoint with invalid data to trigger validation errors."""
        # Try to create a project with invalid data
        invalid_project_data = {
            "name": "",  # Invalid: empty name
            "description": "Test project",
        }

        response = client.post("/api/v1/projects", json=invalid_project_data)

        # Should get validation error, not ExceptionGroup (401 is also acceptable for auth)
        assert response.status_code in [400, 401, 422, 404]  # 404 if endpoint doesn't exist

        # Check response doesn't contain ExceptionGroup
        try:
            error_data = response.json()
            assert "ExceptionGroup" not in str(error_data)
            assert "unhandled errors in a TaskGroup" not in str(error_data)
        except ValueError:
            assert "ExceptionGroup" not in response.text
            assert "unhandled errors in a TaskGroup" not in response.text

    def test_database_error_handling(self, client: TestClient):
        """Test database error handling through API."""
        # Try to access a non-existent resource
        response = client.get("/api/v1/projects/99999")

        # Should get proper 404 or other error, not ExceptionGroup (401 is also acceptable for auth)
        assert response.status_code in [401, 404, 422]

        # Check response format
        try:
            error_data = response.json()
            assert "detail" in error_data
            assert "ExceptionGroup" not in str(error_data)
            assert "unhandled errors in a TaskGroup" not in str(error_data)
        except ValueError:
            assert "ExceptionGroup" not in response.text
            assert "unhandled errors in a TaskGroup" not in response.text

    @pytest.mark.asyncio
    async def test_async_middleware_error_handling(self, async_client: AsyncClient):
        """Test async middleware error handling."""
        # Test async request that might trigger middleware errors
        response = await async_client.get("/api/v1/components")

        # Should not get ExceptionGroup errors
        assert response.status_code in [200, 401, 403, 404, 422]

        # Check response doesn't contain ExceptionGroup
        try:
            error_data = response.json()
            if isinstance(error_data, dict):
                assert "ExceptionGroup" not in str(error_data)
                assert "unhandled errors in a TaskGroup" not in str(error_data)
        except ValueError:
            assert "ExceptionGroup" not in response.text
            assert "unhandled errors in a TaskGroup" not in response.text

    def test_middleware_stack_integration(self, client: TestClient):
        """Test that the entire middleware stack works without ExceptionGroup errors."""
        # Test multiple endpoints to ensure middleware stack is stable
        endpoints_to_test = [
            "/health",
            "/api/v1/projects",
            "/api/v1/components",
            "/api/v1/users/me",
        ]

        for endpoint in endpoints_to_test:
            response = client.get(endpoint)

            # Any status code is fine, just no ExceptionGroup
            assert response.status_code < 600  # Valid HTTP status code

            # Check response doesn't contain ExceptionGroup
            response_text = response.text
            assert "ExceptionGroup" not in response_text
            assert "unhandled errors in a TaskGroup" not in response_text
            assert "anyio" not in response_text.lower() or "create_task_group" not in response_text

    def test_concurrent_requests_no_exception_group(self, client: TestClient):
        """Test concurrent requests don't cause ExceptionGroup errors."""
        import concurrent.futures
        import threading

        def make_request(endpoint: str) -> Dict[str, Any]:
            """Make a request and return result info."""
            try:
                response = client.get(endpoint)
                return {
                    "endpoint": endpoint,
                    "status_code": response.status_code,
                    "has_exception_group": "ExceptionGroup" in response.text,
                    "has_task_group_error": "unhandled errors in a TaskGroup" in response.text,
                    "success": True,
                }
            except Exception as e:
                return {
                    "endpoint": endpoint,
                    "error": str(e),
                    "has_exception_group": "ExceptionGroup" in str(e),
                    "has_task_group_error": "unhandled errors in a TaskGroup" in str(e),
                    "success": False,
                }

        # Test multiple endpoints concurrently
        endpoints = [
            "/health",
            "/api/v1/projects",
            "/api/v1/components",
            "/api/v1/users/me",
        ]

        # Make concurrent requests
        with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
            futures = [executor.submit(make_request, endpoint) for endpoint in endpoints]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]

        # Check all results
        for result in results:
            assert not result["has_exception_group"], f"ExceptionGroup found in {result['endpoint']}"
            assert not result["has_task_group_error"], f"TaskGroup error found in {result['endpoint']}"

    def test_middleware_safe_exception_conversion(self, app: FastAPI):
        """Test that middleware-safe exceptions are properly converted to HTTP responses."""
        from fastapi import Request
        from fastapi.responses import JSONResponse

        # Create a test request with proper ASGI scope
        scope = {
            "type": "http",
            "method": "GET",
            "path": "/test",
            "headers": [],
            "query_string": b"",
            "root_path": "",
            "scheme": "http",
            "server": ("testserver", 80),
        }
        request = Request(scope)

        # Create a middleware-safe exception
        exc = SecurityMiddlewareException(
            message="Test security error", status_code=403, detail="Access denied", security_operation="test_operation"
        )

        # Get the exception handler
        handler = None
        for exc_type, exc_handler in app.exception_handlers.items():
            if exc_type == MiddlewareSafeException or (
                hasattr(exc_type, "__name__") and exc_type.__name__ == "MiddlewareSafeException"
            ):
                handler = exc_handler
                break

        assert handler is not None, "MiddlewareSafeException handler not found"

        # Test the handler
        response = asyncio.run(handler(request, exc))

        assert isinstance(response, JSONResponse)
        assert response.status_code == 403

        # Check response content
        import json

        content = json.loads(response.body.decode())
        assert content["detail"] == "Access denied"
        assert content["error_code"] == "SECURITY_TEST_OPERATION_ERROR"
        assert content["category"] == "MiddlewareError"

    def test_no_exception_group_in_error_logs(self, client: TestClient, caplog):
        """Test that error logs don't contain ExceptionGroup references."""
        import logging

        # Make requests that might trigger errors
        endpoints = ["/api/v1/projects", "/api/v1/components/99999"]

        with caplog.at_level(logging.ERROR):
            for endpoint in endpoints:
                try:
                    client.get(endpoint)
                except Exception:
                    pass  # Ignore exceptions, we're checking logs

        # Check logs don't contain ExceptionGroup references
        log_text = caplog.text
        assert "ExceptionGroup" not in log_text
        assert "unhandled errors in a TaskGroup" not in log_text
