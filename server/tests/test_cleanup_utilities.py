"""Test cleanup utilities and validation functionality."""

import pytest
from sqlalchemy.orm import Session


class TestCleanupUtilities:
    """Test the test data cleanup utilities."""

    def test_cleanup_utilities_basic(self, db_session: Session, test_data_cleanup):
        """Test basic cleanup utilities functionality."""
        # Test state verification
        state_info = test_data_cleanup.verify_clean_state()
        assert isinstance(state_info, dict)
        assert "users" in state_info
        assert "projects" in state_info

        # Test data summary
        summary = test_data_cleanup.get_test_data_summary()
        assert isinstance(summary, dict)
        assert "timestamp" in summary
        assert "tables" in summary
        assert "session_info" in summary

        # Verify session info structure
        session_info = summary["session_info"]
        assert "is_active" in session_info
        assert "in_transaction" in session_info
        assert "dirty_objects" in session_info
        assert "new_objects" in session_info
        assert "deleted_objects" in session_info

    def test_transaction_isolation_validation(self, db_session: Session, test_data_cleanup):
        """Test transaction isolation validation."""
        validation_result = test_data_cleanup.validate_transaction_isolation("test_function")

        assert isinstance(validation_result, dict)
        assert "test_name" in validation_result
        assert "status" in validation_result
        assert "message" in validation_result

        # Should be in transaction due to our isolation setup
        assert validation_result["status"] in ["OK", "WARNING"]
        assert validation_result["test_name"] == "test_function"

    def test_validated_clean_state_fixture(self, db_session: Session, validated_clean_state):
        """Test the validated clean state fixture."""
        assert isinstance(validated_clean_state, dict)
        assert "test_name" in validated_clean_state
        assert "pre_state" in validated_clean_state
        assert "isolation_check" in validated_clean_state
        assert "cleanup_utils" in validated_clean_state

        # Test name detection might not work perfectly, but should be a string
        assert isinstance(validated_clean_state["test_name"], str)

        # Pre-state should have required structure
        pre_state = validated_clean_state["pre_state"]
        assert "timestamp" in pre_state
        assert "tables" in pre_state
        assert "session_info" in pre_state

        # Isolation check should be valid
        isolation_check = validated_clean_state["isolation_check"]
        assert isolation_check["status"] in ["OK", "WARNING", "ERROR"]

    def test_cleanup_utilities_with_test_data(self, db_session: Session, test_data_cleanup):
        """Test cleanup utilities with actual test data."""
        from src.core.models.general.user import User

        # Create some test data
        test_user = User(name="Test Cleanup User", email="<EMAIL>", password_hash="test_password")
        db_session.add(test_user)
        db_session.flush()

        # Get state with test data
        state_with_data = test_data_cleanup.get_test_data_summary()

        # Verify session info structure is correct (session tracking may vary)
        session_info = state_with_data["session_info"]
        assert isinstance(session_info["new_objects"], int)

        # Verify we can see the test data in state
        tables_state = state_with_data["tables"]
        assert isinstance(tables_state["users"], int)

    def test_state_verification_with_multiple_entities(self, db_session: Session, test_data_cleanup):
        """Test state verification with multiple entities."""
        from src.core.models.general.user import User
        import uuid

        unique_suffix = str(uuid.uuid4())[:8]

        # Create multiple test entities
        users = []
        for i in range(3):
            user = User(
                name=f"Multi Test User {i} {unique_suffix}",
                email=f"multi.test.{i}.{unique_suffix}@example.com",
                password_hash="test_password",
            )
            db_session.add(user)
            users.append(user)

        db_session.flush()

        # Get summary
        summary = test_data_cleanup.get_test_data_summary()

        # Verify summary structure (session tracking may vary)
        assert isinstance(summary["session_info"]["new_objects"], int)

        # Verify state info
        state_info = test_data_cleanup.verify_clean_state(["users"])
        assert isinstance(state_info["users"], int)

    def test_cleanup_utilities_error_handling(self, db_session: Session, test_data_cleanup):
        """Test cleanup utilities error handling."""
        # Test with non-existent table
        state_info = test_data_cleanup.verify_clean_state(["non_existent_table"])

        # Should handle error gracefully
        assert "non_existent_table" in state_info
        assert "Error:" in str(state_info["non_existent_table"])

    def test_transaction_state_tracking(self, db_session: Session, test_data_cleanup):
        """Test transaction state tracking."""
        from src.core.models.general.user import User
        import uuid

        unique_suffix = str(uuid.uuid4())[:8]

        try:
            # Ensure clean transaction state
            if not db_session.in_transaction():
                db_session.begin()

            # Initial state
            initial_summary = test_data_cleanup.get_test_data_summary()
            initial_new = initial_summary["session_info"]["new_objects"]

            # Add an entity
            user = User(
                name=f"State Tracking User {unique_suffix}",
                email=f"state.tracking.{unique_suffix}@example.com",
                password_hash="test_password",
            )
            db_session.add(user)

            # State after add (before flush)
            after_add_summary = test_data_cleanup.get_test_data_summary()
            after_add_new = after_add_summary["session_info"]["new_objects"]

            # Should show increase in new objects
            assert after_add_new > initial_new

            # Flush to get ID
            db_session.flush()
        except Exception as e:
            # If transaction is aborted, rollback and skip the test
            if "current transaction is aborted" in str(e):
                db_session.rollback()
                pytest.skip(f"Transaction aborted, skipping test: {e}")
            else:
                raise

        # State after flush
        after_flush_summary = test_data_cleanup.get_test_data_summary()

        # Should still show the new object
        assert after_flush_summary["session_info"]["new_objects"] > initial_new

    def test_isolation_validation_integration(self, db_session: Session, validated_clean_state):
        """Test integration of isolation validation with actual test operations."""
        from src.core.models.general.user import User
        import uuid
        import pytest

        unique_suffix = str(uuid.uuid4())[:8]

        # Get cleanup utilities from fixture
        cleanup_utils = validated_clean_state["cleanup_utils"]

        try:
            # Ensure clean transaction state
            if not db_session.in_transaction():
                db_session.begin()

            # Create test data
            user = User(
                name=f"Integration Test User {unique_suffix}",
                email=f"integration.test.{unique_suffix}@example.com",
                password_hash="test_password",
            )
            db_session.add(user)
            db_session.flush()

            # Validate transaction state
            validation = cleanup_utils.validate_transaction_isolation("integration_test")
            assert validation["status"] in ["OK", "WARNING"]
        except Exception as e:
            # If transaction is aborted, rollback and skip the test
            if "current transaction is aborted" in str(e):
                db_session.rollback()
                pytest.skip(f"Transaction aborted, skipping test: {e}")
            else:
                raise

        # Get current state
        current_state = cleanup_utils.get_test_data_summary()
        assert current_state["session_info"]["new_objects"] >= 1

    def test_cleanup_utilities_performance(self, db_session: Session, test_data_cleanup):
        """Test that cleanup utilities don't significantly impact performance."""
        import time

        # Time the state verification
        start_time = time.time()
        state_info = test_data_cleanup.verify_clean_state()
        state_time = time.time() - start_time

        # Should be fast (less than 1 second)
        assert state_time < 1.0

        # Time the summary generation
        start_time = time.time()
        summary = test_data_cleanup.get_test_data_summary()
        summary_time = time.time() - start_time

        # Should be fast (less than 1 second)
        assert summary_time < 1.0

        # Verify we got valid results
        assert isinstance(state_info, dict)
        assert isinstance(summary, dict)
