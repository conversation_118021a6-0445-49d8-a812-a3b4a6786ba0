"""Global test configuration."""

import os
import sys
import pytest
import asyncio
import httpx
from datetime import datetime
from typing import Generator, AsyncGenerator
from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker, AsyncSession
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from fastapi.testclient import TestClient

# Set testing environment variables before importing application modules
os.environ["TESTING"] = "true"
os.environ["ENVIRONMENT"] = "testing"

# Add server and tests to path for imports# Add backend to path for imports
server_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..")
tests_path = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, server_path)
sys.path.insert(0, tests_path)
from src.core.enums import ProjectStatus
from src.core.database.dependencies import get_db
from src.core.models.base import Base

# Test database configuration - Use PostgreSQL for consistency with production
# Note: For CI/CD, ensure PostgreSQL test database is available
# Local test database can be accessed with command:
# `psql -h localhost -p 5433 -U user -d ultimate_electrical_designer_test`
TEST_DATABASE_URL = None


@pytest.fixture(scope="session")
def test_settings():
    """Override settings for tests."""
    from src.config.settings import settings

    # Ensure TEST_DATABASE_URL is available
    if not settings.TEST_DATABASE_URL:
        raise ValueError("TEST_DATABASE_URL is not configured in environment")

    # Set the DATABASE_URL to the test database URL for test context
    settings.DATABASE_URL = settings.TEST_DATABASE_URL
    settings.ENVIRONMENT = "testing"
    return settings


@pytest.fixture(scope="function")
def engine(test_settings):
    """Create a test database engine for each test function."""
    # Use PostgreSQL test database for consistency with production
    from sqlalchemy import create_engine
    import uuid

    # Get TEST_DATABASE_URL from test_settings
    TEST_DATABASE_URL = test_settings.TEST_DATABASE_URL

    # Use the configured test database directly (database isolation handled by test cleanup)
    test_db_url = TEST_DATABASE_URL

    # DEBUG: Print the actual URL being used
    print(f"DEBUG: Creating engine with URL: {test_db_url}")

    engine = create_engine(
        test_db_url,
        echo=False,  # Disable SQL logging for tests
        pool_pre_ping=True,
        pool_recycle=300,
        # Test-specific optimizations for better performance and resource usage
        pool_size=2,  # Minimal pool for test environment
        max_overflow=3,  # Limited overflow for concurrent tests
        pool_timeout=10,  # Shorter timeout for faster test feedback
    )

    # Create the pg_trgm extension for GIN indexes on text columns
    with engine.connect() as conn:
        conn.execute(text("CREATE EXTENSION IF NOT EXISTS pg_trgm"))
        conn.commit()

    # Don't create/drop tables - they should already exist from migrations
    # Base.metadata.create_all(bind=engine)
    yield engine
    # Base.metadata.drop_all(bind=engine)
    engine.dispose()


@pytest.fixture(scope="function")
def shared_connection(engine):
    """Create shared connection for transaction-based test isolation."""
    connection = engine.connect()
    transaction = connection.begin()

    yield connection

    # Always rollback transaction - ensures complete test isolation
    transaction.rollback()
    connection.close()


@pytest.fixture(scope="function")
def db_session(shared_connection) -> Generator[Session, None, None]:
    """Create isolated database session with automatic rollback."""
    session = Session(
        bind=shared_connection,
        autocommit=False,
        autoflush=False,
        expire_on_commit=False,  # Keep objects accessible after commit
    )
    try:
        yield session
    finally:
        session.close()
        # Transaction rollback handled by shared_connection fixture


@pytest.fixture(scope="function")
async def async_db_session(shared_connection) -> AsyncGenerator[AsyncSession, None]:
    """Create isolated async database session using shared connection.

    This fixture creates an async session that participates in the same transaction
    as the sync session, ensuring proper test isolation between async and sync operations.
    """
    from sqlalchemy.ext.asyncio import AsyncSession
    from sqlalchemy.pool import StaticPool
    from sqlalchemy.ext.asyncio import create_async_engine

    # Create async engine that uses the same connection pool as the shared connection
    # This ensures both sync and async sessions participate in the same transaction
    async_engine = create_async_engine(
        "postgresql+asyncpg://user:password@localhost:5433/ultimate_electrical_designer_test",
        echo=False,
        poolclass=StaticPool,
        pool_pre_ping=True,
        pool_recycle=300,
        # StaticPool doesn't support pool_size/max_overflow parameters
    )

    # Create async session factory
    from sqlalchemy.ext.asyncio import async_sessionmaker

    async_session_factory = async_sessionmaker(
        bind=async_engine,
        class_=AsyncSession,
        autocommit=False,
        autoflush=False,
        expire_on_commit=False,
    )

    async_session = async_session_factory()
    try:
        yield async_session
    finally:
        # Clean session closure
        try:
            await async_session.close()
        except Exception:
            # Ignore cleanup errors - transaction rollback handles data cleanup
            pass

        try:
            await async_engine.dispose()
        except Exception:
            # Ignore cleanup errors - not critical for test functionality
            pass


def reset_sequences(session, tables=None):
    """Reset auto-increment sequences to start from 1 for predictable test IDs.

    Note: Sequence operations are not rolled back with transactions, so we need
    to be careful about when and how we reset them.
    """
    if tables is None:
        # Common tables that need sequence reset
        tables = [
            "users",
            "projects",
            "components",
            "component_categories",
            "user_preferences",
            "project_settings",
            "component_specifications",
        ]

    for table in tables:
        try:
            # Check if sequence exists first
            sequence_exists = session.execute(
                text("SELECT EXISTS (SELECT 1 FROM pg_sequences WHERE sequencename = :seq_name)"),
                {"seq_name": f"{table}_id_seq"},
            ).scalar()

            if sequence_exists:
                # Reset sequence to start from 1
                session.execute(text(f"ALTER SEQUENCE {table}_id_seq RESTART WITH 1"))
        except Exception as e:
            # Ignore errors for tables that don't exist or don't have sequences
            print(f"DEBUG: Could not reset sequence for {table}: {e}")
            pass

    # Commit sequence changes in a separate transaction
    # Note: This is outside the main test transaction
    try:
        session.commit()
    except Exception as e:
        # If commit fails, rollback and continue
        print(f"DEBUG: Could not commit sequence reset: {e}")
        session.rollback()


def get_next_available_id(session, table_name, id_column="id"):
    """Get the next available ID for a table, useful for predictable test data."""
    try:
        result = session.execute(text(f"SELECT COALESCE(MAX({id_column}), 0) + 1 FROM {table_name}")).scalar()
        return result
    except Exception:
        return 1


def create_isolated_test_data(session, model_class, **kwargs):
    """Create test data with predictable IDs in isolated transaction."""
    try:
        # Create the entity
        entity = model_class(**kwargs)
        session.add(entity)
        session.flush()  # Get ID without committing
        return entity
    except Exception as e:
        session.rollback()
        raise e


@pytest.fixture(scope="function")
def clean_test_data(db_session):
    """Ensure clean test data state with transaction isolation."""
    try:
        # Verify clean state - should have no data due to transaction isolation
        from src.core.models.general.user import User

        user_count = db_session.query(User).count()

        # Note: In transaction isolation, we might see data from previous tests
        # until the transaction is committed, but it will be rolled back
        print(f"DEBUG: User count at test start: {user_count}")

    except Exception as e:
        # If there's an error in setup, rollback and continue
        print(f"DEBUG: Error in clean_test_data setup: {e}")
        db_session.rollback()
        # Don't fail the test due to setup issues

    yield

    # Automatic rollback handles cleanup via shared_connection fixture


@pytest.fixture(scope="function")
def predictable_ids(db_session):
    """Fixture that provides predictable ID generation for tests.

    Since sequence reset doesn't work with transaction isolation,
    this fixture provides utilities for predictable test data creation.
    """

    class PredictableIDHelper:
        def __init__(self, session):
            self.session = session
            self._id_counters = {}

        def get_next_id(self, table_name):
            """Get next predictable ID for a table."""
            if table_name not in self._id_counters:
                # Start from a high number to avoid conflicts with existing data
                self._id_counters[table_name] = 100000
            else:
                self._id_counters[table_name] += 1
            return self._id_counters[table_name]

        def create_user_with_id(self, user_data, user_id=None):
            """Create user with predictable ID."""
            from src.core.models.general.user import User

            if user_id is None:
                user_id = self.get_next_id("users")

            # Create user with explicit ID
            user = User(id=user_id, **user_data)
            self.session.add(user)
            self.session.flush()
            return user

        def create_project_with_id(self, project_data, project_id=None):
            """Create project with predictable ID."""
            from src.core.models.general.project import Project

            if project_id is None:
                project_id = self.get_next_id("projects")

            # Create project with explicit ID
            project = Project(id=project_id, **project_data)
            self.session.add(project)
            self.session.flush()
            return project

    return PredictableIDHelper(db_session)


class TestDataCleanupUtilities:
    """Comprehensive test data cleanup utilities."""

    def __init__(self, session):
        self.session = session

    def verify_clean_state(self, tables=None):
        """Verify that specified tables are in clean state."""
        if tables is None:
            tables = ["users", "projects", "components", "component_categories", "user_preferences", "project_settings"]

        state_info = {}
        for table in tables:
            try:
                count = self.session.execute(text(f"SELECT COUNT(*) FROM {table}")).scalar()
                state_info[table] = count
            except Exception as e:
                state_info[table] = f"Error: {e}"

        return state_info

    def get_test_data_summary(self):
        """Get summary of current test data state."""
        summary = {
            "timestamp": str(datetime.now()),
            "tables": self.verify_clean_state(),
            "session_info": {
                "is_active": self.session.is_active,
                "in_transaction": self.session.in_transaction(),
                "dirty_objects": len(self.session.dirty),
                "new_objects": len(self.session.new),
                "deleted_objects": len(self.session.deleted),
            },
        }
        return summary

    def validate_transaction_isolation(self, test_name):
        """Validate that transaction isolation is working for a test."""
        try:
            # Check if we're in a transaction
            if not self.session.in_transaction():
                return {
                    "test_name": test_name,
                    "status": "WARNING",
                    "message": "Not in transaction - isolation may not be working",
                }

            # Check session state
            session_state = {
                "dirty": len(self.session.dirty),
                "new": len(self.session.new),
                "deleted": len(self.session.deleted),
            }

            return {
                "test_name": test_name,
                "status": "OK",
                "message": "Transaction isolation active",
                "session_state": session_state,
            }

        except Exception as e:
            return {"test_name": test_name, "status": "ERROR", "message": f"Validation failed: {e}"}

    def cleanup_test_artifacts(self, patterns=None):
        """Clean up test artifacts that might persist."""
        if patterns is None:
            patterns = ["test%", "%test%", "Test%", "%Test%", "temp%", "%temp%"]

        cleanup_results = {}

        # Clean up users with test patterns
        try:
            for pattern in patterns:
                result = self.session.execute(
                    text("DELETE FROM users WHERE email LIKE :pattern OR name LIKE :pattern"), {"pattern": pattern}
                )
                cleanup_results[f"users_{pattern}"] = result.rowcount
        except Exception as e:
            cleanup_results["users_cleanup_error"] = str(e)

        # Clean up projects with test patterns
        try:
            for pattern in patterns:
                result = self.session.execute(
                    text("DELETE FROM projects WHERE name LIKE :pattern OR description LIKE :pattern"),
                    {"pattern": pattern},
                )
                cleanup_results[f"projects_{pattern}"] = result.rowcount
        except Exception as e:
            cleanup_results["projects_cleanup_error"] = str(e)

        return cleanup_results


@pytest.fixture(scope="function")
def test_data_cleanup(db_session):
    """Fixture providing test data cleanup utilities."""
    return TestDataCleanupUtilities(db_session)


@pytest.fixture(scope="function")
def validated_clean_state(db_session, test_data_cleanup):
    """Fixture that validates clean state before and after test."""
    import inspect

    # Get the test function name
    frame = inspect.currentframe()
    test_name = "unknown_test"
    try:
        # Walk up the stack to find the test function
        for i in range(10):  # Limit search depth
            frame = frame.f_back
            if frame and frame.f_code.co_name.startswith("test_"):
                test_name = frame.f_code.co_name
                break
    except:
        pass

    # Pre-test validation
    pre_state = test_data_cleanup.get_test_data_summary()
    isolation_check = test_data_cleanup.validate_transaction_isolation(test_name)

    print(f"DEBUG: Pre-test state for {test_name}:")
    print(f"  - Transaction isolation: {isolation_check['status']}")
    print(f"  - Session state: {pre_state['session_info']}")

    yield {
        "test_name": test_name,
        "pre_state": pre_state,
        "isolation_check": isolation_check,
        "cleanup_utils": test_data_cleanup,
    }

    # Post-test validation
    post_state = test_data_cleanup.get_test_data_summary()
    print(f"DEBUG: Post-test state for {test_name}:")
    print(f"  - Session state: {post_state['session_info']}")

    # Note: Actual cleanup happens via transaction rollback


@pytest.fixture(scope="function")
def client(db_session: Session, clean_test_data) -> Generator[TestClient, None, None]:
    """Create a test client with isolated database session."""
    from src.app import create_app
    from src.core.database.dependencies import get_db

    app = create_app()

    def override_get_db():
        yield db_session

    app.dependency_overrides[get_db] = override_get_db

    with TestClient(app) as test_client:
        yield test_client

    app.dependency_overrides.clear()


@pytest.fixture(scope="function")
async def async_http_client(
    async_db_session: AsyncSession, db_session: Session, clean_test_data
) -> AsyncGenerator[httpx.AsyncClient, None]:
    """Create an async test client with optimized database session overrides."""
    from src.app import create_app
    from src.core.database.dependencies import get_project_db_session, get_db
    from src.core.database.connection_manager import (
        get_contextual_db_session,
        get_project_contextual_db_session,
        get_central_db_session,
    )
    import httpx

    app = create_app()

    # Optimized database session overrides using test sessions
    # This ensures all database operations use the same test transaction
    def override_get_db():
        return db_session

    async def override_get_project_db_session(project_id: int = None, project_repo=None):
        yield async_db_session

    async def override_get_contextual_db_session(project_id: int = None, project_repo=None):
        yield async_db_session

    async def override_get_project_contextual_db_session(project_id: int = None, project_repo=None):
        yield async_db_session

    async def override_get_central_db_session():
        yield async_db_session

    # Comprehensive dependency overrides for isolated testing
    app.dependency_overrides[get_db] = override_get_db
    app.dependency_overrides[get_project_db_session] = override_get_project_db_session
    app.dependency_overrides[get_contextual_db_session] = override_get_contextual_db_session
    app.dependency_overrides[get_project_contextual_db_session] = override_get_project_contextual_db_session
    app.dependency_overrides[get_central_db_session] = override_get_central_db_session

    # Use httpx.AsyncClient with transport that routes to the FastAPI app
    transport = httpx.ASGITransport(app=app)
    async with httpx.AsyncClient(transport=transport, base_url="http://testserver") as async_client:
        yield async_client

    app.dependency_overrides.clear()


@pytest.fixture(scope="function")
def async_client(async_db_session: AsyncSession, clean_test_data) -> Generator[TestClient, None, None]:
    """Create a test client with isolated async database session override."""
    from src.app import create_app
    from src.core.database.connection_manager import get_contextual_db_session

    app = create_app()

    async def override_get_contextual_db_session(project_id: int = None, project_repo=None):
        yield async_db_session

    app.dependency_overrides[get_contextual_db_session] = override_get_contextual_db_session

    with TestClient(app) as test_client:
        yield test_client

    app.dependency_overrides.clear()


# --------------------------------User Fixtures--------------------------------#

import uuid


@pytest.fixture
def admin_user_data():
    """Create admin user data."""
    unique_suffix = str(uuid.uuid4())[:8]
    return {
        "name": f"Test Admin {unique_suffix}",
        "email": f"admin.{unique_suffix}@example.com",
        "password": "SecurePass123",
        "is_superuser": True,
    }


@pytest.fixture
def user_repository(db_session):
    """Fixture for UserRepository."""
    from src.core.repositories.general.user_repository import UserRepository

    return UserRepository(db_session)


@pytest.fixture
async def async_user_repository(async_db_session):
    """Fixture for UserRepository with async session."""
    from src.core.repositories.general.user_repository import UserRepository

    return UserRepository(async_db_session)


@pytest.fixture
def user_preference_repository(db_session):
    """Fixture for UserPreferenceRepository."""
    from src.core.repositories.general.user_preference_repository import (
        UserPreferenceRepository,
    )

    return UserPreferenceRepository(db_session)


@pytest.fixture
async def async_user_preference_repository(async_db_session):
    """Fixture for UserPreferenceRepository with async session."""
    from src.core.repositories.general.user_preference_repository import (
        UserPreferenceRepository,
    )

    return UserPreferenceRepository(async_db_session)


@pytest.fixture
def user_service(user_repository, user_preference_repository):
    """Fixture for UserService."""
    from src.core.services.general.user_service import UserService

    return UserService(
        user_repository=user_repository,
        preference_repository=user_preference_repository,
    )


@pytest.fixture
async def async_user_service(async_user_repository, async_user_preference_repository):
    """Fixture for UserService with async repositories."""
    from src.core.services.general.user_service import UserService

    return UserService(
        user_repository=async_user_repository,
        preference_repository=async_user_preference_repository,
    )


@pytest.fixture
async def test_admin_user(async_user_service, admin_user_data):
    """Create an admin user in the database."""
    from src.core.schemas.general.user_schemas import UserCreateSchema

    user_create = UserCreateSchema(**admin_user_data)
    user = await async_user_service.create_user(user_create)
    return user


@pytest.fixture
def test_user_data():
    """Create test user data."""
    unique_suffix = str(uuid.uuid4())[:8]
    return {
        "name": f"Test Viewer {unique_suffix}",
        "email": f"viewer.{uuid.uuid4().hex}@example.com",
        "password": "SecurePass123",
        "is_superuser": False,
    }


@pytest.fixture
async def test_user(async_user_service, test_user_data):
    """Create a test user in the database."""
    from src.core.schemas.general.user_schemas import UserCreateSchema

    user_create = UserCreateSchema(**test_user_data)
    user = await async_user_service.create_user(user_create)
    return user


@pytest.fixture
async def admin_token(async_http_client: httpx.AsyncClient, test_admin_user):
    """Get admin authentication token."""
    login_data = {
        "username": test_admin_user.email,
        "password": "SecurePass123",
    }
    response = await async_http_client.post("/api/v1/auth/login", json=login_data)
    response.raise_for_status()
    return response.json()["access_token"]


@pytest.fixture
async def user_token(async_http_client: httpx.AsyncClient, test_user):
    """Get regular user authentication token."""
    login_data = {
        "username": test_user.email,
        "password": "SecurePass123",
    }
    response = await async_http_client.post("/api/v1/auth/login", json=login_data)
    response.raise_for_status()
    return response.json()["access_token"]


@pytest.fixture(scope="function")
async def authenticated_client(async_http_client: httpx.AsyncClient, user_token) -> httpx.AsyncClient:
    """Create authenticated test client."""

    # Set authorization header
    async_http_client.headers.update({"Authorization": f"Bearer {user_token}"})

    return async_http_client


@pytest.fixture(scope="function")
async def admin_client(async_http_client: httpx.AsyncClient, admin_token) -> httpx.AsyncClient:
    """Create authenticated test client with admin privileges."""

    # Set authorization header
    async_http_client.headers.update({"Authorization": f"Bearer {admin_token}"})

    return async_http_client


# Pytest markers for test categorization
pytest_plugins = [
    "pytest_asyncio",
]


def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line("markers", "unit: Unit tests for individual components")
    config.addinivalue_line("markers", "integration: Integration tests for component interaction")
    config.addinivalue_line("markers", "api: API endpoint tests")
    config.addinivalue_line("markers", "database: Database operation tests")
    config.addinivalue_line("markers", "calculations: Engineering calculation tests")
    config.addinivalue_line("markers", "standards: Standards compliance tests")
    config.addinivalue_line("markers", "security: Security validation tests")
    config.addinivalue_line("markers", "performance: Performance benchmarking tests")
    config.addinivalue_line("markers", "slow: Tests that take longer than 1 second")


def pytest_collection_modifyitems(config, items):
    """Automatically mark tests based on their location."""
    for item in items:
        # Mark database tests
        if "database" in str(item.fspath) or "db_session" in item.fixturenames:
            item.add_marker(pytest.mark.database)

        # Mark repository tests
        if "repositories" in str(item.fspath):
            item.add_marker(pytest.mark.repository)
            item.add_marker(pytest.mark.database)
            item.add_marker(pytest.mark.unit)

        # Mark API tests
        if "api" in str(item.fspath):
            item.add_marker(pytest.mark.api)

        # Mark calculation tests
        if "calculations" in str(item.fspath):
            item.add_marker(pytest.mark.calculations)

        # Mark service tests
        if "services" in str(item.fspath):
            item.add_marker(pytest.mark.service)

        # Mark standards tests
        if "standards" in str(item.fspath):
            item.add_marker(pytest.mark.standards)

        # Mark integration tests
        if "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)

        # Mark security tests
        if "security" in str(item.fspath):
            item.add_marker(pytest.mark.security)

        # Mark performance tests
        if "performance" in str(item.fspath):
            item.add_marker(pytest.mark.performance)

        # Mark standards tests
        if "standards" in str(item.fspath):
            item.add_marker(pytest.mark.standards)
        else:
            # Default to unit tests
            item.add_marker(pytest.mark.unit)


# --------------------------------Project Fixtures--------------------------------#


@pytest.fixture
def project_repository(db_session):
    """Fixture for ProjectRepository."""
    from src.core.repositories.general.project_repository import ProjectRepository

    return ProjectRepository(db_session)


@pytest.fixture
def project_member_repository(db_session):
    """Fixture for ProjectMemberRepository."""
    from src.core.repositories.general.project_member_repository import ProjectMemberRepository

    return ProjectMemberRepository(db_session)


@pytest.fixture
async def async_project_repository(async_db_session):
    """Fixture for ProjectRepository with async session."""
    from src.core.repositories.general.project_repository import ProjectRepository

    return ProjectRepository(async_db_session)


@pytest.fixture
async def async_project_member_repository(async_db_session):
    """Fixture for ProjectMemberRepository with async session."""
    from src.core.repositories.general.project_member_repository import ProjectMemberRepository

    return ProjectMemberRepository(async_db_session)


@pytest.fixture
def test_project(db_session, test_user):
    """Create a test project in the database."""
    from src.core.models.general.project import Project

    unique_suffix = str(uuid.uuid4())[:8]
    project = Project(
        name=f"Test Project {unique_suffix}",
        description="A test project",
        project_number=f"PRJ-TEST-{unique_suffix}",
        status=ProjectStatus.ACTIVE.value,  # Use the enum value, not the enum
        client="Test Client",
        location="Test Location",
    )
    db_session.add(project)
    db_session.commit()
    db_session.refresh(project)
    return project


@pytest.fixture
async def async_test_project(async_db_session, test_user):
    """Create a test project in the database using async session."""
    from src.core.models.general.project import Project

    unique_suffix = str(uuid.uuid4())[:8]
    project = Project(
        name=f"Test Project {unique_suffix}",
        description="A test project",
        project_number=f"PRJ-TEST-{unique_suffix}",
        status=ProjectStatus.ACTIVE.value,  # Use the enum value, not the enum
        client="Test Client",
        location="Test Location",
    )
    async_db_session.add(project)
    await async_db_session.flush()
    await async_db_session.commit()
    await async_db_session.refresh(project)
    return project


@pytest.fixture
def test_user_role(db_session):
    """Create a test user role in the database."""
    from src.core.models.general.user_role import UserRole

    unique_suffix = str(uuid.uuid4())[:8]
    role = UserRole()
    role.name = f"TEST_ROLE_{unique_suffix}"
    role.description = "Test role for unit tests"
    role.is_system_role = False
    role.is_active = True
    role.priority = 50
    db_session.add(role)
    db_session.commit()
    db_session.refresh(role)
    return role


@pytest.fixture
async def test_project_member(
    async_project_member_repository,
    async_user_repository,
    async_project_repository,
    async_test_project,
    test_user,
    test_user_role,
):
    """Create a test project member in the database."""
    from src.core.services.general.project_member_service import ProjectMemberService
    from src.core.schemas.general.project_member_schemas import (
        ProjectMemberCreateSchema,
    )

    member_service = ProjectMemberService(
        project_member_repo=async_project_member_repository,
        user_repo=async_user_repository,
        project_repo=async_project_repository,
    )
    member_create = ProjectMemberCreateSchema(
        name="Test Member",
        user_id=test_user.id,
        role_id=test_user_role.id,
        expires_at=None,
    )
    member = await member_service.add_member_to_project(async_test_project.id, member_create)
    return member
