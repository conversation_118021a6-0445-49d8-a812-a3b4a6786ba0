"""Service Dependencies.

This module provides service dependency injection providers for FastAPI.
Services receive their required repositories through dependency injection,
following the layered architecture pattern.
"""

from typing import TYPE_CHECKING

from fastapi import Depends

from src.core.repositories.repository_dependencies import (
    get_component_category_repository,
    get_component_repository,
    get_component_type_repository,
    get_project_member_repository,
    get_project_repository,
    get_task_repository,
    get_user_preference_repository,
    get_user_repository,
)
from src.core.database.connection_manager import _connection_manager

if TYPE_CHECKING:
    from src.core.services.general.component_category_service import (
        ComponentCategoryService,
    )
    from src.core.services.general.component_service import ComponentService
    from src.core.services.general.component_type_service import ComponentTypeService
    from src.core.services.general.project_member_service import ProjectMemberService
    from src.core.services.general.project_service import ProjectService
    from src.core.services.general.task_manager_service import TaskManagerService
    from src.core.services.general.user_service import UserService


def get_project_service(
    project_repo=Depends(get_project_repository),
) -> "ProjectService":
    """Dependency provider for ProjectService."""
    from src.core.services.general.project_service import ProjectService

    return ProjectService(project_repo, _connection_manager)


def get_user_service(
    user_repo=Depends(get_user_repository),
    preference_repo=Depends(get_user_preference_repository),
) -> "UserService":
    """Dependency provider for UserService."""
    from src.core.services.general.user_service import UserService

    return UserService(user_repo, preference_repo)


def get_component_service(
    component_repo=Depends(get_component_repository),
) -> "ComponentService":
    """Dependency provider for ComponentService."""
    from src.core.services.general.component_service import ComponentService

    return ComponentService(component_repo)


def get_component_category_service(
    category_repo=Depends(get_component_category_repository),
) -> "ComponentCategoryService":
    """Dependency provider for ComponentCategoryService."""
    from src.core.services.general.component_category_service import (
        ComponentCategoryService,
    )

    return ComponentCategoryService(category_repo)


def get_component_type_service(
    type_repo=Depends(get_component_type_repository),
) -> "ComponentTypeService":
    """Dependency provider for ComponentTypeService."""
    from src.core.services.general.component_type_service import ComponentTypeService

    return ComponentTypeService(type_repo)


def get_project_member_service(
    project_member_repo=Depends(get_project_member_repository),
    user_repo=Depends(get_user_repository),
    project_repo=Depends(get_project_repository),
) -> "ProjectMemberService":
    """Dependency provider for ProjectMemberService."""
    from src.core.services.general.project_member_service import ProjectMemberService

    return ProjectMemberService(project_member_repo, user_repo, project_repo)


def get_task_manager_service(
    task_repo=Depends(get_task_repository),
    user_repo=Depends(get_user_repository),
    project_repo=Depends(get_project_repository),
) -> "TaskManagerService":
    """Dependency provider for TaskManagerService."""
    from src.core.services.general.task_manager_service import TaskManagerService

    return TaskManagerService(task_repo, user_repo, project_repo)
