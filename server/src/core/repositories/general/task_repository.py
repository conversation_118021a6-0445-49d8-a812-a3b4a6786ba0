"""Task Repository for database operations.

This module provides the TaskRepository class for handling all database
interactions related to the Task model, following the repository pattern
for data access abstraction.
"""

from typing import List, Optional, Dict, Any
from sqlalchemy import select, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from src.config.logging_config import logger
from src.core.errors.unified_error_handler import handle_repository_errors
from src.core.models.general.task import Task, TaskAssignment
from src.core.monitoring.unified_performance_monitor import monitor_repository_performance
from src.core.repositories.base_repository import BaseRepository
from src.core.utils.pagination_utils import (
    PaginationParams,
    PaginationResult,
    SortParams,
    paginate_query_async,
)


class TaskRepository(BaseRepository[Task]):
    """Repository for Task entity data access operations."""

    def __init__(self, db_session: AsyncSession):
        """Initialize the Task repository."""
        super().__init__(db_session, Task)
        logger.debug("TaskRepository initialized")

    @handle_repository_errors("task")
    @monitor_repository_performance("task")
    async def get_by_task_id(self, task_id: str) -> Optional[Task]:
        """Get task by task_id (UUID)."""
        stmt = (
            select(self.model)
            .where(and_(self.model.task_id == task_id, self.model.is_deleted == False))
            .options(selectinload(self.model.assignments))
        )
        result = await self.db_session.execute(stmt)
        return result.scalar_one_or_none()

    @handle_repository_errors("task")
    @monitor_repository_performance("task")
    async def get_all_by_project_id(self, project_id: int, include_deleted: bool = False) -> List[Task]:
        """Get all tasks for a specific project."""
        conditions = [self.model.project_id == project_id]
        if not include_deleted:
            conditions.append(self.model.is_deleted == False)

        stmt = (
            select(self.model)
            .where(and_(*conditions))
            .options(selectinload(self.model.assignments))
            .order_by(self.model.created_at.desc())
        )

        result = await self.db_session.execute(stmt)
        return list(result.scalars().all())

    @handle_repository_errors("task")
    @monitor_repository_performance("task")
    async def get_paginated_by_project_id(
        self,
        project_id: int,
        pagination_params: PaginationParams,
        sort_params: Optional[SortParams] = None,
        filters: Optional[Dict[str, Any]] = None,
        include_deleted: bool = False,
    ) -> PaginationResult:
        """Get paginated tasks for a specific project with optional filters."""
        conditions = [self.model.project_id == project_id]
        if not include_deleted:
            conditions.append(self.model.is_deleted == False)

        # Add additional filters if provided
        if filters:
            for field, value in filters.items():
                if hasattr(self.model, field) and value is not None:
                    if field == "status":
                        # Convert string to TaskStatus enum
                        from src.core.enums import TaskStatus

                        if isinstance(value, str):
                            try:
                                # Find enum by value
                                status_enum = next(status for status in TaskStatus if status.value == value)
                                conditions.append(self.model.status == status_enum)
                            except StopIteration:
                                # Invalid status value, skip this filter
                                continue
                        else:
                            conditions.append(self.model.status == value)
                    elif field == "priority":
                        # Convert string to TaskPriority enum
                        from src.core.enums import TaskPriority

                        if isinstance(value, str):
                            try:
                                # Find enum by value
                                priority_enum = next(priority for priority in TaskPriority if priority.value == value)
                                conditions.append(self.model.priority == priority_enum)
                            except StopIteration:
                                # Invalid priority value, skip this filter
                                continue
                        else:
                            conditions.append(self.model.priority == value)
                    elif field == "assigned_user_id":
                        # Filter by assigned user through TaskAssignment
                        conditions.append(
                            self.model.assignments.any(
                                and_(
                                    TaskAssignment.user_id == value,
                                    TaskAssignment.is_active == True,
                                    TaskAssignment.is_deleted == False,
                                )
                            )
                        )

        query = select(self.model).where(and_(*conditions)).options(selectinload(self.model.assignments))

        return await paginate_query_async(self.db_session, query, self.model, pagination_params, sort_params)

    @handle_repository_errors("task")
    @monitor_repository_performance("task")
    async def get_by_assigned_user(
        self, user_id: int, include_completed: bool = True, include_deleted: bool = False
    ) -> List[Task]:
        """Get all tasks assigned to a specific user."""
        conditions = [
            self.model.assignments.any(
                and_(
                    TaskAssignment.user_id == user_id,
                    TaskAssignment.is_active == True,
                    TaskAssignment.is_deleted == False,
                )
            )
        ]

        if not include_deleted:
            conditions.append(self.model.is_deleted == False)

        if not include_completed:
            # Assuming TaskStatus.COMPLETED exists
            from src.core.enums import TaskStatus

            conditions.append(self.model.status != TaskStatus.COMPLETED)

        stmt = (
            select(self.model)
            .where(and_(*conditions))
            .options(selectinload(self.model.assignments))
            .order_by(self.model.due_date.asc().nullslast())
        )

        result = await self.db_session.execute(stmt)
        return list(result.scalars().all())

    @handle_repository_errors("task")
    @monitor_repository_performance("task")
    async def get_overdue_tasks(self, project_id: Optional[int] = None) -> List[Task]:
        """Get all overdue tasks, optionally filtered by project."""
        from datetime import datetime
        from src.core.enums import TaskStatus

        conditions = [
            self.model.due_date < datetime.utcnow(),
            self.model.status != TaskStatus.COMPLETED,
            self.model.is_deleted == False,
        ]

        if project_id:
            conditions.append(self.model.project_id == project_id)

        stmt = (
            select(self.model)
            .where(and_(*conditions))
            .options(selectinload(self.model.assignments))
            .order_by(self.model.due_date.asc())
        )

        result = await self.db_session.execute(stmt)
        return list(result.scalars().all())

    @handle_repository_errors("task")
    @monitor_repository_performance("task")
    async def search_tasks(
        self, search_term: str, project_id: Optional[int] = None, pagination_params: Optional[PaginationParams] = None
    ) -> List[Task]:
        """Search tasks by title and description."""
        conditions = [
            or_(self.model.title.ilike(f"%{search_term}%"), self.model.description.ilike(f"%{search_term}%")),
            self.model.is_deleted == False,
        ]

        if project_id:
            conditions.append(self.model.project_id == project_id)

        stmt = (
            select(self.model)
            .where(and_(*conditions))
            .options(selectinload(self.model.assignments))
            .order_by(self.model.created_at.desc())
        )

        if pagination_params:
            stmt = stmt.offset((pagination_params.page - 1) * pagination_params.size).limit(pagination_params.size)

        result = await self.db_session.execute(stmt)
        return list(result.scalars().all())

    @handle_repository_errors("task")
    @monitor_repository_performance("task")
    async def update_task_status(self, task_id: str, status: str) -> Optional[Task]:
        """Update task status by task_id."""
        task = await self.get_by_task_id(task_id)
        if not task:
            return None

        from src.core.enums import TaskStatus

        task.status = TaskStatus(status)
        await self.db_session.flush()
        return task

    @handle_repository_errors("task")
    @monitor_repository_performance("task")
    async def soft_delete_by_task_id(self, task_id: str, deleted_by_user_id: int) -> bool:
        """Soft delete a task by task_id."""
        task = await self.get_by_task_id(task_id)
        if not task:
            return False

        from datetime import datetime

        task.is_deleted = True
        task.deleted_at = datetime.utcnow()
        task.deleted_by_user_id = deleted_by_user_id

        # Also soft delete all task assignments
        for assignment in task.assignments:
            assignment.is_deleted = True
            assignment.deleted_at = datetime.utcnow()
            assignment.deleted_by_user_id = deleted_by_user_id

        await self.db_session.flush()
        return True

    @handle_repository_errors("task")
    @monitor_repository_performance("task")
    async def get_task_statistics(self, project_id: int) -> Dict[str, Any]:
        """Get task statistics for a project."""
        from sqlalchemy import func
        from src.core.enums import TaskStatus, TaskPriority

        # Count tasks by status
        status_counts = {}
        for status in TaskStatus:
            count_stmt = select(func.count(self.model.id)).where(
                and_(self.model.project_id == project_id, self.model.status == status, self.model.is_deleted == False)
            )
            result = await self.db_session.execute(count_stmt)
            status_counts[status.value] = result.scalar() or 0

        # Count tasks by priority
        priority_counts = {}
        for priority in TaskPriority:
            count_stmt = select(func.count(self.model.id)).where(
                and_(
                    self.model.project_id == project_id, self.model.priority == priority, self.model.is_deleted == False
                )
            )
            result = await self.db_session.execute(count_stmt)
            priority_counts[priority.value] = result.scalar() or 0

        # Count overdue tasks
        from datetime import datetime

        overdue_stmt = select(func.count(self.model.id)).where(
            and_(
                self.model.project_id == project_id,
                self.model.due_date < datetime.utcnow(),
                self.model.status != TaskStatus.COMPLETED,
                self.model.is_deleted == False,
            )
        )
        overdue_result = await self.db_session.execute(overdue_stmt)
        overdue_count = overdue_result.scalar() or 0

        # Total tasks
        total_stmt = select(func.count(self.model.id)).where(
            and_(self.model.project_id == project_id, self.model.is_deleted == False)
        )
        total_result = await self.db_session.execute(total_stmt)
        total_count = total_result.scalar() or 0

        return {
            "total_tasks": total_count,
            "status_counts": status_counts,
            "priority_counts": priority_counts,
            "overdue_count": overdue_count,
        }
