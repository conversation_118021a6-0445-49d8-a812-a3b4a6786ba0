"""drop_redundant_name_column_from_tasks

Revision ID: 8e92157c3f50
Revises: 130a19a0b95b
Create Date: 2025-08-07 14:06:44.353522

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "8e92157c3f50"
down_revision = "130a19a0b95b"
branch_labels = None
depends_on = None


def upgrade():
    """Drop the redundant name column from tasks table.

    The Task model now uses only the title field, and the name field
    from CommonColumns is no longer needed since it's overridden in the model.
    """
    # Drop the name column from tasks table
    op.drop_column("tasks", "name")


def downgrade():
    """Add back the name column to tasks table.

    This restores the name column in case we need to rollback.
    The column will be nullable to avoid issues with existing data.
    """
    # Add back the name column (nullable to avoid issues with existing data)
    op.add_column("tasks", sa.Column("name", sa.String(255), nullable=True))

    # Update existing records to set name = title for consistency
    op.execute("UPDATE tasks SET name = title WHERE name IS NULL")
