"""Project and Project Member API endpoints.

This module provides API endpoints for managing projects and their members,
including CRUD operations and membership management.
"""

from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.responses import JSONResponse
from starlette.responses import Response
from pydantic import ValidationError

from src.config.logging_config import logger
from src.core.errors.exceptions import BusinessLogicError, NotFoundError
from src.core.errors.unified_error_handler import handle_api_errors
from src.core.monitoring.unified_performance_monitor import monitor_api_performance
from src.core.database.connection_manager import get_contextual_db_session
from src.core.repositories.general.project_member_repository import (
    ProjectMemberRepository,
)
from src.core.repositories.general.project_repository import ProjectRepository
from src.core.repositories.general.user_repository import UserRepository
from src.core.schemas.error import ErrorResponseSchema
from src.core.schemas.general.project_member_schemas import (
    ProjectMemberCreateSchema,
    ProjectMemberReadSchema,
    ProjectMemberUpdateSchema,
)
from src.core.security.enhanced_dependencies import require_authenticated_user
from src.core.services.general.project_member_service import ProjectMemberService
from src.core.services.general.project_service import ProjectService
from src.core.utils.crud_endpoint_factory import create_project_crud_router
from src.core.database.connection_manager import _connection_manager


def get_project_scoped_service(
    session: Any = Depends(get_contextual_db_session),
) -> ProjectService:
    """Get project-scoped service dependency with contextual session and connection manager."""
    return ProjectService(ProjectRepository(session), _connection_manager)


def get_project_scoped_member_service(
    session: Any = Depends(get_contextual_db_session),
) -> ProjectMemberService:
    """Get project-scoped member service dependency with contextual session."""
    return ProjectMemberService(ProjectMemberRepository(session), UserRepository(session), ProjectRepository(session))


router = APIRouter(
    prefix="/projects",
    tags=["Projects"],
    responses={
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
        status.HTTP_403_FORBIDDEN: {
            "description": "Insufficient permissions",
            "model": ErrorResponseSchema,
        },
        status.HTTP_500_INTERNAL_SERVER_ERROR: {
            "description": "Internal server error",
            "model": ErrorResponseSchema,
        },
    },
)

# Create the main project management router for user-accessible endpoints
project_crud_router = create_project_crud_router(get_project_scoped_service)
router.include_router(project_crud_router)


# ============================================================================
# PROJECT ENDPOINTS (CRUD endpoints are handled by factory)
# ============================================================================
# Note: Basic CRUD endpoints (create, read, update, delete, list) are provided
# by the create_project_crud_router factory. Only custom endpoints are defined below.

# ============================================================================
# PROJECT MEMBER ENDPOINTS
# ============================================================================


@router.post(
    "/{project_id}/members",
    response_model=ProjectMemberReadSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Add a member to a project",
    description="Add a user to a project with a specific role",
    operation_id="addProjectMember",
    responses={
        status.HTTP_201_CREATED: {
            "description": "Member added successfully",
            "model": ProjectMemberReadSchema,
        },
        status.HTTP_400_BAD_REQUEST: {
            "description": "Invalid member data",
            "model": ErrorResponseSchema,
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
        status.HTTP_404_NOT_FOUND: {
            "description": "Project or user not found",
            "model": ErrorResponseSchema,
        },
        status.HTTP_409_CONFLICT: {
            "description": "User is already a member of the project",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("add_project_member")
@monitor_api_performance("add_project_member")
async def add_project_member(
    project_id: int,
    member_data: ProjectMemberCreateSchema,
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    project_member_service: ProjectMemberService = Depends(get_project_scoped_member_service),
) -> ProjectMemberReadSchema:
    """Add a user to a project with a specific role.

    Args:
        project_id: Unique project identifier
        member_data: User and role data for the new member
        current_user: Current authenticated user
        project_member_service: Project member service dependency

    Returns:
        ProjectMemberReadSchema: Created project member data

    Raises:
        HTTPException: If addition fails due to validation, project/user not found, or conflict
    """
    logger.info(f"Adding member to project: {project_id}")
    logger.debug(f"Project member addition requested by user: {current_user.get('id')}")

    try:
        created_member = await project_member_service.add_member_to_project(project_id, member_data)
        logger.info(f"Member added successfully to project: {project_id}")
        return created_member

    except ValidationError as e:
        logger.warning(f"Project member addition validation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Member addition validation failed: {str(e)}",
        )
    except NotFoundError as e:
        logger.warning(f"Project or user not found: {e}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))


@router.delete(
    "/{project_id}/members/{user_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Remove a member from a project",
    description="Remove a user from a project",
    operation_id="removeProjectMember",
    responses={
        status.HTTP_204_NO_CONTENT: {"description": "Member removed successfully"},
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
        status.HTTP_404_NOT_FOUND: {
            "description": "Project or user not found",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("remove_project_member")
@monitor_api_performance("remove_project_member")
async def remove_project_member(
    project_id: int,
    user_id: int,
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    project_member_service: ProjectMemberService = Depends(get_project_scoped_member_service),
) -> Response:
    """Remove a user from a project.

    Args:
        project_id: Unique project identifier
        user_id: Unique user identifier
        current_user: Current authenticated user
        project_member_service: Project member service dependency

    Returns:
        JSONResponse: Empty response with 204 status

    Raises:
        HTTPException: If removal fails due to project/user not found
    """
    logger.info(f"Removing member from project: {project_id}")
    logger.debug(f"Project member removal requested by user: {current_user.get('id')}")

    try:
        await project_member_service.remove_member_from_project(project_id, user_id)
        logger.info(f"Member removed successfully from project: {project_id}")
        return Response(status_code=status.HTTP_204_NO_CONTENT)

    except NotFoundError as e:
        logger.warning(f"Project or user not found: {e}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))


@router.put(
    "/members/{member_id}",
    response_model=ProjectMemberReadSchema,
    status_code=status.HTTP_200_OK,
    summary="Update a project member's role or status",
    description="Update a project member's role, status, or expiration date",
    operation_id="updateProjectMember",
    responses={
        status.HTTP_200_OK: {
            "description": "Member updated successfully",
            "model": ProjectMemberReadSchema,
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
        status.HTTP_400_BAD_REQUEST: {
            "description": "Invalid update data",
            "model": ErrorResponseSchema,
        },
        status.HTTP_404_NOT_FOUND: {
            "description": "Member not found",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("update_project_member")
@monitor_api_performance("update_project_member")
async def update_project_member(
    member_id: int,
    update_data: ProjectMemberUpdateSchema,
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    project_member_service: ProjectMemberService = Depends(get_project_scoped_member_service),
) -> ProjectMemberReadSchema:
    """Update a project member's role, status, or expiration date.

    Args:
        member_id: Unique project member identifier
        update_data: Project member update data
        current_user: Current authenticated user
        project_member_service: Project member service dependency

    Returns:
        ProjectMemberReadSchema: Updated project member data

    Raises:
        HTTPException: If update fails
    """
    logger.info(f"Updating member in project: {member_id}")
    logger.debug(f"Project member update requested by user: {current_user.get('id')}")

    try:
        updated_member = await project_member_service.update_project_member(member_id, update_data)
        logger.info(f"Member updated successfully in project: {member_id}")
        return updated_member

    except NotFoundError as e:
        logger.warning(f"Member not found: {member_id}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))


@router.get(
    "/{project_id}/members",
    response_model=List[ProjectMemberReadSchema],
    status_code=status.HTTP_200_OK,
    summary="List all members of a project",
    description="List all members of a project with pagination",
    operation_id="listProjectMembers",
    responses={
        status.HTTP_200_OK: {
            "description": "Members retrieved successfully",
            "model": List[ProjectMemberReadSchema],
        },
        status.HTTP_400_BAD_REQUEST: {
            "description": "Invalid pagination parameters",
            "model": ErrorResponseSchema,
        },
        status.HTTP_404_NOT_FOUND: {
            "description": "Project not found",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("list_project_members")
@monitor_api_performance("list_project_members")
async def list_project_members(
    project_id: int,
    skip: int = Query(0, ge=0, description="Number of members to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of members"),
    project_member_service: ProjectMemberService = Depends(get_project_scoped_member_service),
) -> List[ProjectMemberReadSchema]:
    """Retrieve a list of all members for a specific project.

    Args:
        project_id: Unique project identifier
        skip: Number of members to skip
        limit: Maximum number of members to retrieve
        project_member_service: Project member service dependency

    Returns:
        List[ProjectMemberReadSchema]: List of project members
    """
    logger.debug(f"Listing members for project: {project_id}")

    try:
        members = await project_member_service.list_project_members(project_id, skip, limit)
        logger.debug(f"Retrieved {len(members)} members for project: {project_id}")
        return members

    except NotFoundError as e:
        logger.warning(f"Project not found: {project_id}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
