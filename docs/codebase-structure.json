{"Type": "Folder", "Name": "ued", "Path": ".", "Children": [{"Type": "File", "Name": ".env", "Path": "./.env"}, {"Type": "Folder", "Name": "cad-integrator-service", "Path": "./cad-integrator-service", "Children": [{"Type": "File", "Name": "Dockerfile", "Path": "./cad-integrator-service/Dockerfile"}, {"Type": "File", "Name": "README.md", "Path": "./cad-integrator-service/README.md"}, {"Type": "Folder", "Name": "src", "Path": "./cad-integrator-service/src", "Children": [{"Type": "Folder", "Name": "Controllers", "Path": "./cad-integrator-service/src/Controllers", "Children": [{"Type": "File", "Name": "CadController.cs", "Path": "./cad-integrator-service/src/Controllers/CadController.cs"}]}, {"Type": "Folder", "Name": "Services", "Path": "./cad-integrator-service/src/Services", "Children": [{"Type": "File", "Name": "AutoCADService.cs", "Path": "./cad-integrator-service/src/Services/AutoCADService.cs"}]}, {"Type": "File", "Name": "ultimate_electrical_designer.CadIntegrator.csproj", "Path": "./cad-integrator-service/src/ultimate_electrical_designer.CadIntegrator.csproj"}]}]}, {"Type": "Folder", "Name": "client", "Path": "./client", "Children": [{"Type": "Folder", "Name": "docs", "Path": "./client/docs", "Children": [{"Type": "File", "Name": "test-suite-resolution.md", "Path": "./client/docs/test-suite-resolution.md"}]}, {"Type": "File", "Name": "DOMAIN_MIGRATION_ROADMAP.md", "Path": "./client/DOMAIN_MIGRATION_ROADMAP.md"}, {"Type": "File", "Name": "playwright.config.ts", "Path": "./client/playwright.config.ts", "comments": "@see https://playwright.dev/docs/test-configuration"}, {"Type": "Folder", "Name": "src", "Path": "./client/src", "Children": [{"Type": "Folder", "Name": "app", "Path": "./client/src/app", "Children": [{"Type": "File", "Name": "layout.tsx", "Path": "./client/src/app/layout.tsx"}, {"Type": "File", "Name": "page.tsx", "Path": "./client/src/app/page.tsx"}, {"Type": "Folder", "Name": "providers", "Path": "./client/src/app/providers", "Children": [{"Type": "File", "Name": "query-provider.tsx", "Path": "./client/src/app/providers/query-provider.tsx"}]}, {"Type": "File", "Name": "providers.tsx", "Path": "./client/src/app/providers.tsx"}]}, {"Type": "Folder", "Name": "components", "Path": "./client/src/components", "Children": [{"Type": "File", "Name": "active-theme.tsx", "Path": "./client/src/components/active-theme.tsx"}, {"Type": "Folder", "Name": "auth", "Path": "./client/src/components/auth", "Children": [{"Type": "File", "Name": "RoleManagement.tsx", "Path": "./client/src/components/auth/RoleManagement.tsx", "comments": "Role Management Component"}, {"Type": "File", "Name": "RouteGuard.tsx", "Path": "./client/src/components/auth/RouteGuard.tsx", "comments": "Route Guard Component"}]}, {"Type": "Folder", "Name": "dashboard", "Path": "./client/src/components/dashboard", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/components/dashboard/__tests__", "Children": [{"Type": "File", "Name": "CriticalAlerts.test.tsx", "Path": "./client/src/components/dashboard/__tests__/CriticalAlerts.test.tsx"}, {"Type": "File", "Name": "ProblemSearch.test.tsx", "Path": "./client/src/components/dashboard/__tests__/ProblemSearch.test.tsx"}, {"Type": "File", "Name": "QuickLinks.test.tsx", "Path": "./client/src/components/dashboard/__tests__/QuickLinks.test.tsx"}, {"Type": "File", "Name": "RecentActivityTable.test.tsx", "Path": "./client/src/components/dashboard/__tests__/RecentActivityTable.test.tsx"}, {"Type": "File", "Name": "SystemHealthCard.test.tsx", "Path": "./client/src/components/dashboard/__tests__/SystemHealthCard.test.tsx"}]}, {"Type": "Folder", "Name": "components", "Path": "./client/src/components/dashboard/components", "Children": [{"Type": "File", "Name": "CriticalAlerts.tsx", "Path": "./client/src/components/dashboard/components/CriticalAlerts.tsx"}, {"Type": "File", "Name": "ProblemSearch.tsx", "Path": "./client/src/components/dashboard/components/ProblemSearch.tsx"}, {"Type": "File", "Name": "QuickLinks.tsx", "Path": "./client/src/components/dashboard/components/QuickLinks.tsx"}, {"Type": "File", "Name": "RecentActivityTable.tsx", "Path": "./client/src/components/dashboard/components/RecentActivityTable.tsx"}, {"Type": "File", "Name": "SystemHealthCard.tsx", "Path": "./client/src/components/dashboard/components/SystemHealthCard.tsx"}]}]}, {"Type": "Folder", "Name": "layout", "Path": "./client/src/components/layout", "Children": [{"Type": "File", "Name": "Footer.tsx", "Path": "./client/src/components/layout/Footer.tsx"}, {"Type": "File", "Name": "Header.tsx", "Path": "./client/src/components/layout/Header.tsx"}]}, {"Type": "Folder", "Name": "navigation", "Path": "./client/src/components/navigation", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/components/navigation/__tests__", "Children": [{"Type": "File", "Name": "app-sidebar.test.tsx", "Path": "./client/src/components/navigation/__tests__/app-sidebar.test.tsx"}]}, {"Type": "File", "Name": "app-sidebar.tsx", "Path": "./client/src/components/navigation/app-sidebar.tsx"}, {"Type": "File", "Name": "nav-items.ts", "Path": "./client/src/components/navigation/nav-items.ts"}, {"Type": "File", "Name": "nav-list-item.tsx", "Path": "./client/src/components/navigation/nav-list-item.tsx"}, {"Type": "File", "Name": "nav-user.tsx", "Path": "./client/src/components/navigation/nav-user.tsx"}]}, {"Type": "File", "Name": "tailwind-indicator.tsx", "Path": "./client/src/components/tailwind-indicator.tsx"}, {"Type": "Folder", "Name": "tasks", "Path": "./client/src/components/tasks", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/components/tasks/__tests__", "Children": [{"Type": "File", "Name": "TaskCard.test.tsx", "Path": "./client/src/components/tasks/__tests__/TaskCard.test.tsx", "comments": "TaskCard Unit Tests"}, {"Type": "File", "Name": "TaskForm.integration.test.tsx", "Path": "./client/src/components/tasks/__tests__/TaskForm.integration.test.tsx", "comments": "Integration tests for TaskForm component"}, {"Type": "File", "Name": "TaskList.integration.test.tsx", "Path": "./client/src/components/tasks/__tests__/TaskList.integration.test.tsx", "comments": "Integration tests for TaskList component"}, {"Type": "File", "Name": "TaskStatusBadge.test.tsx", "Path": "./client/src/components/tasks/__tests__/TaskStatusBadge.test.tsx", "comments": "TaskStatusBadge Unit Tests"}]}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/components/tasks/index.ts", "comments": "Task Components Export Index"}, {"Type": "File", "Name": "TaskCard.tsx", "Path": "./client/src/components/tasks/TaskCard.tsx", "comments": "TaskCard Component"}, {"Type": "File", "Name": "TaskForm.tsx", "Path": "./client/src/components/tasks/TaskForm.tsx", "comments": "TaskForm Component"}, {"Type": "File", "Name": "TaskList.tsx", "Path": "./client/src/components/tasks/TaskList.tsx", "comments": "TaskList Component"}, {"Type": "File", "Name": "TaskStatusBadge.tsx", "Path": "./client/src/components/tasks/TaskStatusBadge.tsx", "comments": "TaskStatusBadge Component"}]}, {"Type": "File", "Name": "theme-provider.tsx", "Path": "./client/src/components/theme-provider.tsx"}, {"Type": "Folder", "Name": "ui", "Path": "./client/src/components/ui", "Children": [{"Type": "File", "Name": "accordion.tsx", "Path": "./client/src/components/ui/accordion.tsx"}, {"Type": "File", "Name": "alert-dialog.tsx", "Path": "./client/src/components/ui/alert-dialog.tsx"}, {"Type": "File", "Name": "alert.tsx", "Path": "./client/src/components/ui/alert.tsx"}, {"Type": "File", "Name": "avatar.tsx", "Path": "./client/src/components/ui/avatar.tsx"}, {"Type": "File", "Name": "badge.tsx", "Path": "./client/src/components/ui/badge.tsx"}, {"Type": "File", "Name": "breadcrumb.tsx", "Path": "./client/src/components/ui/breadcrumb.tsx"}, {"Type": "File", "Name": "button.tsx", "Path": "./client/src/components/ui/button.tsx"}, {"Type": "File", "Name": "calendar-rac.tsx", "Path": "./client/src/components/ui/calendar-rac.tsx"}, {"Type": "File", "Name": "calendar.tsx", "Path": "./client/src/components/ui/calendar.tsx"}, {"Type": "File", "Name": "card.tsx", "Path": "./client/src/components/ui/card.tsx"}, {"Type": "File", "Name": "checkbox-tree.tsx", "Path": "./client/src/components/ui/checkbox-tree.tsx", "comments": "IMPORTANT: This component was built for demo purposes only and has not been tested in production."}, {"Type": "File", "Name": "checkbox.tsx", "Path": "./client/src/components/ui/checkbox.tsx"}, {"Type": "File", "Name": "collapsible.tsx", "Path": "./client/src/components/ui/collapsible.tsx"}, {"Type": "File", "Name": "command.tsx", "Path": "./client/src/components/ui/command.tsx"}, {"Type": "File", "Name": "cropper.tsx", "Path": "./client/src/components/ui/cropper.tsx"}, {"Type": "File", "Name": "datefield-rac.tsx", "Path": "./client/src/components/ui/datefield-rac.tsx"}, {"Type": "File", "Name": "dialog.tsx", "Path": "./client/src/components/ui/dialog.tsx"}, {"Type": "File", "Name": "dropdown-menu.tsx", "Path": "./client/src/components/ui/dropdown-menu.tsx"}, {"Type": "File", "Name": "form.tsx", "Path": "./client/src/components/ui/form.tsx", "comments": "Form UI Components for react-hook-form integration"}, {"Type": "File", "Name": "hover-card.tsx", "Path": "./client/src/components/ui/hover-card.tsx"}, {"Type": "File", "Name": "input.tsx", "Path": "./client/src/components/ui/input.tsx"}, {"Type": "File", "Name": "label.tsx", "Path": "./client/src/components/ui/label.tsx"}, {"Type": "File", "Name": "multiselect.tsx", "Path": "./client/src/components/ui/multiselect.tsx", "comments": "fixed option that can't be removed."}, {"Type": "File", "Name": "navigation-menu.tsx", "Path": "./client/src/components/ui/navigation-menu.tsx"}, {"Type": "File", "Name": "pagination.tsx", "Path": "./client/src/components/ui/pagination.tsx"}, {"Type": "File", "Name": "popover.tsx", "Path": "./client/src/components/ui/popover.tsx"}, {"Type": "File", "Name": "progress.tsx", "Path": "./client/src/components/ui/progress.tsx"}, {"Type": "File", "Name": "radio-group.tsx", "Path": "./client/src/components/ui/radio-group.tsx"}, {"Type": "File", "Name": "resizable.tsx", "Path": "./client/src/components/ui/resizable.tsx"}, {"Type": "File", "Name": "scroll-area.tsx", "Path": "./client/src/components/ui/scroll-area.tsx"}, {"Type": "File", "Name": "select-native.tsx", "Path": "./client/src/components/ui/select-native.tsx"}, {"Type": "File", "Name": "select.tsx", "Path": "./client/src/components/ui/select.tsx"}, {"Type": "File", "Name": "separator.tsx", "Path": "./client/src/components/ui/separator.tsx"}, {"Type": "File", "Name": "sheet.tsx", "Path": "./client/src/components/ui/sheet.tsx"}, {"Type": "File", "Name": "sidebar.tsx", "Path": "./client/src/components/ui/sidebar.tsx"}, {"Type": "File", "Name": "skeleton.tsx", "Path": "./client/src/components/ui/skeleton.tsx"}, {"Type": "File", "Name": "slider.tsx", "Path": "./client/src/components/ui/slider.tsx"}, {"Type": "File", "Name": "sonner.tsx", "Path": "./client/src/components/ui/sonner.tsx"}, {"Type": "File", "Name": "stepper.tsx", "Path": "./client/src/components/ui/stepper.tsx"}, {"Type": "File", "Name": "switch.tsx", "Path": "./client/src/components/ui/switch.tsx"}, {"Type": "File", "Name": "table.tsx", "Path": "./client/src/components/ui/table.tsx"}, {"Type": "File", "Name": "tabs.tsx", "Path": "./client/src/components/ui/tabs.tsx"}, {"Type": "File", "Name": "textarea.tsx", "Path": "./client/src/components/ui/textarea.tsx"}, {"Type": "File", "Name": "timeline.tsx", "Path": "./client/src/components/ui/timeline.tsx"}, {"Type": "File", "Name": "toast.tsx", "Path": "./client/src/components/ui/toast.tsx"}, {"Type": "File", "Name": "toaster.tsx", "Path": "./client/src/components/ui/toaster.tsx"}, {"Type": "File", "Name": "toggle-group.tsx", "Path": "./client/src/components/ui/toggle-group.tsx"}, {"Type": "File", "Name": "toggle.tsx", "Path": "./client/src/components/ui/toggle.tsx"}, {"Type": "File", "Name": "tooltip.tsx", "Path": "./client/src/components/ui/tooltip.tsx"}, {"Type": "File", "Name": "tree.tsx", "Path": "./client/src/components/ui/tree.tsx"}]}]}, {"Type": "Folder", "Name": "core", "Path": "./client/src/core", "Children": [{"Type": "Folder", "Name": "caching", "Path": "./client/src/core/caching", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/core/caching/__tests__", "Children": [{"Type": "File", "Name": "cache_provider.integration.test.tsx", "Path": "./client/src/core/caching/__tests__/cache_provider.integration.test.tsx", "comments": "Integration tests for CacheProvider with real browser persistence"}, {"Type": "File", "Name": "cache_provider.test.tsx", "Path": "./client/src/core/caching/__tests__/cache_provider.test.tsx", "comments": "Integration tests for CacheProvider with IndexedDBPersister"}, {"Type": "File", "Name": "cache_provider.validation.test.tsx", "Path": "./client/src/core/caching/__tests__/cache_provider.validation.test.tsx", "comments": "Validation tests for Work Batch 7.4 and 7.5 completion"}, {"Type": "File", "Name": "indexed_db_persister.integration.test.ts", "Path": "./client/src/core/caching/__tests__/indexed_db_persister.integration.test.ts", "comments": "Integration tests for IndexedDBPersister with mocked idb library"}, {"Type": "File", "Name": "indexed_db_persister.test.ts", "Path": "./client/src/core/caching/__tests__/indexed_db_persister.test.ts", "comments": "Unit tests for IndexedDBPersister"}, {"Type": "File", "Name": "indexed_db_persister.validation.test.ts", "Path": "./client/src/core/caching/__tests__/indexed_db_persister.validation.test.ts", "comments": "Validation tests for IndexedDBPersister Work Batch 7.1 completion"}, {"Type": "File", "Name": "sync_manager.test.ts", "Path": "./client/src/core/caching/__tests__/sync_manager.test.ts", "comments": "Unit tests for SyncManager"}, {"Type": "File", "Name": "sync_manager.validation.test.tsx", "Path": "./client/src/core/caching/__tests__/sync_manager.validation.test.tsx", "comments": "Validation tests for Work Batch 7.6 and 7.7 completion"}]}, {"Type": "File", "Name": "CACHE_DESIGN.md", "Path": "./client/src/core/caching/CACHE_DESIGN.md"}, {"Type": "File", "Name": "cache_provider.tsx", "Path": "./client/src/core/caching/cache_provider.tsx", "comments": "CacheProvider - React Query integration with IndexedDBPersister"}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/core/caching/index.ts", "comments": "Core caching functionality for client-side persistence"}, {"Type": "File", "Name": "indexed_db_persister.ts", "Path": "./client/src/core/caching/indexed_db_persister.ts", "comments": "IndexedDBPersister - Custom persister for React Query using IndexedDB"}, {"Type": "File", "Name": "sync_manager.ts", "Path": "./client/src/core/caching/sync_manager.ts", "comments": "SyncManager - Offline mutation synchronization service"}]}]}, {"Type": "Folder", "Name": "hooks", "Path": "./client/src/hooks", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/hooks/__tests__", "Children": [{"Type": "File", "Name": "useAuth.test.tsx", "Path": "./client/src/hooks/__tests__/useAuth.test.tsx"}, {"Type": "File", "Name": "useOfflineMutation.test.tsx", "Path": "./client/src/hooks/__tests__/useOfflineMutation.test.tsx", "comments": "Unit tests for useOfflineMutation hook"}]}, {"Type": "Folder", "Name": "api", "Path": "./client/src/hooks/api", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/hooks/api/__tests__", "Children": [{"Type": "File", "Name": "useAudit.test.tsx", "Path": "./client/src/hooks/api/__tests__/useAudit.test.tsx", "comments": "Unit tests for useAudit hooks"}, {"Type": "File", "Name": "useRbac.test.tsx", "Path": "./client/src/hooks/api/__tests__/useRbac.test.tsx", "comments": "Unit tests for useRbac hooks"}]}, {"Type": "File", "Name": "useAudit.ts", "Path": "./client/src/hooks/api/useAudit.ts", "comments": "React Query hooks for audit trail operations"}, {"Type": "File", "Name": "useAuth.ts", "Path": "./client/src/hooks/api/useAuth.ts", "comments": "React Query hooks for authentication"}, {"Type": "File", "Name": "useRbac.ts", "Path": "./client/src/hooks/api/useRbac.ts", "comments": "React Query hooks for RBAC operations"}, {"Type": "File", "Name": "useTasks.ts", "Path": "./client/src/hooks/api/useTasks.ts", "comments": "React Query hooks for task management"}, {"Type": "File", "Name": "useUsers.ts", "Path": "./client/src/hooks/api/useUsers.ts", "comments": "React Query hooks for user management"}]}, {"Type": "File", "Name": "useAuth.ts", "Path": "./client/src/hooks/useAuth.ts", "comments": "Main authentication hook that combines Zustand store and React Query"}, {"Type": "File", "Name": "useCharacterLimit.ts", "Path": "./client/src/hooks/useCharacterLimit.ts"}, {"Type": "File", "Name": "useCopyToClipboard.ts", "Path": "./client/src/hooks/useCopyToClipboard.ts"}, {"Type": "File", "Name": "useFileUpload.ts", "Path": "./client/src/hooks/useFileUpload.ts"}, {"Type": "File", "Name": "useLayout.tsx", "Path": "./client/src/hooks/useLayout.tsx"}, {"Type": "File", "Name": "useOfflineMutation.ts", "Path": "./client/src/hooks/useOfflineMutation.ts", "comments": "useOfflineMutation Hook - Offline-first mutation handling"}, {"Type": "File", "Name": "usePagination.ts", "Path": "./client/src/hooks/usePagination.ts"}, {"Type": "File", "Name": "useSliderWithInput.ts", "Path": "./client/src/hooks/useSliderWithInput.ts"}, {"Type": "File", "Name": "useTaskWebSocket.ts", "Path": "./client/src/hooks/useTaskWebSocket.ts", "comments": "Task WebSocket Hook"}, {"Type": "File", "Name": "useToast.ts", "Path": "./client/src/hooks/useToast.ts"}]}, {"Type": "Folder", "Name": "lib", "Path": "./client/src/lib", "Children": [{"Type": "Folder", "Name": "api", "Path": "./client/src/lib/api", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/lib/api/__tests__", "Children": [{"Type": "File", "Name": "client.test.ts", "Path": "./client/src/lib/api/__tests__/client.test.ts"}]}, {"Type": "File", "Name": "audit.ts", "Path": "./client/src/lib/api/audit.ts", "comments": "Audit Trail API Client for Ultimate Electrical Designer"}, {"Type": "File", "Name": "client.ts", "Path": "./client/src/lib/api/client.ts", "comments": "API Client for Ultimate Electrical Designer"}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/lib/api/index.ts", "comments": "API Client Module - Main Entry Point"}, {"Type": "File", "Name": "rbac.ts", "Path": "./client/src/lib/api/rbac.ts", "comments": "RBAC API Client for Ultimate Electrical Designer"}]}, {"Type": "Folder", "Name": "auth", "Path": "./client/src/lib/auth", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/lib/auth/__tests__", "Children": [{"Type": "File", "Name": "tokenManager.test.ts", "Path": "./client/src/lib/auth/__tests__/tokenManager.test.ts"}]}, {"Type": "File", "Name": "tokenManager.ts", "Path": "./client/src/lib/auth/tokenManager.ts", "comments": "JWT Token Management Utilities"}]}, {"Type": "File", "Name": "config.ts", "Path": "./client/src/lib/config.ts"}, {"Type": "File", "Name": "fonts.ts", "Path": "./client/src/lib/fonts.ts"}, {"Type": "Folder", "Name": "store", "Path": "./client/src/lib/store", "Children": [{"Type": "File", "Name": "index.ts", "Path": "./client/src/lib/store/index.ts"}]}, {"Type": "File", "Name": "utils.ts", "Path": "./client/src/lib/utils.ts"}, {"Type": "Folder", "Name": "websocket", "Path": "./client/src/lib/websocket", "Children": [{"Type": "File", "Name": "taskWebSocketClient.ts", "Path": "./client/src/lib/websocket/taskWebSocketClient.ts", "comments": "Task WebSocket Client"}]}]}, {"Type": "Folder", "Name": "modules", "Path": "./client/src/modules", "Children": [{"Type": "Folder", "Name": "components", "Path": "./client/src/modules/components", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/modules/components/__tests__", "Children": [{"Type": "Folder", "Name": "integration", "Path": "./client/src/modules/components/__tests__/integration", "Children": [{"Type": "File", "Name": "ComponentManagement.test.tsx", "Path": "./client/src/modules/components/__tests__/integration/ComponentManagement.test.tsx", "comments": "Component Management Integration Tests"}]}, {"Type": "Folder", "Name": "setup", "Path": "./client/src/modules/components/__tests__/setup", "Children": [{"Type": "File", "Name": "test-config.ts", "Path": "./client/src/modules/components/__tests__/setup/test-config.ts", "comments": "Test Configuration"}]}]}, {"Type": "Folder", "Name": "api", "Path": "./client/src/modules/components/api", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/modules/components/api/__tests__", "Children": [{"Type": "File", "Name": "componentApi.test.ts", "Path": "./client/src/modules/components/api/__tests__/componentApi.test.ts", "comments": "Component API Client Tests"}, {"Type": "File", "Name": "componentMutations.test.tsx", "Path": "./client/src/modules/components/api/__tests__/componentMutations.test.tsx", "comments": "Component React Query Mutation Hooks Tests"}, {"Type": "File", "Name": "componentQueries.test.tsx", "Path": "./client/src/modules/components/api/__tests__/componentQueries.test.tsx", "comments": "Component React Query Hooks Tests"}, {"Type": "File", "Name": "domainAdapter.test.ts", "Path": "./client/src/modules/components/api/__tests__/domainAdapter.test.ts", "comments": "Domain Adapter Tests"}]}, {"Type": "File", "Name": "componentApi.ts", "Path": "./client/src/modules/components/api/componentApi.ts", "comments": "Component Management API Client"}, {"Type": "File", "Name": "componentMutations.ts", "Path": "./client/src/modules/components/api/componentMutations.ts", "comments": "React Query mutations for component management"}, {"Type": "File", "Name": "componentQueries.ts", "Path": "./client/src/modules/components/api/componentQueries.ts", "comments": "Enhanced React Query hooks for component management queries"}, {"Type": "File", "Name": "domainAdapter.ts", "Path": "./client/src/modules/components/api/domainAdapter.ts", "comments": "Domain Adapter for Component API"}, {"Type": "File", "Name": "domainComponentApi.ts", "Path": "./client/src/modules/components/api/domainComponentApi.ts", "comments": "Domain-Aware Component API Service"}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/modules/components/api/index.ts", "comments": "Re-exports API wrappers and types specific to the 'components' domain."}]}, {"Type": "Folder", "Name": "components", "Path": "./client/src/modules/components/components", "Children": [{"Type": "Folder", "Name": "atoms", "Path": "./client/src/modules/components/components/atoms", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/modules/components/components/atoms/__tests__", "Children": [{"Type": "File", "Name": "ComponentBadge.test.tsx", "Path": "./client/src/modules/components/components/atoms/__tests__/ComponentBadge.test.tsx", "comments": "ComponentBadge Unit Tests"}]}, {"Type": "File", "Name": "ComponentBadge.tsx", "Path": "./client/src/modules/components/components/atoms/ComponentBadge.tsx", "comments": "ComponentBadge Atom"}, {"Type": "File", "Name": "ComponentIcon.tsx", "Path": "./client/src/modules/components/components/atoms/ComponentIcon.tsx", "comments": "ComponentIcon Atom"}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/modules/components/components/atoms/index.ts", "comments": "Atoms Index"}]}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/modules/components/components/index.ts", "comments": "Components Index"}, {"Type": "Folder", "Name": "molecules", "Path": "./client/src/modules/components/components/molecules", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/modules/components/components/molecules/__tests__", "Children": [{"Type": "File", "Name": "ComponentCard.test.tsx", "Path": "./client/src/modules/components/components/molecules/__tests__/ComponentCard.test.tsx", "comments": "ComponentCard Unit Tests"}, {"Type": "File", "Name": "ComponentFilters.test.tsx", "Path": "./client/src/modules/components/components/molecules/__tests__/ComponentFilters.test.tsx", "comments": "ComponentFilters Unit Tests"}, {"Type": "File", "Name": "ComponentSearch.test.tsx", "Path": "./client/src/modules/components/components/molecules/__tests__/ComponentSearch.test.tsx", "comments": "ComponentSearch Unit Tests"}]}, {"Type": "File", "Name": "ComponentCard.tsx", "Path": "./client/src/modules/components/components/molecules/ComponentCard.tsx", "comments": "ComponentCard Molecule"}, {"Type": "File", "Name": "ComponentFilters.tsx", "Path": "./client/src/modules/components/components/molecules/ComponentFilters.tsx", "comments": "ComponentFilters Molecule"}, {"Type": "File", "Name": "ComponentSearchBar.tsx", "Path": "./client/src/modules/components/components/molecules/ComponentSearchBar.tsx", "comments": "ComponentSearchBar Molecule"}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/modules/components/components/molecules/index.ts", "comments": "Molecules Index"}]}, {"Type": "Folder", "Name": "organisms", "Path": "./client/src/modules/components/components/organisms", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/modules/components/components/organisms/__tests__", "Children": [{"Type": "File", "Name": "BulkOperations.test.tsx", "Path": "./client/src/modules/components/components/organisms/__tests__/BulkOperations.test.tsx", "comments": "BulkOperations Component Tests"}, {"Type": "File", "Name": "ComponentDetails.test.tsx", "Path": "./client/src/modules/components/components/organisms/__tests__/ComponentDetails.test.tsx", "comments": "ComponentDetails Component Tests"}, {"Type": "File", "Name": "ComponentForm.test.tsx", "Path": "./client/src/modules/components/components/organisms/__tests__/ComponentForm.test.tsx", "comments": "ComponentForm Component Tests"}, {"Type": "File", "Name": "ComponentList.domain-integration.test.tsx", "Path": "./client/src/modules/components/components/organisms/__tests__/ComponentList.domain-integration.test.tsx", "comments": "ComponentList Domain Integration Tests"}, {"Type": "File", "Name": "ComponentList.test.tsx", "Path": "./client/src/modules/components/components/organisms/__tests__/ComponentList.test.tsx", "comments": "ComponentList Unit Tests - STUB IMPLEMENTATION"}, {"Type": "File", "Name": "ComponentStats.test.tsx", "Path": "./client/src/modules/components/components/organisms/__tests__/ComponentStats.test.tsx", "comments": "ComponentStats Component Tests"}, {"Type": "File", "Name": "DomainComponentForm.test.tsx", "Path": "./client/src/modules/components/components/organisms/__tests__/DomainComponentForm.test.tsx", "comments": "Domain Component Form Tests"}]}, {"Type": "File", "Name": "BulkOperations.tsx", "Path": "./client/src/modules/components/components/organisms/BulkOperations.tsx", "comments": "BulkOperations - Component for handling bulk operations on components"}, {"Type": "File", "Name": "BulkOperationsPanel.tsx", "Path": "./client/src/modules/components/components/organisms/BulkOperationsPanel.tsx", "comments": "BulkOperationsPanel Organism"}, {"Type": "File", "Name": "ComponentDetails.tsx", "Path": "./client/src/modules/components/components/organisms/ComponentDetails.tsx", "comments": "ComponentDetails - Detailed view component for component information"}, {"Type": "File", "Name": "ComponentForm.tsx", "Path": "./client/src/modules/components/components/organisms/ComponentForm.tsx", "comments": "ComponentForm - Form component for creating and editing components"}, {"Type": "File", "Name": "ComponentList.tsx", "Path": "./client/src/modules/components/components/organisms/ComponentList.tsx", "comments": "ComponentList Organism"}, {"Type": "File", "Name": "ComponentStats.tsx", "Path": "./client/src/modules/components/components/organisms/ComponentStats.tsx", "comments": "ComponentStats - Statistics dashboard for component management"}, {"Type": "File", "Name": "DomainComponentForm.tsx", "Path": "./client/src/modules/components/components/organisms/DomainComponentForm.tsx", "comments": "Domain-Integrated Component Form"}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/modules/components/components/organisms/index.ts", "comments": "Organisms Index"}]}]}, {"Type": "Folder", "Name": "docs", "Path": "./client/src/modules/components/docs", "Children": [{"Type": "File", "Name": "IMPLEMENTATION.md", "Path": "./client/src/modules/components/docs/IMPLEMENTATION.md"}]}, {"Type": "Folder", "Name": "domain", "Path": "./client/src/modules/components/domain", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/modules/components/domain/__tests__", "Children": [{"Type": "File", "Name": "domain-integration.test.ts", "Path": "./client/src/modules/components/domain/__tests__/domain-integration.test.ts", "comments": "Domain Integration Tests"}]}, {"Type": "Folder", "Name": "aggregates", "Path": "./client/src/modules/components/domain/aggregates", "Children": [{"Type": "File", "Name": "ComponentCatalog.ts", "Path": "./client/src/modules/components/domain/aggregates/ComponentCatalog.ts", "comments": "ComponentCatalog Aggregate"}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/modules/components/domain/aggregates/index.ts", "comments": "Domain Aggregates - Component Catalog Context"}]}, {"Type": "Folder", "Name": "domain-services", "Path": "./client/src/modules/components/domain/domain-services", "Children": [{"Type": "File", "Name": "ComponentValidationService.ts", "Path": "./client/src/modules/components/domain/domain-services/ComponentValidationService.ts", "comments": "ComponentValidationService"}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/modules/components/domain/domain-services/index.ts", "comments": "Domain Services - Component Catalog Context"}]}, {"Type": "Folder", "Name": "entities", "Path": "./client/src/modules/components/domain/entities", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/modules/components/domain/entities/__tests__", "Children": [{"Type": "File", "Name": "Component.test.ts", "Path": "./client/src/modules/components/domain/entities/__tests__/Component.test.ts", "comments": "Component Entity Tests"}]}, {"Type": "File", "Name": "Component.ts", "Path": "./client/src/modules/components/domain/entities/Component.ts", "comments": "Component Entity - Component Catalog Domain"}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/modules/components/domain/entities/index.ts", "comments": "Domain Entities - Component Catalog Context"}]}, {"Type": "Folder", "Name": "errors", "Path": "./client/src/modules/components/domain/errors", "Children": [{"Type": "File", "Name": "ComponentDomainError.ts", "Path": "./client/src/modules/components/domain/errors/ComponentDomainError.ts", "comments": "Component Domain Error"}]}, {"Type": "Folder", "Name": "events", "Path": "./client/src/modules/components/domain/events", "Children": [{"Type": "File", "Name": "index.ts", "Path": "./client/src/modules/components/domain/events/index.ts", "comments": "Domain Events - Component Catalog Context"}]}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/modules/components/domain/index.ts", "comments": "Domain Layer - Component Catalog Context"}, {"Type": "Folder", "Name": "repositories", "Path": "./client/src/modules/components/domain/repositories", "Children": [{"Type": "File", "Name": "IComponentCatalogRepository.ts", "Path": "./client/src/modules/components/domain/repositories/IComponentCatalogRepository.ts", "comments": "Component Catalog Repository Interface"}, {"Type": "File", "Name": "IComponentRepository.ts", "Path": "./client/src/modules/components/domain/repositories/IComponentRepository.ts", "comments": "Component Repository Interface"}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/modules/components/domain/repositories/index.ts", "comments": "Repository Interfaces - Component Catalog Context"}]}, {"Type": "Folder", "Name": "specifications", "Path": "./client/src/modules/components/domain/specifications", "Children": [{"Type": "File", "Name": "index.ts", "Path": "./client/src/modules/components/domain/specifications/index.ts", "comments": "Domain Specifications - Component Catalog Context"}]}, {"Type": "Folder", "Name": "value-objects", "Path": "./client/src/modules/components/domain/value-objects", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/modules/components/domain/value-objects/__tests__", "Children": [{"Type": "File", "Name": "ComponentStatus.test.ts", "Path": "./client/src/modules/components/domain/value-objects/__tests__/ComponentStatus.test.ts", "comments": "ComponentStatus Value Object Tests"}]}, {"Type": "File", "Name": "ComponentStatus.ts", "Path": "./client/src/modules/components/domain/value-objects/ComponentStatus.ts", "comments": "ComponentStatus Value Object"}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/modules/components/domain/value-objects/index.ts", "comments": "Domain Value Objects - Component Catalog Context"}, {"Type": "File", "Name": "PriceValue.ts", "Path": "./client/src/modules/components/domain/value-objects/PriceValue.ts", "comments": "PriceValue Value Object"}, {"Type": "File", "Name": "Specifications.ts", "Path": "./client/src/modules/components/domain/value-objects/Specifications.ts", "comments": "Specifications Value Object"}]}]}, {"Type": "File", "Name": "DOMAIN_INTEGRATION_GUIDE.md", "Path": "./client/src/modules/components/DOMAIN_INTEGRATION_GUIDE.md"}, {"Type": "File", "Name": "DOMAIN_PERFORMANCE_AUDIT.md", "Path": "./client/src/modules/components/DOMAIN_PERFORMANCE_AUDIT.md"}, {"Type": "Folder", "Name": "hooks", "Path": "./client/src/modules/components/hooks", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/modules/components/hooks/__tests__", "Children": [{"Type": "File", "Name": "useComponentForm.test.tsx", "Path": "./client/src/modules/components/hooks/__tests__/useComponentForm.test.tsx", "comments": "useComponentForm Hook Tests"}, {"Type": "File", "Name": "useComponentStore.test.tsx", "Path": "./client/src/modules/components/hooks/__tests__/useComponentStore.test.tsx", "comments": "useComponentStore Hook Tests"}]}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/modules/components/hooks/index.ts", "comments": "Re-exports custom React Hooks specific to the 'components' domain."}, {"Type": "File", "Name": "useComponentForm.ts", "Path": "./client/src/modules/components/hooks/useComponentForm.ts", "comments": "Component Form Management Hook"}, {"Type": "File", "Name": "useComponentStore.ts", "Path": "./client/src/modules/components/hooks/useComponentStore.ts", "comments": "Enhanced Component Store Hook"}, {"Type": "File", "Name": "useDomainComponentForm.ts", "Path": "./client/src/modules/components/hooks/useDomainComponentForm.ts", "comments": "Domain-Aware Component Form Hook"}, {"Type": "File", "Name": "useDomainComponentHooks.ts", "Path": "./client/src/modules/components/hooks/useDomainComponentHooks.ts", "comments": "Domain-Aware Component Hooks"}, {"Type": "File", "Name": "useDomainComponentStore.ts", "Path": "./client/src/modules/components/hooks/useDomainComponentStore.ts", "comments": "Domain-Aware Component Store Hook"}]}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/modules/components/index.ts", "comments": "Components Module Entry Point"}, {"Type": "File", "Name": "README.md", "Path": "./client/src/modules/components/README.md"}, {"Type": "File", "Name": "types.ts", "Path": "./client/src/modules/components/types.ts", "comments": "Component management domain-specific types"}, {"Type": "Folder", "Name": "utils", "Path": "./client/src/modules/components/utils", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/modules/components/utils/__tests__", "Children": [{"Type": "File", "Name": "utils.test.ts", "Path": "./client/src/modules/components/utils/__tests__/utils.test.ts", "comments": "Component Utilities Tests"}]}, {"Type": "File", "Name": "api.ts", "Path": "./client/src/modules/components/utils/api.ts", "comments": "API Utilities"}, {"Type": "File", "Name": "calculations.ts", "Path": "./client/src/modules/components/utils/calculations.ts", "comments": "Enhanced Calculation Utilities"}, {"Type": "File", "Name": "formatting.ts", "Path": "./client/src/modules/components/utils/formatting.ts", "comments": "Enhanced Formatting Utilities"}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/modules/components/utils/index.ts", "comments": "Enhanced Utilities Index"}, {"Type": "File", "Name": "validation.ts", "Path": "./client/src/modules/components/utils/validation.ts", "comments": "Simplified Validation Utilities"}]}, {"Type": "File", "Name": "utils.ts", "Path": "./client/src/modules/components/utils.ts", "comments": "Component management utility functions"}]}, {"Type": "Folder", "Name": "projects", "Path": "./client/src/modules/projects", "Children": [{"Type": "Folder", "Name": "api", "Path": "./client/src/modules/projects/api", "Children": [{"Type": "File", "Name": "API_INTEGRATION_DOCS.md", "Path": "./client/src/modules/projects/api/API_INTEGRATION_DOCS.md"}, {"Type": "File", "Name": "projectApi.ts", "Path": "./client/src/modules/projects/api/projectApi.ts"}, {"Type": "File", "Name": "projectMemberApi.ts", "Path": "./client/src/modules/projects/api/projectMemberApi.ts"}]}, {"Type": "Folder", "Name": "application", "Path": "./client/src/modules/projects/application", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/modules/projects/application/__tests__", "Children": [{"Type": "File", "Name": "CreateProjectUseCase.test.ts", "Path": "./client/src/modules/projects/application/__tests__/CreateProjectUseCase.test.ts", "comments": "Create Project Use Case Tests"}, {"Type": "File", "Name": "UpdateProjectUseCase.test.ts", "Path": "./client/src/modules/projects/application/__tests__/UpdateProjectUseCase.test.ts", "comments": "Update Project Use Case Tests"}]}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/modules/projects/application/index.ts", "comments": "Projects Application Layer Index"}, {"Type": "File", "Name": "README.md", "Path": "./client/src/modules/projects/application/README.md"}, {"Type": "Folder", "Name": "services", "Path": "./client/src/modules/projects/application/services", "Children": [{"Type": "File", "Name": "ArchiveProjectUseCase.ts", "Path": "./client/src/modules/projects/application/services/ArchiveProjectUseCase.ts", "comments": "Archive Project Use Case"}, {"Type": "File", "Name": "AssignMemberToProjectUseCase.ts", "Path": "./client/src/modules/projects/application/services/AssignMemberToProjectUseCase.ts", "comments": "Assign Member to Project Use Case"}, {"Type": "File", "Name": "ChangeProjectStatusUseCase.ts", "Path": "./client/src/modules/projects/application/services/ChangeProjectStatusUseCase.ts", "comments": "Change Project Status Use Case"}, {"Type": "File", "Name": "CreateProjectUseCase.ts", "Path": "./client/src/modules/projects/application/services/CreateProjectUseCase.ts", "comments": "Create Project Use Case"}, {"Type": "File", "Name": "UpdateProjectUseCase.ts", "Path": "./client/src/modules/projects/application/services/UpdateProjectUseCase.ts", "comments": "Update Project Use Case"}, {"Type": "File", "Name": "ValidateProjectUseCase.ts", "Path": "./client/src/modules/projects/application/services/ValidateProjectUseCase.ts", "comments": "Validate Project Use Case"}]}, {"Type": "Folder", "Name": "types", "Path": "./client/src/modules/projects/application/types", "Children": [{"Type": "File", "Name": "ApplicationErrors.ts", "Path": "./client/src/modules/projects/application/types/ApplicationErrors.ts", "comments": "Application Layer Error Types"}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/modules/projects/application/types/index.ts", "comments": "Application Layer Types Index"}, {"Type": "File", "Name": "ProjectOperationTypes.ts", "Path": "./client/src/modules/projects/application/types/ProjectOperationTypes.ts", "comments": "Application Layer Operation Types"}]}]}, {"Type": "Folder", "Name": "components", "Path": "./client/src/modules/projects/components", "Children": [{"Type": "Folder", "Name": "atoms", "Path": "./client/src/modules/projects/components/atoms", "Children": [{"Type": "File", "Name": "ActionButton.tsx", "Path": "./client/src/modules/projects/components/atoms/ActionButton.tsx", "comments": "Action Button Atom"}, {"Type": "File", "Name": "EmptyState.tsx", "Path": "./client/src/modules/projects/components/atoms/EmptyState.tsx", "comments": "Empty State Atom"}, {"Type": "File", "Name": "FormInput.tsx", "Path": "./client/src/modules/projects/components/atoms/FormInput.tsx", "comments": "Form Input Atom"}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/modules/projects/components/atoms/index.ts", "comments": "Project Atoms Index"}, {"Type": "File", "Name": "LoadingSpinner.tsx", "Path": "./client/src/modules/projects/components/atoms/LoadingSpinner.tsx", "comments": "Loading Spinner Atom"}, {"Type": "File", "Name": "PriorityBadge.tsx", "Path": "./client/src/modules/projects/components/atoms/PriorityBadge.tsx", "comments": "Priority Badge Atom"}, {"Type": "File", "Name": "StatusBadge.tsx", "Path": "./client/src/modules/projects/components/atoms/StatusBadge.tsx", "comments": "Status Badge Atom"}]}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/modules/projects/components/index.ts", "comments": "Project Components Index"}, {"Type": "Folder", "Name": "molecules", "Path": "./client/src/modules/projects/components/molecules", "Children": [{"Type": "File", "Name": "BulkActionBar.tsx", "Path": "./client/src/modules/projects/components/molecules/BulkActionBar.tsx", "comments": "Bulk Action Bar Molecule"}, {"Type": "File", "Name": "FilterPanel.tsx", "Path": "./client/src/modules/projects/components/molecules/FilterPanel.tsx", "comments": "Filter Panel Molecule"}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/modules/projects/components/molecules/index.ts", "comments": "Project Molecules Index"}, {"Type": "File", "Name": "MemberCard.tsx", "Path": "./client/src/modules/projects/components/molecules/MemberCard.tsx", "comments": "Member <PERSON>"}, {"Type": "File", "Name": "MemberForm.tsx", "Path": "./client/src/modules/projects/components/molecules/MemberForm.tsx", "comments": "Member Form Molecule"}, {"Type": "File", "Name": "SearchBar.tsx", "Path": "./client/src/modules/projects/components/molecules/SearchBar.tsx", "comments": "Search Bar Molecule"}]}, {"Type": "Folder", "Name": "organisms", "Path": "./client/src/modules/projects/components/organisms", "Children": [{"Type": "File", "Name": "index.ts", "Path": "./client/src/modules/projects/components/organisms/index.ts", "comments": "Project Organisms Index"}, {"Type": "File", "Name": "ProjectForm.tsx", "Path": "./client/src/modules/projects/components/organisms/ProjectForm.tsx", "comments": "Project Form Organism (Refactored)"}, {"Type": "File", "Name": "ProjectList.tsx", "Path": "./client/src/modules/projects/components/organisms/ProjectList.tsx", "comments": "Project List Organism (Refactored with Atomic Design)"}, {"Type": "File", "Name": "TeamManagement.tsx", "Path": "./client/src/modules/projects/components/organisms/TeamManagement.tsx", "comments": "Team Management Organism (Refactored with Atomic Design)"}]}, {"Type": "File", "Name": "README.md", "Path": "./client/src/modules/projects/components/README.md"}]}, {"Type": "Folder", "Name": "domain", "Path": "./client/src/modules/projects/domain", "Children": [{"Type": "Folder", "Name": "aggregates", "Path": "./client/src/modules/projects/domain/aggregates", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/modules/projects/domain/aggregates/__tests__", "Children": [{"Type": "File", "Name": ".gitkeep", "Path": "./client/src/modules/projects/domain/aggregates/__tests__/.gitkeep"}]}]}, {"Type": "Folder", "Name": "domain-services", "Path": "./client/src/modules/projects/domain/domain-services", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/modules/projects/domain/domain-services/__tests__", "Children": [{"Type": "File", "Name": ".gitkeep", "Path": "./client/src/modules/projects/domain/domain-services/__tests__/.gitkeep"}, {"Type": "File", "Name": "ProjectValidationService.test.ts", "Path": "./client/src/modules/projects/domain/domain-services/__tests__/ProjectValidationService.test.ts", "comments": "ProjectValidationService Unit Tests"}, {"Type": "File", "Name": "TeamManagementService.test.ts", "Path": "./client/src/modules/projects/domain/domain-services/__tests__/TeamManagementService.test.ts", "comments": "TeamManagementService Unit Tests"}]}, {"Type": "File", "Name": "ProjectValidationService.ts", "Path": "./client/src/modules/projects/domain/domain-services/ProjectValidationService.ts", "comments": "ProjectValidationService"}, {"Type": "File", "Name": "TeamManagementService.ts", "Path": "./client/src/modules/projects/domain/domain-services/TeamManagementService.ts", "comments": "TeamManagementService"}]}, {"Type": "Folder", "Name": "entities", "Path": "./client/src/modules/projects/domain/entities", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/modules/projects/domain/entities/__tests__", "Children": [{"Type": "File", "Name": ".gitkeep", "Path": "./client/src/modules/projects/domain/entities/__tests__/.gitkeep"}, {"Type": "File", "Name": "Project.test.ts", "Path": "./client/src/modules/projects/domain/entities/__tests__/Project.test.ts", "comments": "Project Entity (Aggregate Root) Unit Tests"}, {"Type": "File", "Name": "ProjectMember.test.ts", "Path": "./client/src/modules/projects/domain/entities/__tests__/ProjectMember.test.ts", "comments": "ProjectMember Entity Unit Tests"}]}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/modules/projects/domain/entities/index.ts", "comments": "Projects Domain Entities"}, {"Type": "File", "Name": "Project.ts", "Path": "./client/src/modules/projects/domain/entities/Project.ts", "comments": "Project Entity (Aggregate Root)"}, {"Type": "File", "Name": "ProjectMember.ts", "Path": "./client/src/modules/projects/domain/entities/ProjectMember.ts", "comments": "ProjectMember Entity"}]}, {"Type": "Folder", "Name": "events", "Path": "./client/src/modules/projects/domain/events", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/modules/projects/domain/events/__tests__", "Children": [{"Type": "File", "Name": ".gitkeep", "Path": "./client/src/modules/projects/domain/events/__tests__/.gitkeep"}]}]}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/modules/projects/domain/index.ts", "comments": "Projects Domain Layer"}, {"Type": "File", "Name": "README.md", "Path": "./client/src/modules/projects/domain/README.md"}, {"Type": "Folder", "Name": "repositories", "Path": "./client/src/modules/projects/domain/repositories", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/modules/projects/domain/repositories/__tests__", "Children": [{"Type": "File", "Name": ".gitkeep", "Path": "./client/src/modules/projects/domain/repositories/__tests__/.gitkeep"}, {"Type": "File", "Name": "IProjectRepository.test.ts", "Path": "./client/src/modules/projects/domain/repositories/__tests__/IProjectRepository.test.ts", "comments": "IProjectRepository Interface Tests"}]}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/modules/projects/domain/repositories/index.ts", "comments": "Projects Domain Repositories"}, {"Type": "File", "Name": "IProjectRepository.ts", "Path": "./client/src/modules/projects/domain/repositories/IProjectRepository.ts", "comments": "IProjectRepository Interface"}]}, {"Type": "Folder", "Name": "shared", "Path": "./client/src/modules/projects/domain/shared", "Children": [{"Type": "File", "Name": "EntityId.ts", "Path": "./client/src/modules/projects/domain/shared/EntityId.ts", "comments": "Entity ID Generation Utilities"}]}, {"Type": "Folder", "Name": "specifications", "Path": "./client/src/modules/projects/domain/specifications", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/modules/projects/domain/specifications/__tests__", "Children": [{"Type": "File", "Name": ".gitkeep", "Path": "./client/src/modules/projects/domain/specifications/__tests__/.gitkeep"}]}]}, {"Type": "Folder", "Name": "value-objects", "Path": "./client/src/modules/projects/domain/value-objects", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/modules/projects/domain/value-objects/__tests__", "Children": [{"Type": "File", "Name": ".gitkeep", "Path": "./client/src/modules/projects/domain/value-objects/__tests__/.gitkeep"}, {"Type": "File", "Name": "ProjectBudget.test.ts", "Path": "./client/src/modules/projects/domain/value-objects/__tests__/ProjectBudget.test.ts", "comments": "ProjectBudget Value Object Unit Tests"}, {"Type": "File", "Name": "ProjectStatus.test.ts", "Path": "./client/src/modules/projects/domain/value-objects/__tests__/ProjectStatus.test.ts", "comments": "ProjectStatus Value Object Unit Tests"}, {"Type": "File", "Name": "TeamRole.test.ts", "Path": "./client/src/modules/projects/domain/value-objects/__tests__/TeamRole.test.ts", "comments": "TeamRole Value Object Unit Tests"}]}, {"Type": "File", "Name": "ProjectBudget.ts", "Path": "./client/src/modules/projects/domain/value-objects/ProjectBudget.ts", "comments": "ProjectBudget Value Object"}, {"Type": "File", "Name": "ProjectStatus.ts", "Path": "./client/src/modules/projects/domain/value-objects/ProjectStatus.ts", "comments": "ProjectStatus Value Object"}, {"Type": "File", "Name": "TeamRole.ts", "Path": "./client/src/modules/projects/domain/value-objects/TeamRole.ts", "comments": "TeamRole Value Object"}]}]}, {"Type": "Folder", "Name": "hooks", "Path": "./client/src/modules/projects/hooks", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/modules/projects/hooks/__tests__", "Children": [{"Type": "File", "Name": "useProjectOperations.test.tsx", "Path": "./client/src/modules/projects/hooks/__tests__/useProjectOperations.test.tsx", "comments": "Project Operations Hook Tests"}]}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/modules/projects/hooks/index.ts", "comments": "Projects Module Hooks Index"}, {"Type": "File", "Name": "README.md", "Path": "./client/src/modules/projects/hooks/README.md"}, {"Type": "File", "Name": "useProjectForm.ts", "Path": "./client/src/modules/projects/hooks/useProjectForm.ts", "comments": "Project Form Hook"}, {"Type": "File", "Name": "useProjectHooks.ts", "Path": "./client/src/modules/projects/hooks/useProjectHooks.ts", "comments": "Project Hooks Factory"}, {"Type": "File", "Name": "useProjectMembers.ts", "Path": "./client/src/modules/projects/hooks/useProjectMembers.ts"}, {"Type": "File", "Name": "useProjectOperations.ts", "Path": "./client/src/modules/projects/hooks/useProjectOperations.ts", "comments": "Project Operations Hook"}, {"Type": "File", "Name": "useProjects.ts", "Path": "./client/src/modules/projects/hooks/useProjects.ts"}, {"Type": "File", "Name": "useProjectSelection.ts", "Path": "./client/src/modules/projects/hooks/useProjectSelection.ts", "comments": "Project Selection Hook"}, {"Type": "File", "Name": "useProjectStatus.ts", "Path": "./client/src/modules/projects/hooks/useProjectStatus.ts", "comments": "Project Status Management Hook"}, {"Type": "File", "Name": "useProjectTeam.ts", "Path": "./client/src/modules/projects/hooks/useProjectTeam.ts", "comments": "Project Team Management Hook"}]}, {"Type": "Folder", "Name": "infrastructure", "Path": "./client/src/modules/projects/infrastructure", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/modules/projects/infrastructure/__tests__", "Children": [{"Type": "File", "Name": "adapters.test.ts", "Path": "./client/src/modules/projects/infrastructure/__tests__/adapters.test.ts", "comments": "Infrastructure Adapters Integration Tests"}, {"Type": "File", "Name": "ProjectRepository.test.ts", "Path": "./client/src/modules/projects/infrastructure/__tests__/ProjectRepository.test.ts", "comments": "ProjectRepository Integration Tests"}, {"Type": "File", "Name": "setup.ts", "Path": "./client/src/modules/projects/infrastructure/__tests__/setup.ts", "comments": "Infrastructure Layer Test Setup"}]}, {"Type": "Folder", "Name": "adapters", "Path": "./client/src/modules/projects/infrastructure/adapters", "Children": [{"Type": "File", "Name": "index.ts", "Path": "./client/src/modules/projects/infrastructure/adapters/index.ts", "comments": "Infrastructure Adapters Index"}, {"Type": "File", "Name": "ProjectApiAdapter.ts", "Path": "./client/src/modules/projects/infrastructure/adapters/ProjectApiAdapter.ts", "comments": "Project API Domain Adapter"}, {"Type": "File", "Name": "ProjectMemberAdapter.ts", "Path": "./client/src/modules/projects/infrastructure/adapters/ProjectMemberAdapter.ts", "comments": "ProjectMember API Domain Adapter"}, {"Type": "File", "Name": "ValueObjectAdapters.ts", "Path": "./client/src/modules/projects/infrastructure/adapters/ValueObjectAdapters.ts", "comments": "Value Object API Domain Adapters"}]}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/modules/projects/infrastructure/index.ts", "comments": "Projects Infrastructure Layer Index"}, {"Type": "File", "Name": "ProjectRepository.ts", "Path": "./client/src/modules/projects/infrastructure/ProjectRepository.ts", "comments": "ProjectRepository Implementation"}, {"Type": "File", "Name": "README.md", "Path": "./client/src/modules/projects/infrastructure/README.md"}]}, {"Type": "File", "Name": "README.md", "Path": "./client/src/modules/projects/README.md"}, {"Type": "Folder", "Name": "store", "Path": "./client/src/modules/projects/store", "Children": [{"Type": "File", "Name": "projectStore.ts", "Path": "./client/src/modules/projects/store/projectStore.ts", "comments": "Projects Module Zustand Store"}]}]}]}, {"Type": "Folder", "Name": "stores", "Path": "./client/src/stores", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/stores/__tests__", "Children": [{"Type": "File", "Name": "networkStatusStore.test.ts", "Path": "./client/src/stores/__tests__/networkStatusStore.test.ts", "comments": "Unit tests for Network Status Store"}]}, {"Type": "File", "Name": "authStore.ts", "Path": "./client/src/stores/authStore.ts", "comments": "Zustand store for authentication state management"}, {"Type": "File", "Name": "networkStatusStore.ts", "Path": "./client/src/stores/networkStatusStore.ts", "comments": "Network Status Store - Global online/offline state management"}]}, {"Type": "Folder", "Name": "styles", "Path": "./client/src/styles", "Children": []}, {"Type": "Folder", "Name": "test", "Path": "./client/src/test", "Children": [{"Type": "Folder", "Name": "factories", "Path": "./client/src/test/factories", "Children": [{"Type": "File", "Name": "componentFactories.ts", "Path": "./client/src/test/factories/componentFactories.ts", "comments": "Test data factories for Component Management module"}]}, {"Type": "Folder", "Name": "integration", "Path": "./client/src/test/integration", "Children": [{"Type": "File", "Name": "auth-integration.test.tsx", "Path": "./client/src/test/integration/auth-integration.test.tsx", "comments": "Authentication Integration Tests"}, {"Type": "File", "Name": "component-management-integration.test.tsx", "Path": "./client/src/test/integration/component-management-integration.test.tsx", "comments": "Component Management Integration Tests"}, {"Type": "File", "Name": "offline-mode-integration.test.tsx", "Path": "./client/src/test/integration/offline-mode-integration.test.tsx", "comments": "Offline Mode Integration Tests"}, {"Type": "File", "Name": "rbac-workflow.test.tsx", "Path": "./client/src/test/integration/rbac-workflow.test.tsx", "comments": "Integration tests for RBAC workflow"}]}, {"Type": "Folder", "Name": "reporters", "Path": "./client/src/test/reporters", "Children": [{"Type": "File", "Name": "domain-reporter.ts", "Path": "./client/src/test/reporters/domain-reporter.ts"}]}, {"Type": "File", "Name": "setup.ts", "Path": "./client/src/test/setup.ts"}, {"Type": "File", "Name": "utils.tsx", "Path": "./client/src/test/utils.tsx"}]}, {"Type": "Folder", "Name": "types", "Path": "./client/src/types", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/types/__tests__", "Children": [{"Type": "File", "Name": "audit.test.ts", "Path": "./client/src/types/__tests__/audit.test.ts", "comments": "Unit tests for Audit and Activity Log Zod schemas"}, {"Type": "File", "Name": "auth.test.ts", "Path": "./client/src/types/__tests__/auth.test.ts", "comments": "Unit tests for Authentication Zod schemas"}, {"Type": "File", "Name": "component.test.ts", "Path": "./client/src/types/__tests__/component.test.ts", "comments": "Unit tests for Component and ComponentType Zod schemas"}, {"Type": "File", "Name": "componentCategory.test.ts", "Path": "./client/src/types/__tests__/componentCategory.test.ts", "comments": "Unit tests for ComponentCategory Zod schemas"}, {"Type": "File", "Name": "health.test.ts", "Path": "./client/src/types/__tests__/health.test.ts", "comments": "Unit tests for Health Check Zod schemas"}, {"Type": "File", "Name": "project.test.ts", "Path": "./client/src/types/__tests__/project.test.ts", "comments": "Unit tests for Project Zod schemas"}, {"Type": "File", "Name": "projectMember.test.ts", "Path": "./client/src/types/__tests__/projectMember.test.ts", "comments": "Unit tests for ProjectMember Zod schemas"}, {"Type": "File", "Name": "user.test.ts", "Path": "./client/src/types/__tests__/user.test.ts", "comments": "Unit tests for User and UserPreference Zod schemas"}, {"Type": "File", "Name": "userRole.test.ts", "Path": "./client/src/types/__tests__/userRole.test.ts", "comments": "Unit tests for UserRole and UserRoleAssignment Zod schemas"}]}, {"Type": "File", "Name": "api.ts", "Path": "./client/src/types/api.ts", "comments": "TypeScript type definitions for the API."}, {"Type": "File", "Name": "audit.ts", "Path": "./client/src/types/audit.ts"}, {"Type": "File", "Name": "auth.ts", "Path": "./client/src/types/auth.ts", "comments": "TypeScript definitions for Authorizational operations."}, {"Type": "File", "Name": "component.ts", "Path": "./client/src/types/component.ts", "comments": "Zod schemas and TypeScript definitions for Component and ComponentType based on Pydantic schemas."}, {"Type": "File", "Name": "componentCategory.ts", "Path": "./client/src/types/componentCategory.ts", "comments": "Zod schemas and TypeScript definitions for ComponentCategory based on Pydantic schemas."}, {"Type": "File", "Name": "config.ts", "Path": "./client/src/types/config.ts", "comments": "Typescript interfaces for API operations."}, {"Type": "File", "Name": "health.ts", "Path": "./client/src/types/health.ts", "comments": "Zod schemas and TypeScript definitions for Health Check based on Pydantic schemas."}, {"Type": "File", "Name": "nav.ts", "Path": "./client/src/types/nav.ts"}, {"Type": "File", "Name": "project.ts", "Path": "./client/src/types/project.ts", "comments": "Zod schemas and TypeScript definitions for Project based on Pydantic schemas."}, {"Type": "File", "Name": "projectMember.ts", "Path": "./client/src/types/projectMember.ts", "comments": "Zod schemas and TypeScript definitions for ProjectMember based on Pydantic schemas."}, {"Type": "Folder", "Name": "schemas", "Path": "./client/src/types/schemas", "Children": [{"Type": "File", "Name": "baseSchemas.ts", "Path": "./client/src/types/schemas/baseSchemas.ts", "comments": "Base Zod schemas that mirror the server's Pydantic base schemas."}, {"Type": "Folder", "Name": "enums", "Path": "./client/src/types/schemas/enums", "Children": [{"Type": "File", "Name": "calculationEnums.ts", "Path": "./client/src/types/schemas/enums/calculationEnums.ts", "comments": "enumeration types for various engineering calculations"}, {"Type": "File", "Name": "commonEnums.ts", "Path": "./client/src/types/schemas/enums/commonEnums.ts", "comments": "common, non-domain-specific enumeration types"}, {"Type": "File", "Name": "componentEnums.ts", "Path": "./client/src/types/schemas/enums/componentEnums.ts"}, {"Type": "File", "Name": "dataIoEnums.ts", "Path": "./client/src/types/schemas/enums/dataIoEnums.ts", "comments": "enumeration types pertaining to data input/output"}, {"Type": "File", "Name": "electricalEnums.ts", "Path": "./client/src/types/schemas/enums/electricalEnums.ts", "comments": "comprehensive enumeration types specific to electrical"}, {"Type": "File", "Name": "heatTracingEnums.ts", "Path": "./client/src/types/schemas/enums/heatTracingEnums.ts", "comments": "enumeration types specifically tailored for heat tracing"}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/types/schemas/enums/index.ts"}, {"Type": "File", "Name": "mechanicalEnums.ts", "Path": "./client/src/types/schemas/enums/mechanicalEnums.ts", "comments": "enumeration types related to mechanical elements and"}, {"Type": "File", "Name": "projectManagementEnums.ts", "Path": "./client/src/types/schemas/enums/projectManagementEnums.ts"}, {"Type": "File", "Name": "safetyEnums.ts", "Path": "./client/src/types/schemas/enums/safetyEnums.ts", "comments": "enumeration types related to hazardous area classifications."}, {"Type": "File", "Name": "standardsEnums.ts", "Path": "./client/src/types/schemas/enums/standardsEnums.ts", "comments": "enumeration types related to engineering standards,"}, {"Type": "File", "Name": "systemEnums.ts", "Path": "./client/src/types/schemas/enums/systemEnums.ts", "comments": "enumeration types for internal system operations,"}, {"Type": "File", "Name": "utilEnums.ts", "Path": "./client/src/types/schemas/enums/utilEnums.ts", "comments": "Filter operators for querying and manipulating data."}]}, {"Type": "File", "Name": "errorSchemas.ts", "Path": "./client/src/types/schemas/errorSchemas.ts", "comments": "Base Zod schemas that mirror the server's Pydantic error schemas."}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/types/schemas/index.ts", "comments": "This module exports Zod schemas that mirror the server's base Pydantic schemas."}]}, {"Type": "File", "Name": "task.ts", "Path": "./client/src/types/task.ts", "comments": "Zod schemas and TypeScript definitions for Task Management based on Pydantic schemas."}, {"Type": "File", "Name": "user.ts", "Path": "./client/src/types/user.ts", "comments": "Zod schemas and TypeScript definitions for User and User Preference based on Pydantic schemas."}, {"Type": "File", "Name": "userRole.ts", "Path": "./client/src/types/userRole.ts", "comments": "TypeScript definitions for User Role and Role Assignment Pydantic schemas."}]}, {"Type": "Folder", "Name": "utils", "Path": "./client/src/utils", "Children": [{"Type": "File", "Name": "textUtils.ts", "Path": "./client/src/utils/textUtils.ts", "comments": "@param val Text"}]}]}, {"Type": "File", "Name": "tailwind.config.ts", "Path": "./client/tailwind.config.ts", "comments": "@type {import('tailwindcss').Config}"}, {"Type": "Folder", "Name": "tests", "Path": "./client/tests", "Children": [{"Type": "Folder", "Name": "e2e", "Path": "./client/tests/e2e", "Children": [{"Type": "File", "Name": "cache-persistence.spec.ts", "Path": "./client/tests/e2e/cache-persistence.spec.ts", "comments": "End-to-end tests for cache persistence across page reloads"}, {"Type": "File", "Name": "component-management.spec.ts", "Path": "./client/tests/e2e/component-management.spec.ts", "comments": "Component Management E2E Tests"}, {"Type": "File", "Name": "global.setup.ts", "Path": "./client/tests/e2e/global.setup.ts", "comments": "Playwright Global Setup"}, {"Type": "File", "Name": "offline-mode.spec.ts", "Path": "./client/tests/e2e/offline-mode.spec.ts", "comments": "E2E tests for Offline Mode workflow"}, {"Type": "File", "Name": "rbac-audit-workflow.spec.ts", "Path": "./client/tests/e2e/rbac-audit-workflow.spec.ts", "comments": "E2E tests for RBAC and Audit Trail workflows"}]}, {"Type": "Folder", "Name": "mocks", "Path": "./client/tests/mocks", "Children": [{"Type": "Folder", "Name": "fixtures", "Path": "./client/tests/mocks/fixtures", "Children": [{"Type": "File", "Name": "auth.ts", "Path": "./client/tests/mocks/fixtures/auth.ts", "comments": "Authentication Fixtures for MSW Testing"}, {"Type": "File", "Name": "componentCategories.ts", "Path": "./client/tests/mocks/fixtures/componentCategories.ts", "comments": "Component Category Fixtures for MSW"}, {"Type": "File", "Name": "components.ts", "Path": "./client/tests/mocks/fixtures/components.ts", "comments": "Test fixtures for component management"}, {"Type": "File", "Name": "componentTypes.ts", "Path": "./client/tests/mocks/fixtures/componentTypes.ts", "comments": "Component Type Fixtures for MSW Testing"}]}, {"Type": "Folder", "Name": "handlers", "Path": "./client/tests/mocks/handlers", "Children": [{"Type": "File", "Name": "auth.ts", "Path": "./client/tests/mocks/handlers/auth.ts", "comments": "Authentication API Handlers for MSW"}, {"Type": "File", "Name": "componentCategories.ts", "Path": "./client/tests/mocks/handlers/componentCategories.ts", "comments": "Component Category API Handlers for MSW"}, {"Type": "File", "Name": "components.ts", "Path": "./client/tests/mocks/handlers/components.ts", "comments": "MSW handlers for component API endpoints"}, {"Type": "File", "Name": "componentTypes.ts", "Path": "./client/tests/mocks/handlers/componentTypes.ts", "comments": "Component Type API Handlers for MSW"}, {"Type": "File", "Name": "health.ts", "Path": "./client/tests/mocks/handlers/health.ts", "comments": "Health Check API Handlers for MSW"}, {"Type": "File", "Name": "projects.ts", "Path": "./client/tests/mocks/handlers/projects.ts", "comments": "MSW handlers for project API endpoints"}, {"Type": "File", "Name": "users.ts", "Path": "./client/tests/mocks/handlers/users.ts", "comments": "User Management API Handlers for MSW"}]}, {"Type": "File", "Name": "server.ts", "Path": "./client/tests/mocks/server.ts", "comments": "Mock Service Worker (MSW) Server Setup for Playwright E2E Tests"}]}]}, {"Type": "File", "Name": "vitest.config.ts", "Path": "./client/vitest.config.ts"}, {"Type": "File", "Name": "vitest.setup.ts", "Path": "./client/vitest.setup.ts"}]}, {"Type": "Folder", "Name": "computation-engine-service", "Path": "./computation-engine-service", "Children": [{"Type": "File", "Name": "Dockerfile", "Path": "./computation-engine-service/Dockerfile"}, {"Type": "File", "Name": "README.md", "Path": "./computation-engine-service/README.md"}, {"Type": "Folder", "Name": "src", "Path": "./computation-engine-service/src", "Children": [{"Type": "Folder", "Name": "Controllers", "Path": "./computation-engine-service/src/Controllers", "Children": [{"Type": "File", "Name": "ComputationController.cs", "Path": "./computation-engine-service/src/Controllers/ComputationController.cs"}]}, {"Type": "Folder", "Name": "Services", "Path": "./computation-engine-service/src/Services", "Children": [{"Type": "File", "Name": "PowerFlowSolver.cs", "Path": "./computation-engine-service/src/Services/PowerFlowSolver.cs"}]}, {"Type": "File", "Name": "ultimate_electrical_designer.ComputationEngine.csproj", "Path": "./computation-engine-service/src/ultimate_electrical_designer.ComputationEngine.csproj"}]}]}, {"Type": "Folder", "Name": "docs", "Path": "./docs", "Children": [{"Type": "File", "Name": "codebase-structure.md", "Path": "./docs/codebase-structure.md"}, {"Type": "Folder", "Name": "deployment", "Path": "./docs/deployment", "Children": [{"Type": "File", "Name": "POSTGRESQL_INSTALLER_PLAN.md", "Path": "./docs/deployment/POSTGRESQL_INSTALLER_PLAN.md"}]}, {"Type": "File", "Name": "design.md", "Path": "./docs/design.md"}, {"Type": "Folder", "Name": "developer-guides", "Path": "./docs/developer-guides", "Children": [{"Type": "File", "Name": "synchronization-developer-guide.md", "Path": "./docs/developer-guides/synchronization-developer-guide.md"}]}, {"Type": "Folder", "Name": "personal", "Path": "./docs/personal", "Children": [{"Type": "Folder", "Name": "agents", "Path": "./docs/personal/agents", "Children": [{"Type": "File", "Name": "Backend Frontend Agent.md", "Path": "./docs/personal/agents/Backend Frontend Agent.md"}, {"Type": "File", "Name": "Code Quality Agent.md", "Path": "./docs/personal/agents/Code Quality Agent.md"}, {"Type": "File", "Name": "Orchestrator Agent.md", "Path": "./docs/personal/agents/Orchestrator Agent.md"}, {"Type": "File", "Name": "Prompt Framework.md", "Path": "./docs/personal/agents/Prompt Framework.md"}, {"Type": "File", "Name": "Task Planner Agent.md", "Path": "./docs/personal/agents/Task Planner Agent.md"}, {"Type": "File", "Name": "Technical Design Agent.md", "Path": "./docs/personal/agents/Technical Design Agent.md"}]}, {"Type": "Folder", "Name": "templates", "Path": "./docs/personal/templates", "Children": [{"Type": "File", "Name": "Fix Type Errors Template.md", "Path": "./docs/personal/templates/Fix Type Errors Template.md"}]}]}, {"Type": "File", "Name": "product.md", "Path": "./docs/product.md"}, {"Type": "File", "Name": "README.md", "Path": "./docs/README.md"}, {"Type": "File", "Name": "requirements.md", "Path": "./docs/requirements.md"}, {"Type": "File", "Name": "rules.md", "Path": "./docs/rules.md"}, {"Type": "Folder", "Name": "tasks", "Path": "./docs/tasks", "Children": [{"Type": "Folder", "Name": "Project-Task-Management-Feature", "Path": "./docs/tasks/Project-Task-Management-Feature", "Children": [{"Type": "File", "Name": "Task-Plan.md", "Path": "./docs/tasks/Project-Task-Management-Feature/Task-Plan.md"}, {"Type": "File", "Name": "Technical-Design-Specification.md", "Path": "./docs/tasks/Project-Task-Management-Feature/Technical-Design-Specification.md"}]}, {"Type": "Folder", "Name": "universal-frontend-components", "Path": "./docs/tasks/universal-frontend-components", "Children": [{"Type": "File", "Name": "task-plan.md", "Path": "./docs/tasks/universal-frontend-components/task-plan.md"}, {"Type": "File", "Name": "technical-design.md", "Path": "./docs/tasks/universal-frontend-components/technical-design.md"}]}]}, {"Type": "File", "Name": "tasks.md", "Path": "./docs/tasks.md"}, {"Type": "File", "Name": "tech.md", "Path": "./docs/tech.md"}, {"Type": "File", "Name": "TESTING.md", "Path": "./docs/TESTING.md"}, {"Type": "File", "Name": "workflows.md", "Path": "./docs/workflows.md"}]}, {"Type": "File", "Name": "README.md", "Path": "./README.md"}, {"Type": "Folder", "Name": "scripts", "Path": "./scripts", "Children": []}, {"Type": "Folder", "Name": "server", "Path": "./server", "Children": [{"Type": "File", "Name": ".env", "Path": "./server/.env"}, {"Type": "File", "Name": "conftest.py", "Path": "./server/conftest.py", "comments": "Pytest configuration."}, {"Type": "Folder", "Name": "data", "Path": "./server/data", "Children": [{"Type": "File", "Name": "seed_general.py", "Path": "./server/data/seed_general.py", "comments": "This script, `seed_general.py`, provides a comprehensive and idempotent mechanism"}]}, {"Type": "File", "Name": "Dockerfile", "Path": "./server/Dockerfile"}, {"Type": "Folder", "Name": "docs", "Path": "./server/docs", "Children": [{"Type": "Folder", "Name": "2025-07-19_test-database-integrity", "Path": "./server/docs/2025-07-19_test-database-integrity", "Children": [{"Type": "File", "Name": "01-test_database_integrity_report.md", "Path": "./server/docs/2025-07-19_test-database-integrity/01-test_database_integrity_report.md"}, {"Type": "File", "Name": "02-enhanced_verification_report.md", "Path": "./server/docs/2025-07-19_test-database-integrity/02-enhanced_verification_report.md"}, {"Type": "File", "Name": "03-test_coverage_immediate_actions.md", "Path": "./server/docs/2025-07-19_test-database-integrity/03-test_coverage_immediate_actions.md"}, {"Type": "File", "Name": "04-performance_testing_enhancement_summary.md", "Path": "./server/docs/2025-07-19_test-database-integrity/04-performance_testing_enhancement_summary.md"}, {"Type": "File", "Name": "05-database_operations_testing_comprehensive_summary.md", "Path": "./server/docs/2025-07-19_test-database-integrity/05-database_operations_testing_comprehensive_summary.md"}, {"Type": "File", "Name": "06-advanced_validation_compatibility.md", "Path": "./server/docs/2025-07-19_test-database-integrity/06-advanced_validation_compatibility.md"}]}, {"Type": "Folder", "Name": "2025-07-20_dual-database-setup-DEPRECATED", "Path": "./server/docs/2025-07-20_dual-database-setup-DEPRECATED", "Children": [{"Type": "File", "Name": "discovery-analysis.md", "Path": "./server/docs/2025-07-20_dual-database-setup-DEPRECATED/discovery-analysis.md"}, {"Type": "File", "Name": "dual-database-guide.md", "Path": "./server/docs/2025-07-20_dual-database-setup-DEPRECATED/dual-database-guide.md"}, {"Type": "File", "Name": "implementation-report.md", "Path": "./server/docs/2025-07-20_dual-database-setup-DEPRECATED/implementation-report.md"}, {"Type": "File", "Name": "plan.md", "Path": "./server/docs/2025-07-20_dual-database-setup-DEPRECATED/plan.md"}]}, {"Type": "Folder", "Name": "2025-07-21_offline-mode", "Path": "./server/docs/2025-07-21_offline-mode", "Children": [{"Type": "File", "Name": "discovery_analysis.md", "Path": "./server/docs/2025-07-21_offline-mode/discovery_analysis.md"}, {"Type": "File", "Name": "plan.md", "Path": "./server/docs/2025-07-21_offline-mode/plan.md"}]}, {"Type": "Folder", "Name": "2025-07-22_unified_local_database", "Path": "./server/docs/2025-07-22_unified_local_database", "Children": [{"Type": "File", "Name": "phase1_discovery_analysis.md", "Path": "./server/docs/2025-07-22_unified_local_database/phase1_discovery_analysis.md"}, {"Type": "File", "Name": "phase2_implementation_plan.md", "Path": "./server/docs/2025-07-22_unified_local_database/phase2_implementation_plan.md"}, {"Type": "File", "Name": "phase2_implementation_report.md", "Path": "./server/docs/2025-07-22_unified_local_database/phase2_implementation_report.md"}]}, {"Type": "Folder", "Name": "2025-07-29_comprehensive-test-verification", "Path": "./server/docs/2025-07-29_comprehensive-test-verification", "Children": [{"Type": "File", "Name": "phase4_comprehensive_verification_report.md", "Path": "./server/docs/2025-07-29_comprehensive-test-verification/phase4_comprehensive_verification_report.md"}, {"Type": "File", "Name": "systematic_issues_resolution_summary.md", "Path": "./server/docs/2025-07-29_comprehensive-test-verification/systematic_issues_resolution_summary.md"}]}, {"Type": "File", "Name": "test-summary.md", "Path": "./server/docs/test-summary.md"}]}, {"Type": "File", "Name": "pyproject.toml", "Path": "./server/pyproject.toml"}, {"Type": "File", "Name": "README.md", "Path": "./server/README.md"}, {"Type": "Folder", "Name": "src", "Path": "./server/src", "Children": [{"Type": "Folder", "Name": "alembic", "Path": "./server/src/alembic", "Children": []}, {"Type": "Folder", "Name": "api", "Path": "./server/src/api", "Children": [{"Type": "File", "Name": "main_router.py", "Path": "./server/src/api/main_router.py", "comments": "Main API Router Configuration."}, {"Type": "Folder", "Name": "v1", "Path": "./server/src/api/v1", "Children": [{"Type": "File", "Name": "auth_routes.py", "Path": "./server/src/api/v1/auth_routes.py", "comments": "Authentication API Routes."}, {"Type": "File", "Name": "component_category_routes.py", "Path": "./server/src/api/v1/component_category_routes.py", "comments": "Component Category API Routes."}, {"Type": "File", "Name": "component_routes.py", "Path": "./server/src/api/v1/component_routes.py", "comments": "Component API Routes."}, {"Type": "File", "Name": "component_type_routes.py", "Path": "./server/src/api/v1/component_type_routes.py", "comments": "Component Type API Routes."}, {"Type": "File", "Name": "cross_validation_routes.py", "Path": "./server/src/api/v1/cross_validation_routes.py", "comments": "Cross-entity validation API routes for advanced dependency checking."}, {"Type": "File", "Name": "health_routes.py", "Path": "./server/src/api/v1/health_routes.py", "comments": "Health Check API Routes."}, {"Type": "File", "Name": "parallel_validation_routes.py", "Path": "./server/src/api/v1/parallel_validation_routes.py", "comments": "Parallel Validation API Endpoints."}, {"Type": "File", "Name": "project_routes.py", "Path": "./server/src/api/v1/project_routes.py", "comments": "Project and Project Member API endpoints."}, {"Type": "File", "Name": "router.py", "Path": "./server/src/api/v1/router.py", "comments": "API Version 1 Router Configuration."}, {"Type": "File", "Name": "task_routes.py", "Path": "./server/src/api/v1/task_routes.py", "comments": "Task Management API Routes."}, {"Type": "File", "Name": "user_preferences_routes.py", "Path": "./server/src/api/v1/user_preferences_routes.py", "comments": "User Preferences API Routes."}, {"Type": "File", "Name": "user_routes.py", "Path": "./server/src/api/v1/user_routes.py", "comments": "User Management API Routes."}, {"Type": "File", "Name": "validation_routes.py", "Path": "./server/src/api/v1/validation_routes.py", "comments": "WebSocket validation routes for real-time electrical parameter validation."}]}]}, {"Type": "File", "Name": "app.py", "Path": "./server/src/app.py", "comments": "Application Module."}, {"Type": "Folder", "Name": "config", "Path": "./server/src/config", "Children": [{"Type": "File", "Name": "logging_config.py", "Path": "./server/src/config/logging_config.py", "comments": "Logging Configuration."}, {"Type": "File", "Name": "settings.py", "Path": "./server/src/config/settings.py", "comments": "Application Configuration Settings."}]}, {"Type": "Folder", "Name": "core", "Path": "./server/src/core", "Children": [{"Type": "Folder", "Name": "auth", "Path": "./server/src/core/auth", "Children": [{"Type": "File", "Name": "dependencies.py", "Path": "./server/src/core/auth/dependencies.py", "comments": "Authentication Dependencies."}]}, {"Type": "Folder", "Name": "calculations", "Path": "./server/src/core/calculations", "Children": []}, {"Type": "Folder", "Name": "database", "Path": "./server/src/core/database", "Children": [{"Type": "File", "Name": "connection_manager.py", "Path": "./server/src/core/database/connection_manager.py", "comments": "Dynamic Database Connection Management."}, {"Type": "File", "Name": "dependencies.py", "Path": "./server/src/core/database/dependencies.py", "comments": "Database Dependencies."}, {"Type": "File", "Name": "engine.py", "Path": "./server/src/core/database/engine.py", "comments": "Database Engine Management."}, {"Type": "File", "Name": "initialization.py", "Path": "./server/src/core/database/initialization.py", "comments": "Database Initialization."}, {"Type": "File", "Name": "session.py", "Path": "./server/src/core/database/session.py", "comments": "Database Session Management."}]}, {"Type": "Folder", "Name": "enums", "Path": "./server/src/core/enums", "Children": [{"Type": "File", "Name": "calculation_enums.py", "Path": "./server/src/core/enums/calculation_enums.py", "comments": "Enumeration types for various engineering calculations"}, {"Type": "File", "Name": "common_enums.py", "Path": "./server/src/core/enums/common_enums.py", "comments": "Common, non-domain-specific enumeration types"}, {"Type": "File", "Name": "data_io_enums.py", "Path": "./server/src/core/enums/data_io_enums.py", "comments": "Enumeration types pertaining to data input/output"}, {"Type": "File", "Name": "electrical_enums.py", "Path": "./server/src/core/enums/electrical_enums.py", "comments": "Comprehensive enumeration types specific to electrical"}, {"Type": "File", "Name": "heat_tracing_enums.py", "Path": "./server/src/core/enums/heat_tracing_enums.py", "comments": "Enumeration types specifically tailored for heat tracing"}, {"Type": "File", "Name": "mechanical_enums.py", "Path": "./server/src/core/enums/mechanical_enums.py", "comments": "Enumeration types related to mechanical elements and"}, {"Type": "File", "Name": "project_management_enums.py", "Path": "./server/src/core/enums/project_management_enums.py", "comments": "Enumeration types essential for managing project"}, {"Type": "File", "Name": "standards_enums.py", "Path": "./server/src/core/enums/standards_enums.py", "comments": "Enumeration types related to engineering standards,"}, {"Type": "File", "Name": "system_enums.py", "Path": "./server/src/core/enums/system_enums.py", "comments": "Enumeration types for internal system operations,"}]}, {"Type": "Folder", "Name": "errors", "Path": "./server/src/core/errors", "Children": [{"Type": "File", "Name": "exceptions.py", "Path": "./server/src/core/errors/exceptions.py", "comments": "Custom Exception Classes."}, {"Type": "File", "Name": "unified_error_handler.py", "Path": "./server/src/core/errors/unified_error_handler.py", "comments": "Unified Error Handling System."}]}, {"Type": "Folder", "Name": "integrations", "Path": "./server/src/core/integrations", "Children": [{"Type": "File", "Name": "README.md", "Path": "./server/src/core/integrations/README.md"}]}, {"Type": "Folder", "Name": "models", "Path": "./server/src/core/models", "Children": [{"Type": "File", "Name": "base.py", "Path": "./server/src/core/models/base.py", "comments": "Base Model Classes and Common Database Patterns."}, {"Type": "Folder", "Name": "general", "Path": "./server/src/core/models/general", "Children": [{"Type": "File", "Name": "activity_log.py", "Path": "./server/src/core/models/general/activity_log.py", "comments": "Activity Log and Audit Trail Database Models."}, {"Type": "File", "Name": "component.py", "Path": "./server/src/core/models/general/component.py", "comments": "Component Database Model."}, {"Type": "File", "Name": "component_category.py", "Path": "./server/src/core/models/general/component_category.py", "comments": "Component Category Database Model."}, {"Type": "File", "Name": "component_type.py", "Path": "./server/src/core/models/general/component_type.py", "comments": "Component Type Database Model."}, {"Type": "File", "Name": "project.py", "Path": "./server/src/core/models/general/project.py", "comments": "Project Database Model."}, {"Type": "File", "Name": "synchronization_log.py", "Path": "./server/src/core/models/general/synchronization_log.py", "comments": "Synchronization Log Database Models."}, {"Type": "File", "Name": "task.py", "Path": "./server/src/core/models/general/task.py", "comments": "Task and TaskAssignment Database Models."}, {"Type": "File", "Name": "user.py", "Path": "./server/src/core/models/general/user.py", "comments": "User and User Preference Database Models."}, {"Type": "File", "Name": "user_role.py", "Path": "./server/src/core/models/general/user_role.py", "comments": "User Role and Role Assignment Database Models."}]}]}, {"Type": "Folder", "Name": "monitoring", "Path": "./server/src/core/monitoring", "Children": [{"Type": "File", "Name": "performance_monitor.py", "Path": "./server/src/core/monitoring/performance_monitor.py", "comments": "Performance Monitor Mo<PERSON>le"}, {"Type": "File", "Name": "unified_performance_monitor.py", "Path": "./server/src/core/monitoring/unified_performance_monitor.py", "comments": "Unified Performance Monitoring System."}]}, {"Type": "Folder", "Name": "repositories", "Path": "./server/src/core/repositories", "Children": [{"Type": "File", "Name": "base_repository.py", "Path": "./server/src/core/repositories/base_repository.py", "comments": "Base Repository."}, {"Type": "Folder", "Name": "general", "Path": "./server/src/core/repositories/general", "Children": [{"Type": "File", "Name": "component_category_repository.py", "Path": "./server/src/core/repositories/general/component_category_repository.py", "comments": "Component Category Repository."}, {"Type": "File", "Name": "component_repository.py", "Path": "./server/src/core/repositories/general/component_repository.py", "comments": "Component Repository."}, {"Type": "File", "Name": "component_type_repository.py", "Path": "./server/src/core/repositories/general/component_type_repository.py", "comments": "Component Type Repository."}, {"Type": "File", "Name": "project_member_repository.py", "Path": "./server/src/core/repositories/general/project_member_repository.py", "comments": "Project Member Repository."}, {"Type": "File", "Name": "project_repository.py", "Path": "./server/src/core/repositories/general/project_repository.py", "comments": "Project Repository."}, {"Type": "File", "Name": "task_repository.py", "Path": "./server/src/core/repositories/general/task_repository.py", "comments": "Task Repository for database operations."}, {"Type": "File", "Name": "user_preference_repository.py", "Path": "./server/src/core/repositories/general/user_preference_repository.py", "comments": "User Preference Repository."}, {"Type": "File", "Name": "user_repository.py", "Path": "./server/src/core/repositories/general/user_repository.py", "comments": "User Repository."}]}, {"Type": "File", "Name": "repository_dependencies.py", "Path": "./server/src/core/repositories/repository_dependencies.py", "comments": "Repository Dependencies."}]}, {"Type": "Folder", "Name": "schemas", "Path": "./server/src/core/schemas", "Children": [{"Type": "File", "Name": "base_schemas.py", "Path": "./server/src/core/schemas/base_schemas.py", "comments": "Base schemas."}, {"Type": "File", "Name": "error.py", "Path": "./server/src/core/schemas/error.py", "comments": "Error Response Schema Definitions."}, {"Type": "Folder", "Name": "general", "Path": "./server/src/core/schemas/general", "Children": [{"Type": "File", "Name": "audit_trail_schemas.py", "Path": "./server/src/core/schemas/general/audit_trail_schemas.py", "comments": "Pydantic schemas for Activity Log and Audit Trail models."}, {"Type": "File", "Name": "component_category_schemas.py", "Path": "./server/src/core/schemas/general/component_category_schemas.py", "comments": "Component Category Schemas."}, {"Type": "File", "Name": "component_schemas.py", "Path": "./server/src/core/schemas/general/component_schemas.py", "comments": "Component Schemas."}, {"Type": "File", "Name": "component_type_schemas.py", "Path": "./server/src/core/schemas/general/component_type_schemas.py", "comments": "Component Type Schemas."}, {"Type": "File", "Name": "project_member_schemas.py", "Path": "./server/src/core/schemas/general/project_member_schemas.py", "comments": "Project Member schemas."}, {"Type": "File", "Name": "project_schemas.py", "Path": "./server/src/core/schemas/general/project_schemas.py", "comments": "Project schemas."}, {"Type": "File", "Name": "task_schemas.py", "Path": "./server/src/core/schemas/general/task_schemas.py", "comments": "Task Management Schemas."}, {"Type": "File", "Name": "user_role_schemas.py", "Path": "./server/src/core/schemas/general/user_role_schemas.py", "comments": "Pydantic schemas for User Role and Role Assignment models."}, {"Type": "File", "Name": "user_schemas.py", "Path": "./server/src/core/schemas/general/user_schemas.py", "comments": "User schemas."}]}, {"Type": "File", "Name": "health.py", "Path": "./server/src/core/schemas/health.py", "comments": "Health Check Schemas."}]}, {"Type": "Folder", "Name": "security", "Path": "./server/src/core/security", "Children": [{"Type": "File", "Name": "enhanced_dependencies.py", "Path": "./server/src/core/security/enhanced_dependencies.py", "comments": "Enhanced Security Dependencies."}, {"Type": "File", "Name": "input_validators.py", "Path": "./server/src/core/security/input_validators.py", "comments": "Input Validation Module."}, {"Type": "File", "Name": "password_handler.py", "Path": "./server/src/core/security/password_handler.py", "comments": "Password handling with professional security practices."}, {"Type": "File", "Name": "unified_security_validator.py", "Path": "./server/src/core/security/unified_security_validator.py", "comments": "Unified Security Validation System."}]}, {"Type": "Folder", "Name": "services", "Path": "./server/src/core/services", "Children": [{"Type": "File", "Name": "dependencies.py", "Path": "./server/src/core/services/dependencies.py", "comments": "Service Dependencies."}, {"Type": "Folder", "Name": "general", "Path": "./server/src/core/services/general", "Children": [{"Type": "File", "Name": "audit_trail_service.py", "Path": "./server/src/core/services/general/audit_trail_service.py", "comments": "Generic Audit Trail Service for System-Wide Activity Logging."}, {"Type": "File", "Name": "component_category_service.py", "Path": "./server/src/core/services/general/component_category_service.py", "comments": "Component Category Service."}, {"Type": "File", "Name": "component_service.py", "Path": "./server/src/core/services/general/component_service.py", "comments": "Component Service."}, {"Type": "File", "Name": "component_type_service.py", "Path": "./server/src/core/services/general/component_type_service.py", "comments": "Component Type Service."}, {"Type": "File", "Name": "health_service.py", "Path": "./server/src/core/services/general/health_service.py", "comments": "Health Check Service."}, {"Type": "File", "Name": "project_member_service.py", "Path": "./server/src/core/services/general/project_member_service.py", "comments": "Project Member Service."}, {"Type": "File", "Name": "project_service.py", "Path": "./server/src/core/services/general/project_service.py", "comments": "Project Service Layer."}, {"Type": "File", "Name": "synchronization_service.py", "Path": "./server/src/core/services/general/synchronization_service.py", "comments": "Synchronization Service for Unified Local Database Management."}, {"Type": "File", "Name": "task_manager_service.py", "Path": "./server/src/core/services/general/task_manager_service.py", "comments": "Task Manager Service for business logic operations."}, {"Type": "File", "Name": "user_service.py", "Path": "./server/src/core/services/general/user_service.py", "comments": "User Service."}]}]}, {"Type": "Folder", "Name": "standards", "Path": "./server/src/core/standards", "Children": []}, {"Type": "Folder", "Name": "utils", "Path": "./server/src/core/utils", "Children": [{"Type": "File", "Name": "advanced_cache_manager.py", "Path": "./server/src/core/utils/advanced_cache_manager.py", "comments": "Advanced Cache Manager for Component Management System."}, {"Type": "File", "Name": "crud_endpoint_factory.py", "Path": "./server/src/core/utils/crud_endpoint_factory.py", "comments": "CRUD Endpoint Factory."}, {"Type": "File", "Name": "datetime_utils.py", "Path": "./server/src/core/utils/datetime_utils.py", "comments": "DateTime Utilities."}, {"Type": "File", "Name": "file_io_utils.py", "Path": "./server/src/core/utils/file_io_utils.py", "comments": "File I/O Utilities."}, {"Type": "File", "Name": "json_validation.py", "Path": "./server/src/core/utils/json_validation.py", "comments": "JSON Validation Utilities."}, {"Type": "File", "Name": "logger.py", "Path": "./server/src/core/utils/logger.py", "comments": "Logger Utility Module"}, {"Type": "File", "Name": "memory_manager.py", "Path": "./server/src/core/utils/memory_manager.py", "comments": "Memory Management Utilities."}, {"Type": "File", "Name": "pagination_utils.py", "Path": "./server/src/core/utils/pagination_utils.py", "comments": "Pagination Utilities."}, {"Type": "File", "Name": "performance_optimizer.py", "Path": "./server/src/core/utils/performance_optimizer.py", "comments": "Performance Optimization Utilities."}, {"Type": "File", "Name": "performance_utils.py", "Path": "./server/src/core/utils/performance_utils.py", "comments": "Performance optimization utilities for large dataset operations."}, {"Type": "File", "Name": "query_optimizer.py", "Path": "./server/src/core/utils/query_optimizer.py", "comments": "Database Query Optimizer for Component Management System."}, {"Type": "File", "Name": "query_utils.py", "Path": "./server/src/core/utils/query_utils.py", "comments": "Query Utilities."}, {"Type": "File", "Name": "search_query_builder.py", "Path": "./server/src/core/utils/search_query_builder.py", "comments": "Advanced Search Query Builder for Component Management."}, {"Type": "File", "Name": "security.py", "Path": "./server/src/core/utils/security.py", "comments": "Security utilities."}, {"Type": "File", "Name": "string_utils.py", "Path": "./server/src/core/utils/string_utils.py", "comments": "String Utilities."}, {"Type": "File", "Name": "uuid_utils.py", "Path": "./server/src/core/utils/uuid_utils.py", "comments": "UUID Utilities."}]}, {"Type": "Folder", "Name": "validation", "Path": "./server/src/core/validation", "Children": [{"Type": "File", "Name": "advanced_validators.py", "Path": "./server/src/core/validation/advanced_validators.py", "comments": "Advanced Validation Module."}, {"Type": "File", "Name": "compatibility_matrix.py", "Path": "./server/src/core/validation/compatibility_matrix.py", "comments": "Project-Component Compatibility Matrix."}, {"Type": "File", "Name": "constraint_validator.py", "Path": "./server/src/core/validation/constraint_validator.py", "comments": "Complex Constraint Validation System."}, {"Type": "File", "Name": "cross_entity_validator.py", "Path": "./server/src/core/validation/cross_entity_validator.py", "comments": "Cross-Entity Dependency Validation Engine."}, {"Type": "File", "Name": "data_format_validator.py", "Path": "./server/src/core/validation/data_format_validator.py", "comments": "Multi-format Data Compatibility Validation."}, {"Type": "File", "Name": "intelligent_caching.py", "Path": "./server/src/core/validation/intelligent_caching.py", "comments": "Intelligent Validation Caching System with Invalidation."}, {"Type": "File", "Name": "json_schema_validator.py", "Path": "./server/src/core/validation/json_schema_validator.py", "comments": "Advanced JSON Schema Validation with JSON Path Queries."}, {"Type": "File", "Name": "legacy_migration_validator.py", "Path": "./server/src/core/validation/legacy_migration_validator.py", "comments": "Legacy Data Format Migration Validation System."}, {"Type": "File", "Name": "parallel_processor.py", "Path": "./server/src/core/validation/parallel_processor.py", "comments": "Parallel Validation Processing System."}, {"Type": "File", "Name": "standards_validator.py", "Path": "./server/src/core/validation/standards_validator.py", "comments": "Dynamic Standards Compliance Validation System."}]}]}, {"Type": "File", "Name": "main.py", "Path": "./server/src/main.py", "comments": "Main entrypoint for the application."}, {"Type": "Folder", "Name": "middleware", "Path": "./server/src/middleware", "Children": [{"Type": "File", "Name": "caching_middleware.py", "Path": "./server/src/middleware/caching_middleware.py", "comments": "Caching Middleware."}, {"Type": "File", "Name": "context_middleware.py", "Path": "./server/src/middleware/context_middleware.py", "comments": "Context Middleware."}, {"Type": "File", "Name": "logging_middleware.py", "Path": "./server/src/middleware/logging_middleware.py", "comments": "Logging Middleware."}, {"Type": "File", "Name": "rate_limiting_middleware.py", "Path": "./server/src/middleware/rate_limiting_middleware.py", "comments": "Rate Limiting Middleware."}, {"Type": "File", "Name": "security_middleware.py", "Path": "./server/src/middleware/security_middleware.py", "comments": "Security Middleware."}]}, {"Type": "Folder", "Name": "~", "Path": "./server/src/~", "Children": []}]}, {"Type": "Folder", "Name": "tests", "Path": "./server/tests", "Children": [{"Type": "Folder", "Name": "api", "Path": "./server/tests/api", "Children": [{"Type": "File", "Name": "conftest.py", "Path": "./server/tests/api/conftest.py", "comments": "Test fixtures for API layer tests."}, {"Type": "Folder", "Name": "v1", "Path": "./server/tests/api/v1", "Children": [{"Type": "File", "Name": "test_auth_routes.py", "Path": "./server/tests/api/v1/test_auth_routes.py", "comments": "Tests for authentication API endpoints."}, {"Type": "File", "Name": "test_component_category_routes.py", "Path": "./server/tests/api/v1/test_component_category_routes.py", "comments": "Tests for Component Category functionality."}, {"Type": "File", "Name": "test_component_routes.py", "Path": "./server/tests/api/v1/test_component_routes.py", "comments": "Component API Routes Tests."}, {"Type": "File", "Name": "test_component_type_routes.py", "Path": "./server/tests/api/v1/test_component_type_routes.py", "comments": "Tests for Component Type functionality."}, {"Type": "File", "Name": "test_health_routes.py", "Path": "./server/tests/api/v1/test_health_routes.py", "comments": "Tests for health check API endpoints."}, {"Type": "File", "Name": "test_project_routes.py", "Path": "./server/tests/api/v1/test_project_routes.py", "comments": "Tests for project management API endpoints."}, {"Type": "File", "Name": "test_task_routes.py", "Path": "./server/tests/api/v1/test_task_routes.py", "comments": "Integration tests for Task API endpoints."}, {"Type": "File", "Name": "test_user_routes.py", "Path": "./server/tests/api/v1/test_user_routes.py", "comments": "Tests for user management API endpoints."}]}]}, {"Type": "File", "Name": "conftest.py", "Path": "./server/tests/conftest.py", "comments": "Global test configuration."}, {"Type": "Folder", "Name": "core", "Path": "./server/tests/core", "Children": [{"Type": "Folder", "Name": "calculations", "Path": "./server/tests/core/calculations", "Children": [{"Type": "File", "Name": "conftest.py", "Path": "./server/tests/core/calculations/conftest.py", "comments": "Fixtures for calculations layer tests."}]}, {"Type": "Folder", "Name": "config", "Path": "./server/tests/core/config", "Children": [{"Type": "File", "Name": "test_settings.py", "Path": "./server/tests/core/config/test_settings.py", "comments": "Test suite for application settings."}]}, {"Type": "Folder", "Name": "database", "Path": "./server/tests/core/database", "Children": [{"Type": "File", "Name": "test_alembic_migration_automation.py", "Path": "./server/tests/core/database/test_alembic_migration_automation.py", "comments": "Test suite for Alembic migration automation with data integrity validation."}, {"Type": "File", "Name": "test_connection_manager.py", "Path": "./server/tests/core/database/test_connection_manager.py", "comments": "Unit tests for DynamicConnectionManager."}, {"Type": "File", "Name": "test_connection_manager_integration.py", "Path": "./server/tests/core/database/test_connection_manager_integration.py", "comments": "Integration tests for DynamicConnectionManager with database connectivity."}, {"Type": "File", "Name": "test_migration_rollback_scenarios.py", "Path": "./server/tests/core/database/test_migration_rollback_scenarios.py", "comments": "Test suite for Alembic migration rollback scenarios."}, {"Type": "File", "Name": "test_project_database_routing.py", "Path": "./server/tests/core/database/test_project_database_routing.py", "comments": "Integration tests for project-specific database routing."}, {"Type": "File", "Name": "test_task_migration.py", "Path": "./server/tests/core/database/test_task_migration.py", "comments": "Test suite for Task and TaskAssignment migration."}]}, {"Type": "Folder", "Name": "errors", "Path": "./server/tests/core/errors", "Children": [{"Type": "File", "Name": "test_error_context.py", "Path": "./server/tests/core/errors/test_error_context.py", "comments": "Test suite for unified error handling system."}, {"Type": "File", "Name": "test_error_handler_decorators.py", "Path": "./server/tests/core/errors/test_error_handler_decorators.py", "comments": "Test suite for unified error handling system."}, {"Type": "File", "Name": "test_error_handling_result.py", "Path": "./server/tests/core/errors/test_error_handling_result.py", "comments": "Test suite for unified error handling system."}, {"Type": "File", "Name": "test_unified_error_handler.py", "Path": "./server/tests/core/errors/test_unified_error_handler.py", "comments": "Test suite for unified error handling system."}]}, {"Type": "Folder", "Name": "models", "Path": "./server/tests/core/models", "Children": [{"Type": "File", "Name": "test_component.py", "Path": "./server/tests/core/models/test_component.py", "comments": "Unit tests for Component model."}, {"Type": "File", "Name": "test_component_category.py", "Path": "./server/tests/core/models/test_component_category.py", "comments": "Unit tests for Component Category functionality."}, {"Type": "File", "Name": "test_component_relational.py", "Path": "./server/tests/core/models/test_component_relational.py", "comments": "Unit tests for Component model with relational approach."}, {"Type": "File", "Name": "test_component_type.py", "Path": "./server/tests/core/models/test_component_type.py", "comments": "Unit tests for Component Type functionality."}, {"Type": "File", "Name": "test_project_model_database_url.py", "Path": "./server/tests/core/models/test_project_model_database_url.py", "comments": "Unit tests for Project model database_url field functionality."}, {"Type": "File", "Name": "test_synchronization_log_model.py", "Path": "./server/tests/core/models/test_synchronization_log_model.py", "comments": "Unit tests for SynchronizationLog and SynchronizationConflict models."}, {"Type": "File", "Name": "test_task.py", "Path": "./server/tests/core/models/test_task.py", "comments": "Unit tests for Task and TaskAssignment models."}]}, {"Type": "Folder", "Name": "repositories", "Path": "./server/tests/core/repositories", "Children": [{"Type": "File", "Name": "conftest.py", "Path": "./server/tests/core/repositories/conftest.py", "comments": "Test fixtures for repository layer tests."}, {"Type": "File", "Name": "test_component_category_repository.py", "Path": "./server/tests/core/repositories/test_component_category_repository.py", "comments": "Unit tests for Component Category functionality."}, {"Type": "File", "Name": "test_component_repository.py", "Path": "./server/tests/core/repositories/test_component_repository.py", "comments": "Unit tests for Component Repository."}, {"Type": "File", "Name": "test_component_type_repository.py", "Path": "./server/tests/core/repositories/test_component_type_repository.py", "comments": "Unit tests for Component Type functionality."}, {"Type": "File", "Name": "test_task_repository.py", "Path": "./server/tests/core/repositories/test_task_repository.py", "comments": "Unit tests for TaskRepository."}]}, {"Type": "Folder", "Name": "security", "Path": "./server/tests/core/security", "Children": [{"Type": "File", "Name": "test_input_validators.py", "Path": "./server/tests/core/security/test_input_validators.py", "comments": "Unit tests for input validation modules."}, {"Type": "File", "Name": "test_password_handler.py", "Path": "./server/tests/core/security/test_password_handler.py", "comments": "Unit tests for the PasswordHandler class."}]}, {"Type": "Folder", "Name": "services", "Path": "./server/tests/core/services", "Children": [{"Type": "File", "Name": "conftest.py", "Path": "./server/tests/core/services/conftest.py", "comments": "Test fixtures for service layer tests."}, {"Type": "File", "Name": "test_component_category_service.py", "Path": "./server/tests/core/services/test_component_category_service.py", "comments": "Unit tests for Component Category functionality."}, {"Type": "File", "Name": "test_component_service.py", "Path": "./server/tests/core/services/test_component_service.py", "comments": "Unit tests for Component Service."}, {"Type": "File", "Name": "test_component_type_service.py", "Path": "./server/tests/core/services/test_component_type_service.py", "comments": "Unit tests for Component Type functionality."}, {"Type": "File", "Name": "test_project_member_service.py", "Path": "./server/tests/core/services/test_project_member_service.py"}, {"Type": "File", "Name": "test_project_service.py", "Path": "./server/tests/core/services/test_project_service.py"}, {"Type": "File", "Name": "test_project_service_database_url.py", "Path": "./server/tests/core/services/test_project_service_database_url.py", "comments": "Unit tests for ProjectService database URL integration."}, {"Type": "File", "Name": "test_synchronization_service_conflict_resolution.py", "Path": "./server/tests/core/services/test_synchronization_service_conflict_resolution.py", "comments": "Unit tests for SynchronizationService conflict resolution methods."}, {"Type": "File", "Name": "test_synchronization_service_main_orchestration.py", "Path": "./server/tests/core/services/test_synchronization_service_main_orchestration.py", "comments": "Unit tests for SynchronizationService main orchestration method."}, {"Type": "File", "Name": "test_synchronization_service_utilities.py", "Path": "./server/tests/core/services/test_synchronization_service_utilities.py", "comments": "Unit tests for SynchronizationService utility methods."}, {"Type": "File", "Name": "test_task_manager_service.py", "Path": "./server/tests/core/services/test_task_manager_service.py", "comments": "Unit and integration tests for TaskManagerService."}, {"Type": "File", "Name": "test_user_service.py", "Path": "./server/tests/core/services/test_user_service.py", "comments": "Unit tests for the UserService class."}]}, {"Type": "Folder", "Name": "utils", "Path": "./server/tests/core/utils", "Children": [{"Type": "File", "Name": "test_advanced_search.py", "Path": "./server/tests/core/utils/test_advanced_search.py", "comments": "Unit tests for Advanced Component Search functionality."}]}]}, {"Type": "Folder", "Name": "integration", "Path": "./server/tests/integration", "Children": [{"Type": "File", "Name": "test_component_management_workflow.py", "Path": "./server/tests/integration/test_component_management_workflow.py", "comments": "Comprehensive integration tests for Component Management API workflow."}, {"Type": "File", "Name": "test_comprehensive_data_integrity.py", "Path": "./server/tests/integration/test_comprehensive_data_integrity.py", "comments": "Comprehensive Data Integrity Testing Suite"}, {"Type": "File", "Name": "test_constraint_violations.py", "Path": "./server/tests/integration/test_constraint_violations.py", "comments": "Comprehensive Constraint Violation Testing"}, {"Type": "File", "Name": "test_data_integrity.py", "Path": "./server/tests/integration/test_data_integrity.py", "comments": "This module contains tests for ensuring data integrity across various layers"}, {"Type": "File", "Name": "test_middleware_integration.py", "Path": "./server/tests/integration/test_middleware_integration.py", "comments": "Integration tests for the complete middleware stack."}, {"Type": "File", "Name": "test_synchronization_service_cdc.py", "Path": "./server/tests/integration/test_synchronization_service_cdc.py", "comments": "Integration tests for SynchronizationService Change Data Capture (CDC) implementation."}, {"Type": "File", "Name": "test_synchronization_service_conflict_integration.py", "Path": "./server/tests/integration/test_synchronization_service_conflict_integration.py", "comments": "Integration tests for SynchronizationService conflict resolution integration."}, {"Type": "File", "Name": "test_synchronization_service_log_integration.py", "Path": "./server/tests/integration/test_synchronization_service_log_integration.py", "comments": "Integration tests for SynchronizationService with SynchronizationLog model."}, {"Type": "File", "Name": "test_synchronization_service_transaction_management.py", "Path": "./server/tests/integration/test_synchronization_service_transaction_management.py", "comments": "Integration tests for SynchronizationService transaction management and sync log integration."}, {"Type": "File", "Name": "test_validation_integration.py", "Path": "./server/tests/integration/test_validation_integration.py", "comments": "Dedicated Test Suite for Immediate Action Enhancements"}]}, {"Type": "Folder", "Name": "middleware", "Path": "./server/tests/middleware", "Children": [{"Type": "File", "Name": "conftest.py", "Path": "./server/tests/middleware/conftest.py", "comments": "Test fixtures for middleware tests."}, {"Type": "File", "Name": "test_caching_middleware.py", "Path": "./server/tests/middleware/test_caching_middleware.py", "comments": "Unit tests for CachingMiddleware following robust test patterns."}, {"Type": "File", "Name": "test_context_middleware.py", "Path": "./server/tests/middleware/test_context_middleware.py", "comments": "Unit tests for ContextMiddleware following robust test patterns."}, {"Type": "File", "Name": "test_logging_middleware.py", "Path": "./server/tests/middleware/test_logging_middleware.py", "comments": "Unit tests for LoggingMiddleware following robust test patterns."}, {"Type": "File", "Name": "test_rate_limiting_middleware.py", "Path": "./server/tests/middleware/test_rate_limiting_middleware.py", "comments": "Unit tests for RateLimitingMiddleware following robust test patterns."}, {"Type": "File", "Name": "test_security_middleware.py", "Path": "./server/tests/middleware/test_security_middleware.py", "comments": "Unit tests for SecurityMiddleware."}]}, {"Type": "Folder", "Name": "performance", "Path": "./server/tests/performance", "Children": [{"Type": "File", "Name": "conftest.py", "Path": "./server/tests/performance/conftest.py", "comments": "Test fixtures for performance tests."}, {"Type": "File", "Name": "locust_validation_load_tests.py", "Path": "./server/tests/performance/locust_validation_load_tests.py", "comments": "Locust Load Tests for Validation Logic Under High Concurrency."}, {"Type": "File", "Name": "test_component_performance.py", "Path": "./server/tests/performance/test_component_performance.py", "comments": "Performance benchmarks for Component operations."}, {"Type": "File", "Name": "test_concurrent_validation_stress.py", "Path": "./server/tests/performance/test_concurrent_validation_stress.py", "comments": "Concurrent Validation Stress Tests."}, {"Type": "File", "Name": "test_database_performance.py", "Path": "./server/tests/performance/test_database_performance.py", "comments": "Database Performance and Stress Testing."}, {"Type": "File", "Name": "test_email_lookup_benchmarks.py", "Path": "./server/tests/performance/test_email_lookup_benchmarks.py", "comments": "Email Lookup Performance Benchmarks with Large Datasets."}, {"Type": "File", "Name": "test_email_lookup_scale_performance.py", "Path": "./server/tests/performance/test_email_lookup_scale_performance.py", "comments": "Scale Performance Tests for Case-Insensitive Email Lookups."}, {"Type": "File", "Name": "test_memory_usage_concurrency.py", "Path": "./server/tests/performance/test_memory_usage_concurrency.py", "comments": "Memory Usage Tests Under High Concurrency."}, {"Type": "File", "Name": "test_performance_optimization.py", "Path": "./server/tests/performance/test_performance_optimization.py", "comments": "Tests for Performance Optimization functionality."}, {"Type": "File", "Name": "test_validation_pipeline_performance.py", "Path": "./server/tests/performance/test_validation_pipeline_performance.py", "comments": "Validation Pipeline Performance Tests."}]}, {"Type": "File", "Name": "test_runner.py", "Path": "./server/tests/test_runner.py", "comments": "Comprehensive Testing Script."}, {"Type": "Folder", "Name": "validation", "Path": "./server/tests/validation", "Children": [{"Type": "File", "Name": "test_advanced_validators.py", "Path": "./server/tests/validation/test_advanced_validators.py", "comments": "Unit tests for advanced validation system."}, {"Type": "File", "Name": "test_compatibility_matrix.py", "Path": "./server/tests/validation/test_compatibility_matrix.py", "comments": "Unit tests for compatibility matrix validation system."}, {"Type": "File", "Name": "test_data_format_validator.py", "Path": "./server/tests/validation/test_data_format_validator.py", "comments": "Unit tests for multi-format data compatibility validation."}, {"Type": "File", "Name": "test_json_schema_validator.py", "Path": "./server/tests/validation/test_json_schema_validator.py", "comments": "Unit tests for advanced JSON schema validation with JSON path queries."}, {"Type": "File", "Name": "test_legacy_migration_validator.py", "Path": "./server/tests/validation/test_legacy_migration_validator.py", "comments": "Unit tests for legacy data format migration validation system."}, {"Type": "File", "Name": "test_parallel_processor.py", "Path": "./server/tests/validation/test_parallel_processor.py", "comments": "Unit tests for parallel validation processing system."}, {"Type": "File", "Name": "test_standards_validator.py", "Path": "./server/tests/validation/test_standards_validator.py", "comments": "Unit tests for standards compliance validation system."}]}]}, {"Type": "Folder", "Name": "~", "Path": "./server/~", "Children": []}]}, {"Type": "Folder", "Name": "~", "Path": "./~", "Children": []}]}