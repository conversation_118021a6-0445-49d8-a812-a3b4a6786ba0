# Standardized Mock Strategy

## Ultimate Electrical Designer - Test Infrastructure Standardization

**Document Version:** 1.0  
**Date:** August 7, 2025  
**Task:** 2.1.2 - Design Standardized Mock Strategy  

---

## Executive Summary

This document defines a standardized mock strategy to resolve test failures caused by inconsistent mock usage patterns. The strategy provides clear guidelines for unit tests (service layer) and integration tests (API layer) with specific patterns to address ID mismatch issues and mock assertion failures.

## Core Principles

### 1. **Clear Test Type Separation**
- **Unit Tests**: Mock all external dependencies, test business logic in isolation
- **Integration Tests**: Use real database and services, test full request/response cycle
- **No Mixed Patterns**: Never combine mock expectations with real database operations

### 2. **Predictable Test Data**
- **Unit Tests**: Use predictable mock IDs (1, 2, 3, etc.)
- **Integration Tests**: Use ID-agnostic assertions or predictable data creation
- **Consistent Factories**: Standardized test data creation patterns

### 3. **Consistent Fixture Organization**
- **Naming Convention**: `mock_*` for mocks, `test_*` for real data, `sample_*` for schemas
- **Location-Based**: Service mocks in service test directories, API fixtures in API directories
- **Shared Utilities**: Common patterns in shared fixture modules

## Unit Test Strategy (Service Layer)

### Mock Data Factories

```python
# tests/fixtures/mock_factories.py
class MockDataFactory:
    """Standardized mock data creation for unit tests."""
    
    @staticmethod
    def create_user(id: int = 1, name: str = "Test User", email: str = "<EMAIL>") -> User:
        """Create predictable mock user with default ID 1."""
        return User(
            id=id,
            name=name,
            email=email,
            password_hash="mock_hash",
            is_active=True,
            is_superuser=False,
            created_at=datetime(2024, 1, 1),
            updated_at=datetime(2024, 1, 1),
        )
    
    @staticmethod
    def create_project(id: int = 1, user_id: int = 1, name: str = "Test Project") -> Project:
        """Create predictable mock project with default ID 1."""
        return Project(
            id=id,
            name=name,
            description="Test project description",
            project_number=f"PRJ-{id:04d}",
            status="ACTIVE",
            client="Test Client",
            created_by_user_id=user_id,
            created_at=datetime(2024, 1, 1),
            updated_at=datetime(2024, 1, 1),
        )
    
    @staticmethod
    def create_component_category(id: int = 1, name: str = "Test Category") -> ComponentCategory:
        """Create predictable mock component category."""
        return ComponentCategory(
            id=id,
            name=name,
            description="Test category description",
            is_active=True,
            parent_category_id=None,
            created_at=datetime(2024, 1, 1),
            updated_at=datetime(2024, 1, 1),
        )
```

### Standardized Mock Repository Pattern

```python
# tests/core/services/conftest.py
@pytest.fixture
def mock_user_repository():
    """Standardized mock user repository for unit tests."""
    mock_repo = AsyncMock(spec=UserRepository)
    
    # Configure predictable return values
    mock_repo.create.return_value = MockDataFactory.create_user(id=1)
    mock_repo.get_by_id.return_value = MockDataFactory.create_user(id=1)
    mock_repo.get_by_email.return_value = MockDataFactory.create_user(id=1)
    mock_repo.check_email_exists.return_value = False
    
    # Configure session methods
    mock_repo.db_session = AsyncMock()
    mock_repo.db_session.flush = AsyncMock()
    mock_repo.db_session.commit = AsyncMock()
    mock_repo.db_session.refresh = AsyncMock()
    
    return mock_repo

@pytest.fixture
def user_service(mock_user_repository, mock_user_preference_repository):
    """User service with standardized mocks."""
    return UserService(
        user_repository=mock_user_repository,
        preference_repository=mock_user_preference_repository
    )
```

### Unit Test Example

```python
# tests/core/services/test_user_service.py
class TestUserService:
    async def test_create_user_success(self, user_service, mock_user_repository):
        """Test successful user creation with predictable mock data."""
        user_data = UserCreateSchema(
            name="New User",
            email="<EMAIL>",
            password="SecurePass123"
        )
        
        # Configure mock to return predictable user
        expected_user = MockDataFactory.create_user(id=1, name="New User", email="<EMAIL>")
        mock_user_repository.create.return_value = expected_user
        mock_user_repository.check_email_exists.return_value = False
        
        # Execute
        result = await user_service.create_user(user_data)
        
        # Assert with predictable expectations
        assert result.id == 1  # Predictable mock ID
        assert result.name == "New User"
        assert result.email == "<EMAIL>"
        
        # Verify mock calls
        mock_user_repository.check_email_exists.assert_called_once_with("<EMAIL>")
        mock_user_repository.create.assert_called_once()
```

## Integration Test Strategy (API Layer)

### Predictable Test Data Creation

```python
# tests/fixtures/integration_factories.py
class IntegrationDataFactory:
    """Standardized real data creation for integration tests."""
    
    @staticmethod
    async def create_test_user(
        async_db_session,
        name: str = "Test User",
        email: str = None,
        password: str = "SecurePass123"
    ) -> User:
        """Create real test user with predictable data."""
        if email is None:
            unique_suffix = str(uuid.uuid4())[:8]
            email = f"test.{unique_suffix}@example.com"
        
        user_repo = UserRepository(async_db_session)
        preference_repo = UserPreferenceRepository(async_db_session)
        user_service = UserService(user_repository=user_repo, preference_repository=preference_repo)
        
        user_data = UserCreateSchema(name=name, email=email, password=password)
        return await user_service.create_user(user_data)
    
    @staticmethod
    def create_test_project(
        db_session,
        user: User,
        name: str = None,
        project_number: str = None
    ) -> Project:
        """Create real test project with predictable data."""
        if name is None:
            unique_suffix = str(uuid.uuid4())[:8]
            name = f"Test Project {unique_suffix}"
        
        if project_number is None:
            unique_suffix = str(uuid.uuid4())[:8]
            project_number = f"PRJ-{unique_suffix}"
        
        project = Project(
            name=name,
            description="Integration test project",
            project_number=project_number,
            status=ProjectStatus.ACTIVE.value,
            client="Test Client",
            created_by_user_id=user.id,
        )
        
        db_session.add(project)
        db_session.flush()
        db_session.refresh(project)
        return project
```

### ID-Agnostic Assertion Helpers

```python
# tests/utils/assertion_helpers.py
class AssertionHelpers:
    """Helpers for ID-agnostic assertions in integration tests."""
    
    @staticmethod
    def assert_user_properties(actual_user: dict, expected_name: str, expected_email: str):
        """Assert user properties without depending on specific ID."""
        assert isinstance(actual_user.get("id"), int)
        assert actual_user["id"] > 0
        assert actual_user["name"] == expected_name
        assert actual_user["email"] == expected_email
        assert actual_user["is_active"] is True
    
    @staticmethod
    def assert_project_properties(actual_project: dict, expected_name: str, user_id: int):
        """Assert project properties with known user relationship."""
        assert isinstance(actual_project.get("id"), int)
        assert actual_project["id"] > 0
        assert actual_project["name"] == expected_name
        assert actual_project["created_by_user_id"] == user_id
        assert actual_project["status"] == "ACTIVE"
    
    @staticmethod
    def assert_category_properties(actual_category: dict, expected_name: str):
        """Assert category properties without depending on specific ID."""
        assert isinstance(actual_category.get("id"), int)
        assert actual_category["id"] > 0
        assert actual_category["name"] == expected_name
        assert actual_category["is_active"] is True
```

### Integration Test Example

```python
# tests/api/v1/test_component_category_routes.py
class TestComponentCategoryAPI:
    async def test_create_category_endpoint(self, authenticated_client, test_project):
        """Test category creation with ID-agnostic assertions."""
        category_data = {
            "name": "Integration Test Category",
            "description": "Integration test description",
            "is_active": True,
        }
        
        response = await authenticated_client.post(
            f"/api/v1/projects/{test_project.id}/components/component-categories/",
            json=category_data,
        )
        
        assert response.status_code == 201
        
        # Use ID-agnostic assertions
        data = response.json()
        AssertionHelpers.assert_category_properties(data, "Integration Test Category")
        assert data["description"] == "Integration test description"
        
        # Store ID for subsequent operations
        category_id = data["id"]
        assert isinstance(category_id, int)
        assert category_id > 0
```

## Fixture Organization

### Naming Convention

| **Prefix** | **Purpose** | **Example** | **Usage** |
|------------|-------------|-------------|-----------|
| `mock_*` | Unit test mocks | `mock_user_repository` | Service layer tests |
| `test_*` | Integration test real data | `test_user`, `test_project` | API layer tests |
| `sample_*` | Test data schemas/DTOs | `sample_user_data` | Both test types |

### File Organization

```
tests/
├── conftest.py                 # Global fixtures (database, authentication)
├── fixtures/
│   ├── mock_factories.py       # Mock data factories for unit tests
│   ├── integration_factories.py # Real data factories for integration tests
│   └── assertion_helpers.py    # ID-agnostic assertion utilities
├── core/
│   └── services/
│       └── conftest.py         # Service layer mock fixtures
└── api/
    └── conftest.py             # API layer integration fixtures
```

## Implementation Guidelines

### 1. **Unit Test Migration Pattern**

```python
# Before (problematic)
async def test_delete_component(self, component_service):
    result = await component_service.delete_component(1, deleted_by_user_id=1)
    # Fails if real database creates user with ID 35803

# After (standardized)
async def test_delete_component(self, component_service, mock_component_repository):
    mock_user = MockDataFactory.create_user(id=1)
    mock_component = MockDataFactory.create_component(id=1)
    
    mock_component_repository.get_by_id.return_value = mock_component
    mock_component_repository.soft_delete.return_value = True
    
    result = await component_service.delete_component(1, deleted_by_user_id=mock_user.id)
    
    # Predictable assertion with mock data
    mock_component_repository.soft_delete.assert_called_once_with(1, deleted_by_user_id=1)
```

### 2. **Integration Test Migration Pattern**

```python
# Before (problematic)
async def test_create_category(self, authenticated_client, test_project):
    response = await authenticated_client.post(url, json=data)
    assert response.json()["id"] == 1  # Fails with real database

# After (standardized)
async def test_create_category(self, authenticated_client, test_project):
    response = await authenticated_client.post(url, json=data)
    data = response.json()
    
    # ID-agnostic assertion
    AssertionHelpers.assert_category_properties(data, "Expected Name")
    
    # Store ID for subsequent operations
    category_id = data["id"]
```

## Success Criteria

### ✅ **Unit Tests**
- [ ] All service tests use `MockDataFactory` for predictable data
- [ ] All repository dependencies are mocked with `AsyncMock`
- [ ] All assertions use predictable mock IDs (1, 2, 3, etc.)
- [ ] No real database operations in unit tests
- [ ] Mock assertions work consistently

### ✅ **Integration Tests**
- [ ] All API tests use `IntegrationDataFactory` for real data creation
- [ ] All assertions use `AssertionHelpers` for ID-agnostic validation
- [ ] Real database operations with transaction isolation
- [ ] Predictable test data creation patterns
- [ ] No mock assertions on real database operations

### ✅ **Overall**
- [ ] Zero mock assertion failures due to ID mismatches
- [ ] Clear separation between unit and integration test patterns
- [ ] Consistent fixture usage across similar tests
- [ ] Comprehensive documentation and examples

## Next Steps

1. **Task 2.1.3**: Implement mock data factories and assertion helpers
2. **Task 2.1.4**: Migrate existing tests to use standardized patterns
3. **Task 2.1.5**: Validate mock strategy standardization
4. **Task 2.2.1**: Address HTTP status code contract violations

**Quality Gate**: ✅ Standardized mock strategy designed and documented
- Clear patterns for unit and integration tests
- Mock data factories and assertion helpers specified
- Implementation guidelines provided
- Success criteria defined
