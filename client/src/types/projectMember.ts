/**
 * Zod schemas and TypeScript definitions for ProjectMember based on Pydantic schemas.
 */
import { z } from "zod"

import {
  BaseSchema,
  CreatePaginatedResponseSchema,
  TimestampMixinSchema,
} from "./schemas/baseSchemas"
import { UserReadSchema } from "./user"
import { UserRoleBaseSchema } from "./userRole"

// ========================= # PROJECT MEMBER # =========================
// ========================= # BASE # =========================

export const ProjectMemberBaseSchema = BaseSchema.extend({
  name: z.string().trim().describe("Name/title for this membership"),
  user_id: z.number().int().describe("User ID of the member"),
  project_id: z.number().int().describe("Project ID"),
  role_id: z.number().int().describe("Role ID of the member in the project"),
  is_active: z
    .boolean()
    .default(true)
    .describe("Whether the membership is active"),
  expires_at: z.coerce
    .date()
    .optional()
    .nullable()
    .describe("When the membership expires"),
})

// ========================= # CRUD # =========================

export const ProjectMemberCreateSchema = z.object({
  name: z
    .string()
    .trim()
    .min(1)
    .optional()
    .describe(
      "Name/title for this membership (auto-generated if not provided)"
    ),
  user_id: z.number().int().min(1).describe("User ID to add to the project"),
  role_id: z
    .number()
    .int()
    .min(1)
    .describe("Role ID for the user in the project"),
  expires_at: z.coerce
    .date()
    .optional()
    .nullable()
    .describe("When the membership expires"),
})

export const ProjectMemberUpdateSchema = z.object({
  role_id: z
    .number()
    .int()
    .min(1)
    .optional()
    .describe("New role ID for the member"),
  is_active: z.boolean().optional().describe("New status for the membership"),
  expires_at: z.coerce
    .date()
    .optional()
    .nullable()
    .describe("New expiration date"),
})

export const ProjectMemberReadSchema = ProjectMemberBaseSchema.merge(
  TimestampMixinSchema
).extend({
  id: z.number().int().describe("Unique identifier for the membership"),
  user: UserReadSchema.describe("User details"),
  role: UserRoleBaseSchema.describe("Role details"),
})

// ========================= # OTHER # =========================

export const ProjectMemberPaginatedResponseSchema =
  CreatePaginatedResponseSchema(ProjectMemberReadSchema)

// ===================== # TYPE DEFINITIONS # =====================

export type ProjectMember = z.infer<typeof ProjectMemberBaseSchema>
export type ProjectMemberCreate = z.infer<typeof ProjectMemberCreateSchema>
export type ProjectMemberUpdate = z.infer<typeof ProjectMemberUpdateSchema>
export type ProjectMemberRead = z.infer<typeof ProjectMemberReadSchema>
export type ProjectMemberPaginatedResponse = z.infer<
  typeof ProjectMemberPaginatedResponseSchema
>
