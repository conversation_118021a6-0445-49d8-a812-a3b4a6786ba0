/**
 * Zod schemas and TypeScript definitions for ComponentCategory based on Pydantic schemas.
 */
import { z } from "zod"

import {
  BaseSchema,
  CreatePaginatedResponseSchema,
  TimestampMixinSchema,
} from "./schemas/baseSchemas"

// ========================= # COMPONENT CATEGORY # =========================
// ========================= # BASE # =========================

export const ComponentCategoryBaseSchema = BaseSchema.extend({
  id: z.number().int().describe("Category ID"),
  name: z.string().trim().min(1).max(100).describe("Category name"),
  description: z
    .string()
    .trim()
    .max(1000)
    .optional()
    .nullable()
    .describe("Detailed category description"),
  parent_category_id: z
    .number()
    .int()
    .min(1)
    .optional()
    .nullable()
    .describe("Parent category ID for hierarchical organization"),
  is_active: z
    .boolean()
    .default(true)
    .describe("Whether category is active in the system"),
})

// ========================= # CRUD # =========================

export const ComponentCategoryCreateSchema = ComponentCategoryBaseSchema

export const ComponentCategoryUpdateSchema = z.object({
  name: z.string().trim().min(1).max(100).optional().describe("Category name"),
  description: z
    .string()
    .trim()
    .max(1000)
    .optional()
    .nullable()
    .describe("Detailed category description"),
  parent_category_id: z
    .number()
    .int()
    .min(1)
    .optional()
    .nullable()
    .describe("Parent category ID for hierarchical organization"),
  is_active: z
    .boolean()
    .optional()
    .describe("Whether category is active in the system"),
})

export const ComponentCategoryReadSchema = ComponentCategoryBaseSchema.merge(
  TimestampMixinSchema
).extend({
  full_path: z.string().describe("Full hierarchical path"),
  level: z.number().int().describe("Hierarchical level"),
  is_root_category: z.boolean().describe("Whether this is a root category"),
  has_children: z.boolean().describe("Whether category has child categories"),
  component_count: z
    .number()
    .int()
    .describe("Number of component types in category"),
})

// ========================= # OTHER # =========================

export const ComponentCategoryPaginatedResponseSchema =
  CreatePaginatedResponseSchema(ComponentCategoryReadSchema)

export const ComponentCategorySummarySchema = BaseSchema.extend({
  id: z.number().int().describe("Category ID"),
  name: z.string().describe("Category name"),
  description: z
    .string()
    .optional()
    .nullable()
    .describe("Category description"),
  parent_category_id: z
    .number()
    .int()
    .optional()
    .nullable()
    .describe("Parent category ID"),
  is_active: z.boolean().describe("Whether category is active"),
  component_count: z.number().int().describe("Number of component types"),
  child_count: z.number().int().describe("Number of child categories"),
})

/**
 * Schema for hierarchical category tree representation.
 * This uses a recursive type definition for nested category structures.
 */
export const ComponentCategoryTreeNodeSchema: z.ZodType<{
  id: number
  name: string
  description?: string | null
  is_active: boolean
  level: number
  component_count: number
  children?: ComponentCategoryTreeNode[]
}> = z.lazy(() =>
  z.object({
    id: z.number().int().describe("Category ID"),
    name: z.string().describe("Category name"),
    description: z
      .string()
      .optional()
      .nullable()
      .describe("Category description"),
    is_active: z.boolean().describe("Whether category is active"),
    level: z.number().int().describe("Hierarchical level"),
    component_count: z.number().int().describe("Number of component types"),
    children: z
      .array(ComponentCategoryTreeNodeSchema)
      .default([])
      .describe("Child categories"),
  })
)

export const ComponentCategoryTreeResponseSchema = z.object({
  tree: z
    .array(ComponentCategoryTreeNodeSchema)
    .describe("Hierarchical category tree"),
  total_categories: z.number().int().describe("Total number of categories"),
  max_depth: z.number().int().describe("Maximum tree depth"),
})

export const ComponentCategorySearchSchema = z.object({
  search_term: z
    .string()
    .trim()
    .min(1)
    .max(100)
    .optional()
    .describe("Search term for name or description"),
  parent_category_id: z
    .number()
    .int()
    .min(1)
    .optional()
    .describe("Filter by parent category"),
  is_active: z.boolean().optional().describe("Filter by active status"),
  include_children: z
    .boolean()
    .default(false)
    .describe("Include child categories in results"),
  min_component_count: z
    .number()
    .int()
    .min(0)
    .optional()
    .describe("Minimum number of component types"),
  max_component_count: z
    .number()
    .int()
    .min(0)
    .optional()
    .describe("Maximum number of component types"),
})

export const ComponentCategoryBulkCreateSchema = z.object({
  categories: z
    .array(ComponentCategoryCreateSchema)
    .min(1)
    .max(100)
    .describe("List of categories to create"),
  validate_hierarchy: z
    .boolean()
    .default(true)
    .describe("Whether to validate hierarchical relationships"),
  skip_duplicates: z
    .boolean()
    .default(false)
    .describe("Whether to skip duplicate categories"),
})

export const ComponentCategoryValidationResultSchema = z.object({
  is_valid: z.boolean().describe("Whether validation passed"),
  errors: z.array(z.string()).default([]).describe("Validation errors"),
  warnings: z.array(z.string()).default([]).describe("Validation warnings"),
  category_id: z
    .number()
    .int()
    .optional()
    .describe("Category ID if applicable"),
})

export const ComponentCategoryStatsSchema = z.object({
  total_categories: z.number().int().describe("Total number of categories"),
  active_categories: z.number().int().describe("Number of active categories"),
  root_categories: z.number().int().describe("Number of root categories"),
  max_depth: z.number().int().describe("Maximum hierarchy depth"),
  avg_component_types_per_category: z
    .number()
    .describe("Average component types per category"),
  categories_with_no_types: z
    .number()
    .int()
    .describe("Categories with no component types"),
})

// ===================== # TYPE DEFINITIONS # =====================

export type ComponentCategory = z.infer<typeof ComponentCategoryBaseSchema>
export type ComponentCategoryCreate = z.infer<
  typeof ComponentCategoryCreateSchema
>
export type ComponentCategoryUpdate = z.infer<
  typeof ComponentCategoryUpdateSchema
>
export type ComponentCategoryRead = z.infer<typeof ComponentCategoryReadSchema>
export type ComponentCategoryPaginatedResponse = z.infer<
  typeof ComponentCategoryPaginatedResponseSchema
>
export type ComponentCategorySummary = z.infer<
  typeof ComponentCategorySummarySchema
>
export type ComponentCategoryTreeNode = z.infer<
  typeof ComponentCategoryTreeNodeSchema
>
export type ComponentCategoryTreeResponse = z.infer<
  typeof ComponentCategoryTreeResponseSchema
>
export type ComponentCategorySearch = z.infer<
  typeof ComponentCategorySearchSchema
>
export type ComponentCategoryBulkCreate = z.infer<
  typeof ComponentCategoryBulkCreateSchema
>
export type ComponentCategoryValidationResult = z.infer<
  typeof ComponentCategoryValidationResultSchema
>
export type ComponentCategoryStats = z.infer<
  typeof ComponentCategoryStatsSchema
>
