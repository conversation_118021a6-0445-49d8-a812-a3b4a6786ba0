/**
 * TypeScript type definitions for the API.
 */
import { Activity<PERSON><PERSON><PERSON><PERSON>er, AuditTrailFilter } from "./audit"
import { ErrorResponse } from "./schemas"

export * from "./audit"
export * from "./auth"
export * from "./component"
export * from "./componentCategory"
export * from "./config"
export * from "./schemas/enums"
export * from "./nav"
export * from "./project"
export * from "./projectMember"
export * from "./schemas"
export * from "./task"
export * from "./user"
export * from "./userRole"

//===================== # BASE TYPE DEFINITIONS # =====================//

export interface Pagination {
  page: number
  size: number
  total?: number | null
  pages?: number | null
}

export interface PaginatedResponse<T> {
  items: T[]
  pagination?: Pagination
  // Direct access properties for flat structure support
  page?: number
  size?: number
  pages?: number
  total?: number
}

export interface ApiResponse<T = unknown> {
  data?: T
  error?: ErrorResponse
  status: number
}

export interface ListQueryParams {
  skip?: number | null
  limit?: number | null
  search?: string | ""
  sort_by?: string | ""
  sort_order?: "asc" | "desc"
}

export interface HealthCheck {
  status: string
  timestamp: string
  version: string
}

//===================== # REACT QUERY KEYS # =====================//

export const QueryKeys = {
  auth: {
    me: ["auth", "me"] as const,
    profile: ["auth", "profile"] as const,
  },
  users: {
    single: (id: number) => ["users", id] as const,
    all: ["users"] as const,
    lists: () => [...QueryKeys.users.all, "list"] as const,
    list: (filters: ListQueryParams) =>
      [...QueryKeys.users.lists(), filters] as const,
    details: () => [...QueryKeys.users.all, "detail"] as const,
    detail: (id: number) => [...QueryKeys.users.details(), id] as const,
    summary: () => [...QueryKeys.users.all, "summary"] as const,
  },
  roles: {
    roles: ["roles"] as const,
    rolesList: (params?: ListQueryParams) => ["roles", "list", params] as const,
    role: (id: number) => ["roles", id] as const,
    roleHierarchy: ["roles", "hierarchy"] as const,
    rolePermissions: (id: number) => ["roles", id, "permissions"] as const,

    roleAssignments: ["role-assignments"] as const,
    roleAssignmentsList: (params?: ListQueryParams) =>
      ["role-assignments", "list", params] as const,
    roleAssignment: (id: number) => ["role-assignments", id] as const,
    userRoles: (userId: number) => ["users", userId, "roles"] as const,
    userRolesSummary: (userId: number) =>
      ["users", userId, "roles", "summary"] as const,
  },
  projects: {
    all: ["projects"] as const,
    lists: () => [...QueryKeys.projects.all, "list"] as const,
    list: (filters: ListQueryParams) =>
      [...QueryKeys.projects.lists(), filters] as const,
    details: () => [...QueryKeys.projects.all, "detail"] as const,
    detail: (id: number) => [...QueryKeys.projects.details(), id] as const,
    members: (id: number) =>
      [...QueryKeys.projects.detail(id), "members"] as const,
  },
  tasks: {
    all: ["tasks"] as const,
    lists: () => [...QueryKeys.tasks.all, "list"] as const,
    list: (projectId: number, filters?: any) =>
      [...QueryKeys.tasks.lists(), projectId, filters] as const,
    details: () => [...QueryKeys.tasks.all, "detail"] as const,
    detail: (projectId: number, taskId: string) =>
      [...QueryKeys.tasks.details(), projectId, taskId] as const,
    statistics: (projectId: number) =>
      [...QueryKeys.tasks.all, "statistics", projectId] as const,
    search: (projectId: number, searchTerm: string) =>
      [...QueryKeys.tasks.all, "search", projectId, searchTerm] as const,
    userTasks: (userId: number) =>
      [...QueryKeys.tasks.all, "user", userId] as const,
    overdue: (projectId?: number) =>
      [...QueryKeys.tasks.all, "overdue", projectId] as const,
  },
  components: {
    single: (id: number) => ["components", id] as const,
    all: ["components"] as const,
    lists: () => [...QueryKeys.components.all, "list"] as const,
    list: (filters: ListQueryParams) =>
      [...QueryKeys.components.lists(), filters] as const,
    details: () => [...QueryKeys.components.all, "detail"] as const,
    detail: (id: number) => [...QueryKeys.components.details(), id] as const,
    search: (params?: any) => ["components", "search", params] as const,
    advancedSearch: (params?: any) =>
      ["components", "advanced-search", params] as const,
    preferred: (params?: any) => ["components", "preferred", params] as const,
    stats: ["components", "stats"] as const,
    suggestions: (query: string, field?: string) =>
      ["components", "suggestions", query, field] as const,
    categories: ["components", "categories"] as const,
    types: (categoryId?: number) =>
      ["components", "types", categoryId] as const,

    // Aliases for backward compatibility
    componentsList: (filters: ListQueryParams) =>
      [...QueryKeys.components.lists(), filters] as const,
    componentsStats: ["components", "stats"] as const,
    componentsSearch: (params?: any) =>
      ["components", "search", params] as const,
    componentsAdvancedSearch: (params?: any) =>
      ["components", "advanced-search", params] as const,
    componentsPreferred: (params?: any) =>
      ["components", "preferred", params] as const,
    componentsSuggestions: (query: string, field?: string) =>
      ["components", "suggestions", query, field] as const,
    componentsCategories: ["components", "categories"] as const,
    componentsTypes: (categoryId?: number) =>
      ["components", "types", categoryId] as const,
  },
  audit: {
    activityLogs: ["activity-logs"] as const,
    activityLogsList: (params?: ActivityLogFilter) =>
      ["activity-logs", "list", params] as const,
    activityLog: (id: number) => ["activity-logs", id] as const,
    userActivityLogs: (userId: number, params?: ActivityLogFilter) =>
      ["users", userId, "activity-logs", params] as const,
    securityEvents: (params?: ActivityLogFilter) =>
      ["activity-logs", "security", params] as const,

    auditTrails: ["audit-trails"] as const,
    auditTrailsList: (params?: AuditTrailFilter) =>
      ["audit-trails", "list", params] as const,
    auditTrail: (id: number) => ["audit-trails", id] as const,
    recordHistory: (tableName: string, recordId: number) =>
      ["audit-trails", "record-history", tableName, recordId] as const,
    userActivitySummary: (userId: number) =>
      ["users", userId, "activity-summary"] as const,
    auditSummary: (params?: any) =>
      ["audit-trails", "summary", params] as const,
  },
} as const

export const MutationKeys = {
  auth: {
    login: ["auth", "login"] as const,
    logout: ["auth", "logout"] as const,
    register: ["auth", "register"] as const,
    changePassword: ["auth", "changePassword"] as const,
  },
  users: {
    create: ["users", "create"] as const,
    update: ["users", "update"] as const,
    delete: ["users", "delete"] as const,
  },
  roles: {
    createRole: ["roles", "create"] as const,
    updateRole: ["roles", "update"] as const,
    deleteRole: ["roles", "delete"] as const,

    createRoleAssignment: ["role-assignments", "create"] as const,
    updateRoleAssignment: ["role-assignments", "update"] as const,
    deleteRoleAssignment: ["role-assignments", "delete"] as const,
  },
  projects: {
    create: ["projects", "create"] as const,
    update: ["projects", "update"] as const,
    delete: ["projects", "delete"] as const,
    addMember: ["projects", "addMember"] as const,
    updateMember: ["projects", "updateMember"] as const,
    removeMember: ["projects", "removeMember"] as const,
  },
  tasks: {
    create: ["tasks", "create"] as const,
    update: ["tasks", "update"] as const,
    delete: ["tasks", "delete"] as const,
    updateStatus: ["tasks", "updateStatus"] as const,
    assignUsers: ["tasks", "assignUsers"] as const,
    unassignUser: ["tasks", "unassignUser"] as const,
  },
  components: {
    create: ["components", "create"] as const,
    update: ["components", "update"] as const,
    delete: ["components", "delete"] as const,
    bulkCreateComponents: ["components", "bulk-create"] as const,
    bulkUpdateComponents: ["components", "bulk-update"] as const,
    bulkDeleteComponents: ["components", "bulk-delete"] as const,

    // Aliases for backward compatibility
    createComponent: ["components", "create"] as const,
    updateComponent: ["components", "update"] as const,
    deleteComponent: ["components", "delete"] as const,
  },
  audit: {
    createActivityLog: ["activity-logs", "create"] as const,
    updateActivityLog: ["activity-logs", "update"] as const,

    createAuditTrail: ["audit-trails", "create"] as const,
    updateAuditTrail: ["audit-trails", "update"] as const,

    cleanupAuditLogs: ["audit-trails", "cleanup"] as const,
  },
} as const
