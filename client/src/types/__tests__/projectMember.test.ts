/**
 * Unit tests for ProjectMember Zod schemas
 */
import { describe, expect, it } from "vitest"
import {
  ProjectMemberCreateSchema,
  ProjectMemberReadSchema,
  ProjectMemberUpdateSchema,
} from "../projectMember"

describe("ProjectMember Schemas", () => {
  describe("ProjectMemberCreateSchema", () => {
    it("should validate valid project member creation data", () => {
      const validMember = {
        name: "Lead Engineer",
        user_id: 1,
        role_id: 2,
        expires_at: "2024-12-31T23:59:59Z",
      }

      const result = ProjectMemberCreateSchema.safeParse(validMember)
      expect(result.success).toBe(true)
    })

    it("should validate member without expiration", () => {
      const memberWithoutExpiration = {
        name: "Project Manager",
        user_id: 2,
        role_id: 1,
      }

      const result = ProjectMemberCreateSchema.safeParse(
        memberWithoutExpiration
      )
      expect(result.success).toBe(true)
    })

    it("should validate member without name (auto-generated)", () => {
      const memberWithoutName = {
        user_id: 3,
        role_id: 1,
      }

      const result = ProjectMemberCreateSchema.safeParse(memberWithoutName)
      expect(result.success).toBe(true)
    })

    it("should reject member with invalid user_id", () => {
      const invalidMember = {
        name: "Invalid Member",
        user_id: -1, // Invalid, must be positive integer
        role_id: 1,
      }

      const result = ProjectMemberCreateSchema.safeParse(invalidMember)
      expect(result.success).toBe(false)
    })

    it("should reject member with invalid role_id", () => {
      const invalidMember = {
        name: "Invalid Member",
        user_id: 1,
        role_id: 0, // Invalid, must be positive integer
      }

      const result = ProjectMemberCreateSchema.safeParse(invalidMember)
      expect(result.success).toBe(false)
    })

    it("should reject member with empty name", () => {
      const invalidMember = {
        name: "",
        user_id: 1,
        role_id: 1,
      }

      const result = ProjectMemberCreateSchema.safeParse(invalidMember)
      expect(result.success).toBe(false)
    })

    it("should trim member name", () => {
      const memberWithSpaces = {
        name: "  Trimmed Role Name  ",
        user_id: 1,
        role_id: 1,
      }

      const result = ProjectMemberCreateSchema.safeParse(memberWithSpaces)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.name).toBe("Trimmed Role Name")
      }
    })
  })

  describe("ProjectMemberUpdateSchema", () => {
    it("should validate partial member updates", () => {
      const partialUpdate = {
        role_id: 3,
        is_active: false,
      }

      const result = ProjectMemberUpdateSchema.safeParse(partialUpdate)
      expect(result.success).toBe(true)
    })

    it("should validate expiration date update", () => {
      const expirationUpdate = {
        expires_at: "2025-01-01T00:00:00Z",
      }

      const result = ProjectMemberUpdateSchema.safeParse(expirationUpdate)
      expect(result.success).toBe(true)
    })

    it("should allow empty update object", () => {
      const result = ProjectMemberUpdateSchema.safeParse({})
      expect(result.success).toBe(true)
    })

    it("should validate status change", () => {
      const statusUpdate = {
        is_active: false,
      }

      const result = ProjectMemberUpdateSchema.safeParse(statusUpdate)
      expect(result.success).toBe(true)
    })

    it("should reject invalid role_id in update", () => {
      const invalidUpdate = {
        role_id: -1,
      }

      const result = ProjectMemberUpdateSchema.safeParse(invalidUpdate)
      expect(result.success).toBe(false)
    })
  })

  describe("ProjectMemberReadSchema", () => {
    it("should validate complete project member read data", () => {
      const readData = {
        id: 1,
        name: "Lead Engineer",
        user_id: 1,
        project_id: 1,
        role_id: 2,
        is_active: true,
        expires_at: "2024-12-31T23:59:59Z",
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
        user: {
          id: 1,
          name: "John Doe",
          email: "<EMAIL>",
          is_superuser: false,
          is_active: true,
          role: null,
          last_login: null,
          created_at: "2024-01-01T00:00:00Z",
          updated_at: "2024-01-01T00:00:00Z",
        },
        role: {
          name: "Lead Engineer",
          description: "Lead engineer role",
          is_system_role: false,
          permissions: '["read", "write"]',
        },
      }

      const result = ProjectMemberReadSchema.safeParse(readData)
      expect(result.success).toBe(true)
    })

    it("should validate member without expiration date", () => {
      const memberWithoutExpiration = {
        id: 2,
        name: "Project Manager",
        user_id: 2,
        project_id: 1,
        role_id: 1,
        is_active: true,
        expires_at: null,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
        user: {
          id: 2,
          name: "Jane Smith",
          email: "<EMAIL>",
          is_superuser: false,
          is_active: true,
          role: null,
          last_login: null,
          created_at: "2024-01-01T00:00:00Z",
          updated_at: "2024-01-01T00:00:00Z",
        },
        role: {
          name: "Project Manager",
          description: "Project manager role",
          is_system_role: false,
          permissions: '["read", "write", "admin"]',
        },
      }

      const result = ProjectMemberReadSchema.safeParse(memberWithoutExpiration)
      expect(result.success).toBe(true)
    })

    it("should validate inactive member", () => {
      const inactiveMember = {
        id: 3,
        name: "Former Team Member",
        user_id: 3,
        project_id: 1,
        role_id: 3,
        is_active: false,
        expires_at: null,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-02-01T00:00:00Z",
        user: {
          id: 3,
          name: "Former User",
          email: "<EMAIL>",
          is_superuser: false,
          is_active: false,
          role: null,
          last_login: null,
          created_at: "2024-01-01T00:00:00Z",
          updated_at: "2024-02-01T00:00:00Z",
        },
        role: {
          name: "Designer",
          description: "Designer role",
          is_system_role: false,
          permissions: '["read"]',
        },
      }

      const result = ProjectMemberReadSchema.safeParse(inactiveMember)
      expect(result.success).toBe(true)
    })

    it("should require all mandatory fields", () => {
      const incompleteMember = {
        id: 1,
        name: "Incomplete Member",
        // Missing required fields like user_id, project_id, etc.
      }

      const result = ProjectMemberReadSchema.safeParse(incompleteMember)
      expect(result.success).toBe(false)
    })
  })
})
