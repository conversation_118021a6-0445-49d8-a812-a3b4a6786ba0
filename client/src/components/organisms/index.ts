/**
 * Organism Components - Complex Interface Sections
 * 
 * Atomic design organisms combining atoms and molecules to create
 * complete, complex interface sections with business logic.
 * 
 * Architecture:
 * - Composed of atoms and molecules
 * - Complex business logic integration
 * - WCAG 2.1 AA accessibility compliance
 * - TypeScript strict mode compliance
 * - Consistent design system integration
 * - Performance optimized
 * - Engineering-grade quality
 */

// Core Organisms
export {
  AuthForm,
  type AuthFormProps,
  type AuthFormData,
  type AuthFormSize,
  type AuthFormLayout,
  type AuthFormType,
  authFormVariants
} from "./AuthForm"