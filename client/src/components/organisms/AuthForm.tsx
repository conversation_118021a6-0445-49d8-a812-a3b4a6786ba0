/**
 * AuthForm Organism - Complex Interface Component
 * 
 * Atomic design organism providing complete authentication interface
 * using InputField molecules and Button atoms for form composition.
 * 
 * Features:
 * - Atomic design composition (InputField + Button)
 * - WCAG 2.1 AA accessibility compliance
 * - TypeScript strict mode compliance
 * - Login and register form types
 * - Integrated form validation
 * - Loading states and error handling
 * - Keyboard navigation support
 * - Engineering-grade quality
 */

import React, { useState } from "react"
import type { VariantProps } from "class-variance-authority"
import { cva } from "class-variance-authority"

import { cn } from "@/lib/utils"
import { InputField } from "../molecules/InputField"
import { Button } from "../atoms/Button"

// AuthForm variants using CVA for consistent styling
const authFormVariants = cva("space-y-6", {
  variants: {
    size: {
      default: "max-w-sm mx-auto",
      wide: "max-w-md mx-auto",
      full: "w-full",
    },
    layout: {
      default: "space-y-6",
      compact: "space-y-4",
      spacious: "space-y-8",
    },
  },
  defaultVariants: {
    size: "default",
    layout: "default",
  },
})

export interface AuthFormData {
  email: string
  password: string
  confirmPassword?: string
  firstName?: string
  lastName?: string
}

export interface AuthFormProps
  extends Omit<React.FormHTMLAttributes<HTMLFormElement>, "onSubmit">,
    VariantProps<typeof authFormVariants> {
  /** Form type - login or register */
  formType: "login" | "register"
  /** Form submission handler */
  onSubmit: (data: AuthFormData) => void | Promise<void>
  /** Loading state */
  loading?: boolean
  /** Global form error message */
  error?: string
  /** Success message */
  success?: string
  /** Submit button text override */
  submitText?: string
  /** Show remember me checkbox (login only) */
  showRememberMe?: boolean
  /** Show forgot password link (login only) */
  showForgotPassword?: boolean
  /** Forgot password click handler */
  onForgotPassword?: () => void
  /** Test identifier for automated testing */
  "data-testid"?: string
}

export const AuthForm = React.forwardRef<HTMLFormElement, AuthFormProps>(
  (
    {
      formType,
      onSubmit,
      loading = false,
      error,
      success,
      submitText,
      showRememberMe = true,
      showForgotPassword = true,
      onForgotPassword,
      size,
      layout,
      className,
      "data-testid": testId,
      ...props
    },
    ref
  ) => {
    const [formData, setFormData] = useState<AuthFormData>({
      email: "",
      password: "",
      confirmPassword: "",
      firstName: "",
      lastName: "",
    })

    const [fieldErrors, setFieldErrors] = useState<Partial<AuthFormData>>({})
    const [touched, setTouched] = useState<Record<string, boolean>>({})

    const isRegister = formType === "register"
    const defaultSubmitText = isRegister ? "Create Account" : "Sign In"

    // Form validation
    const validateEmail = (email: string): string | undefined => {
      if (!email) return "Email is required"
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(email)) return "Please enter a valid email address"
      return undefined
    }

    const validatePassword = (password: string): string | undefined => {
      if (!password) return "Password is required"
      if (password.length < 8) return "Password must be at least 8 characters"
      if (isRegister && !/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)) {
        return "Password must contain uppercase, lowercase, and number"
      }
      return undefined
    }

    const validateConfirmPassword = (confirmPassword: string): string | undefined => {
      if (isRegister && !confirmPassword) return "Please confirm your password"
      if (isRegister && confirmPassword !== formData.password) {
        return "Passwords do not match"
      }
      return undefined
    }

    const validateName = (name: string, fieldName: string): string | undefined => {
      if (isRegister && !name) return `${fieldName} is required`
      return undefined
    }

    // Update field value and validate
    const updateField = (field: keyof AuthFormData, value: string) => {
      setFormData(prev => ({ ...prev, [field]: value }))
      
      // Validate on change if field was touched
      if (touched[field]) {
        let error: string | undefined
        switch (field) {
          case "email":
            error = validateEmail(value)
            break
          case "password":
            error = validatePassword(value)
            break
          case "confirmPassword":
            error = validateConfirmPassword(value)
            break
          case "firstName":
            error = validateName(value, "First name")
            break
          case "lastName":
            error = validateName(value, "Last name")
            break
        }
        setFieldErrors(prev => ({ ...prev, [field]: error }))
      }
    }

    // Mark field as touched
    const markTouched = (field: string) => {
      setTouched(prev => ({ ...prev, [field]: true }))
    }

    // Handle form submission
    const handleSubmit = async (e: React.FormEvent) => {
      e.preventDefault()

      // Mark all fields as touched
      const allFields = ["email", "password"]
      if (isRegister) {
        allFields.push("confirmPassword", "firstName", "lastName")
      }
      
      const newTouched: Record<string, boolean> = {}
      allFields.forEach(field => {
        newTouched[field] = true
      })
      setTouched(newTouched)

      // Validate all fields
      const newErrors: Partial<AuthFormData> = {}
      newErrors.email = validateEmail(formData.email)
      newErrors.password = validatePassword(formData.password)
      
      if (isRegister) {
        newErrors.confirmPassword = validateConfirmPassword(formData.confirmPassword || "")
        newErrors.firstName = validateName(formData.firstName || "", "First name")
        newErrors.lastName = validateName(formData.lastName || "", "Last name")
      }

      setFieldErrors(newErrors)

      // Check if form has errors
      const hasErrors = Object.values(newErrors).some(error => error !== undefined)
      if (hasErrors) return

      // Submit form
      try {
        await onSubmit(formData)
      } catch (err) {
        // Error handling is managed by parent component
        console.error("Form submission error:", err)
      }
    }

    return (
      <form
        ref={ref}
        className={cn(authFormVariants({ size, layout }), className)}
        onSubmit={handleSubmit}
        data-testid={testId}
        {...props}
      >
        <div className="space-y-4">
          <div className="text-center space-y-2">
            <h1 className="text-2xl font-semibold tracking-tight">
              {isRegister ? "Create Account" : "Welcome Back"}
            </h1>
            <p className="text-sm text-muted-foreground">
              {isRegister
                ? "Enter your information to create your account"
                : "Enter your credentials to access your account"}
            </p>
          </div>

          {/* Global Messages */}
          {error && (
            <div 
              className="p-3 text-sm text-destructive bg-destructive/10 border border-destructive/20 rounded-md"
              role="alert"
              aria-live="polite"
              data-testid={testId ? `${testId}-error` : undefined}
            >
              {error}
            </div>
          )}

          {success && (
            <div 
              className="p-3 text-sm text-green-700 bg-green-50 border border-green-200 rounded-md"
              role="status"
              aria-live="polite"
              data-testid={testId ? `${testId}-success` : undefined}
            >
              {success}
            </div>
          )}

          {/* Form Fields */}
          <div className="space-y-4">
            {isRegister && (
              <div className="grid grid-cols-2 gap-4">
                <InputField
                  label="First Name"
                  type="text"
                  value={formData.firstName || ""}
                  onChange={(e) => updateField("firstName", e.target.value)}
                  onBlur={() => markTouched("firstName")}
                  error={fieldErrors.firstName}
                  touched={touched.firstName}
                  required
                  disabled={loading}
                  data-testid={testId ? `${testId}-firstName` : undefined}
                />
                <InputField
                  label="Last Name"
                  type="text"
                  value={formData.lastName || ""}
                  onChange={(e) => updateField("lastName", e.target.value)}
                  onBlur={() => markTouched("lastName")}
                  error={fieldErrors.lastName}
                  touched={touched.lastName}
                  required
                  disabled={loading}
                  data-testid={testId ? `${testId}-lastName` : undefined}
                />
              </div>
            )}

            <InputField
              label="Email"
              type="email"
              value={formData.email}
              onChange={(e) => updateField("email", e.target.value)}
              onBlur={() => markTouched("email")}
              error={fieldErrors.email}
              touched={touched.email}
              required
              disabled={loading}
              autoComplete="email"
              data-testid={testId ? `${testId}-email` : undefined}
            />

            <InputField
              label="Password"
              type="password"
              value={formData.password}
              onChange={(e) => updateField("password", e.target.value)}
              onBlur={() => markTouched("password")}
              error={fieldErrors.password}
              touched={touched.password}
              required
              disabled={loading}
              autoComplete={isRegister ? "new-password" : "current-password"}
              helpText={
                isRegister
                  ? "Must be at least 8 characters with uppercase, lowercase, and number"
                  : undefined
              }
              data-testid={testId ? `${testId}-password` : undefined}
            />

            {isRegister && (
              <InputField
                label="Confirm Password"
                type="password"
                value={formData.confirmPassword || ""}
                onChange={(e) => updateField("confirmPassword", e.target.value)}
                onBlur={() => markTouched("confirmPassword")}
                error={fieldErrors.confirmPassword}
                touched={touched.confirmPassword}
                required
                disabled={loading}
                autoComplete="new-password"
                data-testid={testId ? `${testId}-confirmPassword` : undefined}
              />
            )}
          </div>

          {/* Additional Options (Login only) */}
          {!isRegister && (showRememberMe || showForgotPassword) && (
            <div className="flex items-center justify-between text-sm">
              {showRememberMe && (
                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="checkbox"
                    className="rounded border border-input"
                    disabled={loading}
                  />
                  <span className="text-muted-foreground">Remember me</span>
                </label>
              )}

              {showForgotPassword && (
                <button
                  type="button"
                  onClick={onForgotPassword}
                  disabled={loading}
                  className="text-primary hover:underline focus:underline focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed"
                  data-testid={testId ? `${testId}-forgotPassword` : undefined}
                >
                  Forgot password?
                </button>
              )}
            </div>
          )}

          {/* Submit Button */}
          <Button
            type="submit"
            size="lg"
            loading={loading}
            loadingText={isRegister ? "Creating Account..." : "Signing In..."}
            fullWidth
            data-testid={testId ? `${testId}-submit` : undefined}
          >
            {submitText || defaultSubmitText}
          </Button>
        </div>
      </form>
    )
  }
)

AuthForm.displayName = "AuthForm"

// Export types for external use
export type AuthFormSize = NonNullable<AuthFormProps["size"]>
export type AuthFormLayout = NonNullable<AuthFormProps["layout"]>
export type AuthFormType = NonNullable<AuthFormProps["formType"]>

// Export variants for external use
export { authFormVariants }