/**
 * Unified Form Field System - Comprehensive Test Suite
 * 
 * Tests following TDD methodology with complete coverage:
 * - Form field rendering and behavior
 * - Input types and validation states
 * - Interactive features and controls
 * - Accessibility compliance (WCAG 2.1 AA)
 * - Backward compatibility
 * - Performance and edge cases
 */

import React from "react"
import { render, screen, fireEvent } from "@testing-library/react"
import userEvent from "@testing-library/user-event"
import { vi } from "vitest"

import {
  UnifiedFormField,
  FormInput,
} from "../unified-form"

describe("UnifiedFormField", () => {
  // Basic rendering tests
  describe("Basic Rendering", () => {
    it("renders with default props", () => {
      render(<UnifiedFormField label="Test Field" data-testid="form-field" />)
      const input = screen.getByRole("textbox", { name: "Test Field" })
      expect(input).toBeInTheDocument()
      expect(input).toHaveAttribute("type", "text")
    })

    it("properly associates label with input", () => {
      render(<UnifiedFormField label="Email Address" data-testid="form-field" />)
      const input = screen.getByRole("textbox")
      const label = screen.getByText("Email Address")
      
      expect(label.tagName).toBe("LABEL")
      expect(label).toHaveAttribute("for", input.id)
      expect(input.id).toBeTruthy()
    })

    it("generates unique IDs for multiple fields", () => {
      render(
        <div>
          <UnifiedFormField label="Field 1" data-testid="field1" />
          <UnifiedFormField label="Field 2" data-testid="field2" />
        </div>
      )
      
      const input1 = screen.getByRole("textbox", { name: "Field 1" })
      const input2 = screen.getByRole("textbox", { name: "Field 2" })
      
      expect(input1.id).not.toBe(input2.id)
      expect(input1.id).toBeTruthy()
      expect(input2.id).toBeTruthy()
    })

    it("uses provided id when specified", () => {
      render(<UnifiedFormField label="Test" id="custom-id" data-testid="form-field" />)
      const input = screen.getByRole("textbox")
      expect(input.id).toBe("custom-id")
    })
  })

  // Input type tests
  describe("Input Types", () => {
    it("renders text input by default", () => {
      render(<UnifiedFormField label="Name" data-testid="form-field" />)
      const input = screen.getByRole("textbox")
      expect(input).toHaveAttribute("type", "text")
    })

    it("renders email input correctly", () => {
      render(<UnifiedFormField label="Email" type="email" data-testid="form-field" />)
      const input = screen.getByRole("textbox")
      expect(input).toHaveAttribute("type", "email")
    })

    it("renders password input correctly", () => {
      render(<UnifiedFormField label="Password" type="password" data-testid="form-field" />)
      const input = screen.getByLabelText("Password")
      expect(input).toHaveAttribute("type", "password")
    })

    it("renders number input correctly", () => {
      render(<UnifiedFormField label="Age" type="number" data-testid="form-field" />)
      const input = screen.getByRole("spinbutton")
      expect(input).toHaveAttribute("type", "number")
    })

    it("renders textarea correctly", () => {
      render(<UnifiedFormField label="Comments" type="textarea" data-testid="form-field" />)
      const textarea = screen.getByRole("textbox", { name: "Comments" })
      expect(textarea.tagName).toBe("TEXTAREA")
    })

    it("renders select correctly", () => {
      const options = [
        { value: "option1", label: "Option 1" },
        { value: "option2", label: "Option 2" }
      ]
      render(<UnifiedFormField label="Choose" type="select" options={options} data-testid="form-field" />)
      const select = screen.getByRole("combobox")
      expect(select.tagName).toBe("SELECT")
      expect(screen.getByText("Option 1")).toBeInTheDocument()
      expect(screen.getByText("Option 2")).toBeInTheDocument()
    })
  })

  // Validation state tests
  describe("Validation States", () => {
    it("shows required indicator for required fields", () => {
      render(<UnifiedFormField label="Required Field" required data-testid="form-field" />)
      const input = screen.getByRole("textbox")
      expect(input).toHaveAttribute("aria-required", "true")
      expect(screen.getByText("*")).toBeInTheDocument()
    })

    it("shows error state when error is provided", () => {
      render(
        <UnifiedFormField 
          label="Email" 
          error="Invalid email address"
          touched
          data-testid="form-field" 
        />
      )
      
      const input = screen.getByRole("textbox")
      expect(input).toHaveAttribute("aria-invalid", "true")
      
      const errorMessage = screen.getByText("Invalid email address")
      expect(errorMessage).toBeInTheDocument()
      expect(errorMessage).toHaveClass("text-destructive")
    })

    it("shows warning state when warning is provided", () => {
      render(
        <UnifiedFormField 
          label="Password" 
          warning="Password strength: weak"
          data-testid="form-field" 
        />
      )
      
      const warning = screen.getByText("Password strength: weak")
      expect(warning).toBeInTheDocument()
      expect(warning).toHaveClass("text-amber-600")
    })

    it("shows success state when success is provided", () => {
      render(
        <UnifiedFormField 
          label="Email" 
          success="Email is available"
          data-testid="form-field" 
        />
      )
      
      const success = screen.getByText("Email is available")
      expect(success).toBeInTheDocument()
      expect(success).toHaveClass("text-green-600")
    })

    it("only shows error when touched", () => {
      const { rerender } = render(
        <UnifiedFormField 
          label="Email" 
          error="Invalid email"
          touched={false}
          data-testid="form-field" 
        />
      )
      
      expect(screen.queryByText("Invalid email")).not.toBeInTheDocument()
      
      rerender(
        <UnifiedFormField 
          label="Email" 
          error="Invalid email"
          touched={true}
          data-testid="form-field" 
        />
      )
      
      expect(screen.getByText("Invalid email")).toBeInTheDocument()
    })

    it("prioritizes error over warning and success", () => {
      render(
        <UnifiedFormField 
          label="Test" 
          error="Error message"
          warning="Warning message"
          success="Success message"
          touched
          data-testid="form-field" 
        />
      )
      
      expect(screen.getByText("Error message")).toBeInTheDocument()
      expect(screen.queryByText("Warning message")).not.toBeInTheDocument()
      expect(screen.queryByText("Success message")).not.toBeInTheDocument()
    })
  })

  // Help text and description tests
  describe("Help Text and Description", () => {
    it("shows help text when provided", () => {
      render(
        <UnifiedFormField 
          label="Username" 
          helpText="Choose a unique username"
          data-testid="form-field" 
        />
      )
      
      const helpText = screen.getByText("Choose a unique username")
      expect(helpText).toBeInTheDocument()
      
      const input = screen.getByRole("textbox")
      expect(input).toHaveAttribute("aria-describedby", expect.stringContaining(helpText.id))
    })

    it("shows description when provided", () => {
      render(
        <UnifiedFormField 
          label="Bio" 
          description="Tell us about yourself"
          data-testid="form-field" 
        />
      )
      
      expect(screen.getByText("Tell us about yourself")).toBeInTheDocument()
    })

    it("combines help text and validation messages in aria-describedby", () => {
      render(
        <UnifiedFormField 
          label="Email" 
          helpText="We'll never share your email"
          error="Invalid format"
          touched
          data-testid="form-field" 
        />
      )
      
      const input = screen.getByRole("textbox")
      const helpText = screen.getByText("We'll never share your email")
      const errorText = screen.getByText("Invalid format")
      
      const describedBy = input.getAttribute("aria-describedby")
      expect(describedBy).toContain(helpText.id)
      expect(describedBy).toContain(errorText.id)
    })
  })

  // Interactive features tests
  describe("Interactive Features", () => {
    it("shows password toggle for password fields when enabled", () => {
      render(
        <UnifiedFormField 
          label="Password" 
          type="password"
          showPasswordToggle
          data-testid="form-field" 
        />
      )
      
      const toggleButton = screen.getByRole("button", { name: /show password/i })
      expect(toggleButton).toBeInTheDocument()
    })

    it("toggles password visibility when toggle button is clicked", async () => {
      const user = userEvent.setup()
      
      render(
        <UnifiedFormField 
          label="Password" 
          type="password"
          showPasswordToggle
          data-testid="form-field" 
        />
      )
      
      const input = screen.getByLabelText("Password")
      const toggleButton = screen.getByRole("button", { name: /show password/i })
      
      expect(input).toHaveAttribute("type", "password")
      
      await user.click(toggleButton)
      expect(input).toHaveAttribute("type", "text")
      
      await user.click(toggleButton)
      expect(input).toHaveAttribute("type", "password")
    })

    it("shows clear button for clearable fields with value", () => {
      render(
        <UnifiedFormField 
          label="Search" 
          clearable
          value="test value"
          onChange={() => {}}
          data-testid="form-field" 
        />
      )
      
      const clearButton = screen.getByRole("button", { name: /clear input/i })
      expect(clearButton).toBeInTheDocument()
    })

    it("clears value when clear button is clicked", async () => {
      const user = userEvent.setup()
      const handleChange = vi.fn()
      
      render(
        <UnifiedFormField 
          label="Search" 
          clearable
          value="test value"
          onChange={handleChange}
          data-testid="form-field" 
        />
      )
      
      const clearButton = screen.getByRole("button", { name: /clear input/i })
      await user.click(clearButton)
      
      expect(handleChange).toHaveBeenCalledWith(
        expect.objectContaining({
          target: expect.objectContaining({ value: "" })
        })
      )
    })

    it("does not show clear button when no value", () => {
      render(
        <UnifiedFormField 
          label="Search" 
          clearable
          value=""
          onChange={() => {}}
          data-testid="form-field" 
        />
      )
      
      expect(screen.queryByRole("button", { name: /clear input/i })).not.toBeInTheDocument()
    })
  })

  // Size and variant tests
  describe("Size and Variant", () => {
    it("applies correct size classes", () => {
      const { rerender } = render(
        <UnifiedFormField label="Test" size="sm" data-testid="form-field" />
      )
      let input = screen.getByRole("textbox")
      expect(input).toHaveClass("h-8")

      rerender(<UnifiedFormField label="Test" size="lg" data-testid="form-field" />)
      input = screen.getByRole("textbox")
      expect(input).toHaveClass("h-12")
    })

    it("applies variant classes correctly", () => {
      render(<UnifiedFormField label="Test" variant="filled" data-testid="form-field" />)
      const input = screen.getByRole("textbox")
      expect(input).toHaveClass("bg-muted")
    })
  })

  // Event handling tests
  describe("Event Handling", () => {
    it("calls onChange when input value changes", async () => {
      const user = userEvent.setup()
      const handleChange = vi.fn()
      
      render(
        <UnifiedFormField 
          label="Name" 
          onChange={handleChange}
          data-testid="form-field" 
        />
      )
      
      const input = screen.getByRole("textbox")
      await user.type(input, "test")
      
      expect(handleChange).toHaveBeenCalled()
    })

    it("calls onFocus when input gains focus", async () => {
      const user = userEvent.setup()
      const handleFocus = vi.fn()
      
      render(
        <UnifiedFormField 
          label="Name" 
          onFocus={handleFocus}
          data-testid="form-field" 
        />
      )
      
      const input = screen.getByRole("textbox")
      await user.click(input)
      
      expect(handleFocus).toHaveBeenCalled()
    })

    it("calls onBlur when input loses focus", async () => {
      const user = userEvent.setup()
      const handleBlur = vi.fn()
      
      render(
        <UnifiedFormField 
          label="Name" 
          onBlur={handleBlur}
          data-testid="form-field" 
        />
      )
      
      const input = screen.getByRole("textbox")
      await user.click(input)
      await user.tab()
      
      expect(handleBlur).toHaveBeenCalled()
    })
  })

  // Accessibility tests
  describe("Accessibility", () => {
    it("has proper label association", () => {
      render(<UnifiedFormField label="Full Name" data-testid="form-field" />)
      const input = screen.getByRole("textbox", { name: "Full Name" })
      expect(input).toBeInTheDocument()
    })

    it("supports keyboard navigation", async () => {
      const user = userEvent.setup()
      
      render(
        <div>
          <UnifiedFormField label="Field 1" data-testid="field1" />
          <UnifiedFormField label="Field 2" data-testid="field2" />
        </div>
      )
      
      await user.tab()
      expect(screen.getByRole("textbox", { name: "Field 1" })).toHaveFocus()
      
      await user.tab()
      expect(screen.getByRole("textbox", { name: "Field 2" })).toHaveFocus()
    })

    it("properly describes validation states to screen readers", () => {
      render(
        <UnifiedFormField 
          label="Email" 
          error="Invalid email format"
          helpText="Enter a valid email address"
          touched
          data-testid="form-field" 
        />
      )
      
      const input = screen.getByRole("textbox")
      expect(input).toHaveAttribute("aria-invalid", "true")
      
      const describedBy = input.getAttribute("aria-describedby")
      expect(describedBy).toBeTruthy()
      
      const helpText = screen.getByText("Enter a valid email address")
      const errorText = screen.getByText("Invalid email format")
      expect(describedBy).toContain(helpText.id)
      expect(describedBy).toContain(errorText.id)
    })
  })
})

// Backward compatibility component tests
describe("FormInput (Backward Compatibility)", () => {
  it("renders identically to UnifiedFormField", () => {
    const { container: unifiedContainer } = render(
      <UnifiedFormField label="Test" data-testid="unified" />
    )
    const { container: formInputContainer } = render(
      <FormInput label="Test" data-testid="forminput" />
    )
    
    const unifiedInput = screen.getByTestId("unified").querySelector("input")
    const formInput = screen.getByTestId("forminput").querySelector("input")
    
    expect(unifiedInput?.className).toBe(formInput?.className)
  })

  it("supports all legacy props", () => {
    render(
      <FormInput 
        label="Legacy Field"
        error="Legacy error"
        warning="Legacy warning"
        success="Legacy success"
        helpText="Legacy help"
        required
        touched
        size="md"
        variant="default"
        data-testid="legacy-input"
      />
    )
    
    expect(screen.getByRole("textbox")).toBeInTheDocument()
    expect(screen.getByText("Legacy error")).toBeInTheDocument()
    expect(screen.getByText("*")).toBeInTheDocument()
  })
})

// Form field validation behavior tests
describe("Form Field Validation", () => {
  it("shows error state correctly based on error and touched props", () => {
    const { rerender } = render(
      <UnifiedFormField 
        label="Test"
        error="Error message"
        touched={false}
        data-testid="form-field" 
      />
    )
    
    // Should not show error when not touched
    expect(screen.queryByText("Error message")).not.toBeInTheDocument()
    
    rerender(
      <UnifiedFormField 
        label="Test"
        error="Error message"
        touched={true}
        data-testid="form-field" 
      />
    )
    
    // Should show error when touched
    expect(screen.getByText("Error message")).toBeInTheDocument()
  })

  it("prioritizes validation states correctly", () => {
    render(
      <UnifiedFormField 
        label="Test"
        error="Error message"
        warning="Warning message"
        success="Success message"
        touched={true}
        data-testid="form-field" 
      />
    )
    
    // Should show error (highest priority)
    expect(screen.getByText("Error message")).toBeInTheDocument()
    expect(screen.queryByText("Warning message")).not.toBeInTheDocument()
    expect(screen.queryByText("Success message")).not.toBeInTheDocument()
  })
})

// Performance tests
describe("Performance", () => {
  it("renders multiple form fields quickly", () => {
    const startTime = performance.now()
    
    const fields = Array.from({ length: 50 }, (_, i) => (
      <UnifiedFormField
        key={i}
        label={`Field ${i}`}
        data-testid={`field-${i}`}
      />
    ))
    
    render(<div>{fields}</div>)
    
    const endTime = performance.now()
    const renderTime = endTime - startTime
    
    // Should render 50 form fields in less than 100ms (more realistic)
    expect(renderTime).toBeLessThan(100)
    
    // Verify all fields are rendered
    expect(screen.getAllByRole("textbox")).toHaveLength(50)
  })
})

// Edge cases
describe("Edge Cases", () => {
  it("handles undefined label gracefully", () => {
    render(
      <UnifiedFormField 
        // @ts-expect-error Testing undefined label
        label={undefined}
        data-testid="form-field" 
      />
    )
    
    const input = screen.getByRole("textbox")
    expect(input).toBeInTheDocument()
  })

  it("handles empty validation messages", () => {
    render(
      <UnifiedFormField 
        label="Test"
        error=""
        warning=""
        success=""
        helpText=""
        data-testid="form-field" 
      />
    )
    
    const input = screen.getByRole("textbox")
    expect(input).toBeInTheDocument()
  })

  it("handles rapid value changes", async () => {
    const user = userEvent.setup()
    const handleChange = vi.fn()
    
    render(
      <UnifiedFormField 
        label="Test"
        onChange={handleChange}
        data-testid="form-field" 
      />
    )
    
    const input = screen.getByRole("textbox")
    
    // Rapid typing
    await user.type(input, "abc", { delay: 1 })
    
    expect(handleChange).toHaveBeenCalled()
    expect(input).toHaveValue("abc")
  })

  it("handles special characters in labels and values", () => {
    render(
      <UnifiedFormField 
        label="Test & Special <Characters>"
        value="Value with & special <characters>"
        onChange={() => {}}
        data-testid="form-field" 
      />
    )
    
    expect(screen.getByDisplayValue("Value with & special <characters>")).toBeInTheDocument()
    expect(screen.getByText("Test & Special <Characters>")).toBeInTheDocument()
  })
})