/**
 * Unified Button System - Comprehensive Test Suite
 * 
 * Tests following TDD methodology with complete coverage:
 * - Button rendering and behavior
 * - Action types and configurations
 * - Loading states and interactions
 * - Accessibility compliance (WCAG 2.1 AA)
 * - Backward compatibility
 * - Performance and edge cases
 */

import React from "react"
import { render, screen, fireEvent, waitFor } from "@testing-library/react"
import userEvent from "@testing-library/user-event"
import { vi } from "vitest"

import {
  UnifiedButton,
  ActionButton,
  CreateButton,
  EditButton,
  DeleteButton,
  SaveButton,
  CancelButton,
  MoreButton,
  BackButton,
  NextButton,
  getActionConfig,
  isValidActionType,
  getActionsByCategory,
} from "../unified-button"

describe("UnifiedButton", () => {
  // Basic rendering tests
  describe("Basic Rendering", () => {
    it("renders with default props", () => {
      render(<UnifiedButton action="edit" data-testid="button" />)
      const button = screen.getByTestId("button")
      expect(button).toBeInTheDocument()
      expect(button).toHaveAttribute("type", "button")
    })

    it("renders with action-specific icon and label", () => {
      render(<UnifiedButton action="edit" display="both" data-testid="button" />)
      const button = screen.getByTestId("button")
      expect(button).toBeInTheDocument()
      expect(screen.getByText("Edit")).toBeInTheDocument()
      
      // Icon should be present (Lucide Edit icon)
      const icon = button.querySelector('svg')
      expect(icon).toBeInTheDocument()
    })

    it("applies correct variant based on action config", () => {
      render(<UnifiedButton action="delete" data-testid="button" />)
      const button = screen.getByTestId("button")
      // Delete action should use destructive variant by default
      expect(button).toHaveClass("bg-destructive")
    })

    it("allows variant override", () => {
      render(<UnifiedButton action="delete" variant="outline" data-testid="button" />)
      const button = screen.getByTestId("button")
      // Should use outline variant instead of default destructive
      expect(button).toHaveClass("border-input")
    })
  })

  // Display mode tests
  describe("Display Modes", () => {
    it("shows only icon when display is 'icon'", () => {
      render(
        <UnifiedButton 
          action="edit" 
          display="icon" 
          data-testid="button"
        />
      )
      const button = screen.getByTestId("button")
      expect(button).toHaveAttribute("aria-label", "Edit")
      expect(screen.queryByText("Edit")).not.toBeInTheDocument()
      expect(button.querySelector('svg')).toBeInTheDocument()
    })

    it("shows only text when display is 'text'", () => {
      render(
        <UnifiedButton 
          action="edit" 
          display="text" 
          data-testid="button"
        />
      )
      const button = screen.getByTestId("button")
      expect(screen.getByText("Edit")).toBeInTheDocument()
      expect(button.querySelector('svg')).not.toBeInTheDocument()
    })

    it("shows both icon and text when display is 'both'", () => {
      render(
        <UnifiedButton 
          action="edit" 
          display="both" 
          data-testid="button"
        />
      )
      const button = screen.getByTestId("button")
      expect(screen.getByText("Edit")).toBeInTheDocument()
      expect(button.querySelector('svg')).toBeInTheDocument()
    })

    it("shows icon first when display is 'icon-text'", () => {
      render(
        <UnifiedButton 
          action="edit" 
          display="icon-text" 
          data-testid="button"
        />
      )
      const button = screen.getByTestId("button")
      expect(screen.getByText("Edit")).toBeInTheDocument()
      expect(button.querySelector('svg')).toBeInTheDocument()
    })

    it("shows text first when display is 'text-icon'", () => {
      render(
        <UnifiedButton 
          action="edit" 
          display="text-icon" 
          iconPosition="right"
          data-testid="button"
        />
      )
      const button = screen.getByTestId("button")
      expect(screen.getByText("Edit")).toBeInTheDocument()
      expect(button.querySelector('svg')).toBeInTheDocument()
    })
  })

  // Size tests
  describe("Sizes", () => {
    it("applies correct size classes", () => {
      const { rerender } = render(
        <UnifiedButton action="edit" size="sm" data-testid="button" />
      )
      let button = screen.getByTestId("button")
      expect(button).toHaveClass("h-8")

      rerender(<UnifiedButton action="edit" size="lg" data-testid="button" />)
      button = screen.getByTestId("button")
      expect(button).toHaveClass("h-12")
    })

    it("applies icon-only sizing correctly", () => {
      render(
        <UnifiedButton 
          action="edit" 
          display="icon" 
          size="md" 
          data-testid="button"
        />
      )
      const button = screen.getByTestId("button")
      expect(button).toHaveClass("h-10", "w-10")
    })
  })

  // Loading state tests
  describe("Loading States", () => {
    it("shows loading spinner when loading", () => {
      render(
        <UnifiedButton 
          action="save" 
          loading={true} 
          display="both"
          data-testid="button"
        />
      )
      const button = screen.getByTestId("button")
      expect(button).toBeDisabled()
      
      // Loading spinner should be present
      const spinner = button.querySelector('[data-testid="button"] svg.animate-spin')
      expect(spinner).toBeInTheDocument()
    })

    it("shows custom loading text when provided", () => {
      render(
        <UnifiedButton 
          action="save" 
          loading={true}
          loadingText="Saving..."
          display="both"
          data-testid="button"
        />
      )
      expect(screen.getByText("Saving...")).toBeInTheDocument()
    })

    it("prevents click events when loading", () => {
      const handleClick = vi.fn()
      render(
        <UnifiedButton 
          action="save" 
          loading={true}
          onClick={handleClick}
          data-testid="button"
        />
      )
      const button = screen.getByTestId("button")
      fireEvent.click(button)
      expect(handleClick).not.toHaveBeenCalled()
    })
  })

  // Interaction tests
  describe("User Interactions", () => {
    it("handles click events", async () => {
      const user = userEvent.setup()
      const handleClick = vi.fn()
      
      render(
        <UnifiedButton 
          action="save" 
          onClick={handleClick}
          data-testid="button"
        />
      )
      
      const button = screen.getByTestId("button")
      await user.click(button)
      expect(handleClick).toHaveBeenCalledTimes(1)
    })

    it("handles keyboard interactions", async () => {
      const user = userEvent.setup()
      const handleClick = vi.fn()
      
      render(
        <UnifiedButton 
          action="save" 
          onClick={handleClick}
          data-testid="button"
        />
      )
      
      const button = screen.getByTestId("button")
      button.focus()
      await user.keyboard(" ") // Space key
      expect(handleClick).toHaveBeenCalledTimes(1)
      
      await user.keyboard("{Enter}")
      expect(handleClick).toHaveBeenCalledTimes(2)
    })

    it("prevents interaction when disabled", async () => {
      const user = userEvent.setup()
      const handleClick = vi.fn()
      
      render(
        <UnifiedButton 
          action="save" 
          disabled={true}
          onClick={handleClick}
          data-testid="button"
        />
      )
      
      const button = screen.getByTestId("button")
      await user.click(button)
      expect(handleClick).not.toHaveBeenCalled()
      expect(button).toBeDisabled()
    })
  })

  // Full width tests
  describe("Full Width", () => {
    it("applies full width classes when fullWidth is true", () => {
      render(
        <UnifiedButton 
          action="save" 
          fullWidth={true}
          data-testid="button"
        />
      )
      const button = screen.getByTestId("button")
      expect(button).toHaveClass("w-full")
    })
  })

  // Custom content tests
  describe("Custom Content", () => {
    it("uses custom label when provided", () => {
      render(
        <UnifiedButton 
          action="save" 
          customLabel="Custom Save"
          display="text"
          data-testid="button"
        />
      )
      expect(screen.getByText("Custom Save")).toBeInTheDocument()
    })

    it("uses children as label when provided", () => {
      render(
        <UnifiedButton 
          action="save" 
          display="text"
          data-testid="button"
        >
          Children Content
        </UnifiedButton>
      )
      expect(screen.getByText("Children Content")).toBeInTheDocument()
    })

    it("renders custom icon when provided", () => {
      const CustomIcon = ({ className }: { className?: string }) => (
        <div className={className} data-testid="custom-icon">★</div>
      )
      
      render(
        <UnifiedButton 
          action="save" 
          customIcon={CustomIcon}
          data-testid="button"
        />
      )
      
      expect(screen.getByTestId("custom-icon")).toBeInTheDocument()
    })
  })

  // Action type tests
  describe("Action Types", () => {
    it("handles all CRUD actions", () => {
      const crudActions = ["create", "read", "update", "delete"] as const
      crudActions.forEach(action => {
        const { unmount } = render(<UnifiedButton action={action} />)
        expect(screen.getByRole("button")).toBeInTheDocument()
        unmount()
      })
    })

    it("handles file operation actions", () => {
      const fileActions = ["save", "download", "upload", "copy"] as const
      fileActions.forEach(action => {
        const { unmount } = render(<UnifiedButton action={action} />)
        expect(screen.getByRole("button")).toBeInTheDocument()
        unmount()
      })
    })

    it("handles state change actions", () => {
      const stateActions = ["activate", "pause", "complete", "cancel"] as const
      stateActions.forEach(action => {
        const { unmount } = render(<UnifiedButton action={action} />)
        expect(screen.getByRole("button")).toBeInTheDocument()
        unmount()
      })
    })

    it("handles navigation actions", () => {
      const navActions = ["back", "forward", "next", "home"] as const
      navActions.forEach(action => {
        const { unmount } = render(<UnifiedButton action={action} />)
        expect(screen.getByRole("button")).toBeInTheDocument()
        unmount()
      })
    })

    it("warns for invalid action types", () => {
      const consoleSpy = vi.spyOn(console, "warn").mockImplementation(() => {})
      
      render(
        <UnifiedButton 
          // @ts-expect-error Testing invalid action
          action="invalid-action"
          data-testid="button"
        />
      )
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Invalid action type "invalid-action"')
      )
      
      consoleSpy.mockRestore()
    })
  })

  // Accessibility tests
  describe("Accessibility", () => {
    it("has proper focus management", async () => {
      const user = userEvent.setup()
      
      render(
        <UnifiedButton 
          action="save" 
          data-testid="button"
        />
      )
      
      const button = screen.getByTestId("button")
      await user.tab()
      expect(button).toHaveFocus()
    })

    it("has correct aria-label for icon-only buttons", () => {
      render(
        <UnifiedButton 
          action="edit" 
          display="icon"
          data-testid="button"
        />
      )
      const button = screen.getByTestId("button")
      expect(button).toHaveAttribute("aria-label", "Edit")
    })

    it("icons are hidden from screen readers", () => {
      render(
        <UnifiedButton 
          action="edit" 
          display="both"
          data-testid="button"
        />
      )
      const button = screen.getByTestId("button")
      const icon = button.querySelector('svg')
      expect(icon).toHaveAttribute("aria-hidden", "true")
    })

    it("has proper disabled state accessibility", () => {
      render(
        <UnifiedButton 
          action="save" 
          disabled={true}
          data-testid="button"
        />
      )
      const button = screen.getByTestId("button")
      expect(button).toBeDisabled()
      expect(button).toHaveAttribute("aria-disabled", "true")
    })
  })
})

// Backward compatibility component tests
describe("ActionButton (Backward Compatibility)", () => {
  it("renders identically to UnifiedButton", () => {
    const { container: unifiedContainer } = render(
      <UnifiedButton action="edit" data-testid="unified" />
    )
    const { container: actionContainer } = render(
      <ActionButton action="edit" data-testid="action" />
    )
    
    // Both should have the same structure and classes
    const unifiedButton = screen.getByTestId("unified")
    const actionButton = screen.getByTestId("action")
    
    expect(unifiedButton.className).toBe(actionButton.className)
  })
})

// Convenience components tests
describe("Convenience Components", () => {
  it("CreateButton renders with correct default props", () => {
    render(<CreateButton data-testid="create-button" />)
    const button = screen.getByTestId("create-button")
    expect(button).toBeInTheDocument()
    expect(screen.getByText("Create")).toBeInTheDocument()
  })

  it("EditButton renders with correct default props", () => {
    render(<EditButton data-testid="edit-button" />)
    const button = screen.getByTestId("edit-button")
    expect(button).toBeInTheDocument()
    expect(button).toHaveClass("border-input") // outline variant
  })

  it("DeleteButton renders with correct default props", () => {
    render(<DeleteButton data-testid="delete-button" />)
    const button = screen.getByTestId("delete-button")
    expect(button).toBeInTheDocument()
    expect(button).toHaveClass("bg-destructive") // destructive variant
  })

  it("SaveButton renders with correct default props", () => {
    render(<SaveButton data-testid="save-button" />)
    const button = screen.getByTestId("save-button")
    expect(button).toBeInTheDocument()
    expect(screen.getByText("Save")).toBeInTheDocument()
  })

  it("CancelButton renders with correct default props", () => {
    render(<CancelButton data-testid="cancel-button" />)
    const button = screen.getByTestId("cancel-button")
    expect(button).toBeInTheDocument()
    expect(screen.getByText("Cancel")).toBeInTheDocument()
  })

  it("MoreButton renders with correct default props", () => {
    render(<MoreButton data-testid="more-button" />)
    const button = screen.getByTestId("more-button")
    expect(button).toBeInTheDocument()
    expect(button).toHaveAttribute("aria-label", "More")
  })

  it("BackButton renders with correct default props", () => {
    render(<BackButton data-testid="back-button" />)
    const button = screen.getByTestId("back-button")
    expect(button).toBeInTheDocument()
    expect(screen.getByText("Back")).toBeInTheDocument()
  })

  it("NextButton renders with correct default props", () => {
    render(<NextButton data-testid="next-button" />)
    const button = screen.getByTestId("next-button")
    expect(button).toBeInTheDocument()
    expect(screen.getByText("Next")).toBeInTheDocument()
  })
})

// Utility functions tests
describe("Utility Functions", () => {
  describe("getActionConfig", () => {
    it("returns correct config for valid actions", () => {
      const editConfig = getActionConfig("edit")
      expect(editConfig).toBeDefined()
      expect(editConfig.label).toBe("Edit")
      expect(editConfig.variant).toBe("outline")
    })

    it("returns config for all defined actions", () => {
      const actions = ["create", "delete", "save", "cancel", "more"] as const
      actions.forEach(action => {
        const config = getActionConfig(action)
        expect(config).toBeDefined()
        expect(config.label).toBeDefined()
        expect(config.icon).toBeDefined()
      })
    })
  })

  describe("isValidActionType", () => {
    it("validates correct action types", () => {
      expect(isValidActionType("edit")).toBe(true)
      expect(isValidActionType("delete")).toBe(true)
      expect(isValidActionType("save")).toBe(true)
    })

    it("rejects invalid action types", () => {
      expect(isValidActionType("invalid")).toBe(false)
      expect(isValidActionType("")).toBe(false)
    })
  })

  describe("getActionsByCategory", () => {
    it("returns categorized actions", () => {
      const categories = getActionsByCategory()
      expect(categories).toBeDefined()
      expect(categories.crud).toContain("create")
      expect(categories.crud).toContain("read")
      expect(categories.file).toContain("save")
      expect(categories.navigation).toContain("back")
    })

    it("contains all expected categories", () => {
      const categories = getActionsByCategory()
      const expectedCategories = [
        "crud", "file", "state", "user", "navigation", 
        "interaction", "notification", "favorites", "security",
        "settings", "documentation", "scheduling", "visibility", "performance"
      ]
      
      expectedCategories.forEach(category => {
        expect(categories).toHaveProperty(category)
        expect(Array.isArray(categories[category as keyof typeof categories])).toBe(true)
      })
    })
  })
})

// Performance tests
describe("Performance", () => {
  it("renders multiple buttons quickly", () => {
    const startTime = performance.now()
    
    const buttons = Array.from({ length: 50 }, (_, i) => (
      <UnifiedButton
        key={i}
        action="edit"
        data-testid={`button-${i}`}
      />
    ))
    
    render(<div>{buttons}</div>)
    
    const endTime = performance.now()
    const renderTime = endTime - startTime
    
    // Should render 50 buttons in less than 100ms (more realistic)
    expect(renderTime).toBeLessThan(100)
    
    // Verify all buttons are rendered
    expect(screen.getAllByRole("button")).toHaveLength(50)
  })
})

// Edge cases
describe("Edge Cases", () => {
  it("handles missing onClick gracefully", () => {
    render(<UnifiedButton action="save" data-testid="button" />)
    const button = screen.getByTestId("button")
    
    // Should not throw error when clicked without onClick handler
    expect(() => fireEvent.click(button)).not.toThrow()
  })

  it("handles empty custom labels", () => {
    render(
      <UnifiedButton 
        action="save" 
        customLabel=""
        display="text"
        data-testid="button"
      />
    )
    
    // Should fallback to default label or action name
    const button = screen.getByTestId("button")
    expect(button).toBeInTheDocument()
  })

  it("handles rapid state changes", async () => {
    const { rerender } = render(
      <UnifiedButton action="save" loading={false} data-testid="button" />
    )
    
    rerender(<UnifiedButton action="save" loading={true} data-testid="button" />)
    await waitFor(() => {
      expect(screen.getByTestId("button")).toBeDisabled()
    })
    
    rerender(<UnifiedButton action="save" loading={false} data-testid="button" />)
    await waitFor(() => {
      expect(screen.getByTestId("button")).not.toBeDisabled()
    })
  })
})