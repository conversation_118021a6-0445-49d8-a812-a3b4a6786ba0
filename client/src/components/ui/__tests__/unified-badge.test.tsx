/**
 * Unified Badge System - Comprehensive Test Suite
 * 
 * Tests following TDD methodology with complete coverage:
 * - Component rendering and behavior
 * - Accessibility compliance (WCAG 2.1 AA)
 * - TypeScript type safety
 * - Backward compatibility
 * - Edge cases and error handling
 */

import React from "react"
import { render, screen, fireEvent } from "@testing-library/react"
import { vi } from "vitest"

import {
  UnifiedBadge,
  ComponentBadge,
  StatusBadge,
  PriorityBadge,
  ActiveBadge,
  InactiveBadge,
  PreferredBadge,
  useComponentBadgeProps,
  useStatusBadgeProps,
  usePriorityBadgeProps,
  isValidComponentStatus,
  isValidProjectStatus,
  isValidProjectPriority,
  getPriorityLevel,
} from "../unified-badge"

describe("UnifiedBadge", () => {
  // Basic rendering tests
  describe("Basic Rendering", () => {
    it("renders with default props", () => {
      render(<UnifiedBadge type="component" intent="active" data-testid="badge" />)
      const badge = screen.getByTestId("badge")
      expect(badge).toBeInTheDocument()
      expect(badge).toHaveAttribute("role", "status")
    })

    it("renders with custom label", () => {
      render(
        <UnifiedBadge
          type="component"
          intent="active"
          customLabel="Custom Label"
          data-testid="badge"
        />
      )
      expect(screen.getByText("Custom Label")).toBeInTheDocument()
    })

    it("applies correct CSS classes for different variants", () => {
      const { rerender } = render(
        <UnifiedBadge type="component" intent="active" variant="solid" data-testid="badge" />
      )
      let badge = screen.getByTestId("badge")
      expect(badge).toHaveClass("border-transparent")

      rerender(
        <UnifiedBadge type="component" intent="active" variant="outline" data-testid="badge" />
      )
      badge = screen.getByTestId("badge")
      expect(badge).toHaveClass("border-2")
    })

    it("applies correct size classes", () => {
      const { rerender } = render(
        <UnifiedBadge type="component" intent="active" size="sm" data-testid="badge" />
      )
      let badge = screen.getByTestId("badge")
      expect(badge).toHaveClass("px-1.5", "py-0.5", "text-xs")

      rerender(
        <UnifiedBadge type="component" intent="active" size="lg" data-testid="badge" />
      )
      badge = screen.getByTestId("badge")
      expect(badge).toHaveClass("px-3", "py-1.5", "text-base")
    })
  })

  // Type-specific tests
  describe("Badge Types", () => {
    it("renders component type badges correctly", () => {
      render(
        <UnifiedBadge
          type="component"
          intent="active"
          data-testid="component-badge"
        />
      )
      const badge = screen.getByTestId("component-badge")
      expect(badge).toBeInTheDocument()
      expect(screen.getByText("Active")).toBeInTheDocument()
    })

    it("renders status type badges correctly", () => {
      render(
        <UnifiedBadge
          type="status"
          intent="completed"
          data-testid="status-badge"
        />
      )
      const badge = screen.getByTestId("status-badge")
      expect(badge).toBeInTheDocument()
      expect(screen.getByText("Completed")).toBeInTheDocument()
    })

    it("renders priority type badges correctly", () => {
      render(
        <UnifiedBadge
          type="priority"
          intent="high"
          data-testid="priority-badge"
        />
      )
      const badge = screen.getByTestId("priority-badge")
      expect(badge).toBeInTheDocument()
      expect(screen.getByText("High")).toBeInTheDocument()
    })

    it("handles invalid intent gracefully", () => {
      const consoleSpy = vi.spyOn(console, "warn").mockImplementation(() => {})
      
      render(
        <UnifiedBadge
          type="component"
          // @ts-expect-error Testing invalid intent
          intent="invalid-intent"
          data-testid="badge"
        />
      )
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Invalid intent "invalid-intent"')
      )
      
      // Should fallback to generic info
      expect(screen.getByText("Info")).toBeInTheDocument()
      
      consoleSpy.mockRestore()
    })
  })

  // Icon and label visibility tests
  describe("Icon and Label Visibility", () => {
    it("shows icon when showIcon is true", () => {
      render(
        <UnifiedBadge
          type="component"
          intent="active"
          showIcon={true}
          data-testid="badge"
        />
      )
      // Icon should be present but hidden from screen readers
      const icons = screen.getByTestId("badge").querySelectorAll('[aria-hidden="true"]')
      expect(icons.length).toBeGreaterThan(0)
    })

    it("hides icon when showIcon is false", () => {
      render(
        <UnifiedBadge
          type="component"
          intent="active"
          showIcon={false}
          data-testid="badge"
        />
      )
      const icons = screen.getByTestId("badge").querySelectorAll('[aria-hidden="true"]')
      expect(icons.length).toBe(0)
    })

    it("shows label when showLabel is true", () => {
      render(
        <UnifiedBadge
          type="component"
          intent="active"
          showLabel={true}
          data-testid="badge"
        />
      )
      expect(screen.getByText("Active")).toBeInTheDocument()
    })

    it("hides label when showLabel is false", () => {
      render(
        <UnifiedBadge
          type="component"
          intent="active"
          showLabel={false}
          data-testid="badge"
        />
      )
      expect(screen.queryByText("Active")).not.toBeInTheDocument()
    })
  })

  // Interactive behavior tests
  describe("Interactive Behavior", () => {
    it("applies interactive styles when interactive is true", () => {
      render(
        <UnifiedBadge
          type="component"
          intent="active"
          interactive={true}
          data-testid="badge"
        />
      )
      const badge = screen.getByTestId("badge")
      expect(badge).toHaveClass("cursor-pointer")
      expect(badge).toHaveAttribute("tabIndex", "0")
    })

    it("handles click events when interactive", () => {
      const handleClick = vi.fn()
      render(
        <UnifiedBadge
          type="component"
          intent="active"
          interactive={true}
          onClick={handleClick}
          data-testid="badge"
        />
      )
      const badge = screen.getByTestId("badge")
      fireEvent.click(badge)
      expect(handleClick).toHaveBeenCalledTimes(1)
    })

    it("applies pulse animation when pulse is true", () => {
      render(
        <UnifiedBadge
          type="component"
          intent="active"
          pulse={true}
          data-testid="badge"
        />
      )
      const badge = screen.getByTestId("badge")
      expect(badge).toHaveClass("animate-pulse")
    })
  })

  // Accessibility tests
  describe("Accessibility", () => {
    it("has correct ARIA attributes", () => {
      render(
        <UnifiedBadge
          type="component"
          intent="active"
          data-testid="badge"
        />
      )
      const badge = screen.getByTestId("badge")
      expect(badge).toHaveAttribute("role", "status")
      expect(badge).toHaveAttribute("aria-label", "component active: Active")
    })

    it("supports custom ARIA labels", () => {
      render(
        <UnifiedBadge
          type="component"
          intent="active"
          customLabel="Custom Status"
          data-testid="badge"
        />
      )
      const badge = screen.getByTestId("badge")
      expect(badge).toHaveAttribute("aria-label", "component active: Custom Status")
    })

    it("has proper focus handling when interactive", () => {
      render(
        <UnifiedBadge
          type="component"
          intent="active"
          interactive={true}
          data-testid="badge"
        />
      )
      const badge = screen.getByTestId("badge")
      expect(badge).toHaveAttribute("tabIndex", "0")
      
      badge.focus()
      expect(badge).toHaveFocus()
    })

    it("icons are hidden from screen readers", () => {
      render(
        <UnifiedBadge
          type="component"
          intent="active"
          showIcon={true}
          data-testid="badge"
        />
      )
      const icons = screen.getByTestId("badge").querySelectorAll('[aria-hidden="true"]')
      icons.forEach(icon => {
        expect(icon).toHaveAttribute("aria-hidden", "true")
      })
    })
  })

  // Custom icon tests
  describe("Custom Icons", () => {
    it("renders custom icon when provided", () => {
      const CustomIcon = ({ className }: { className?: string }) => (
        <div className={className} data-testid="custom-icon">Custom</div>
      )
      
      render(
        <UnifiedBadge
          type="component"
          intent="active"
          customIcon={CustomIcon}
          data-testid="badge"
        />
      )
      
      expect(screen.getByTestId("custom-icon")).toBeInTheDocument()
    })
  })
})

// Backward compatibility components tests
describe("Backward Compatibility Components", () => {
  describe("ComponentBadge", () => {
    it("renders correctly with component status", () => {
      render(<ComponentBadge status="active" data-testid="component-badge" />)
      const badge = screen.getByTestId("component-badge")
      expect(badge).toBeInTheDocument()
      expect(screen.getByText("Active")).toBeInTheDocument()
    })

    it("supports all component status types", () => {
      const statuses = ["active", "inactive", "preferred", "available"] as const
      statuses.forEach(status => {
        const { unmount } = render(<ComponentBadge status={status} />)
        expect(screen.getByRole("status")).toBeInTheDocument()
        unmount()
      })
    })
  })

  describe("StatusBadge", () => {
    it("renders correctly with project status", () => {
      render(<StatusBadge status="completed" data-testid="status-badge" />)
      const badge = screen.getByTestId("status-badge")
      expect(badge).toBeInTheDocument()
      expect(screen.getByText("Completed")).toBeInTheDocument()
    })

    it("supports all project status types", () => {
      const statuses = ["draft", "active", "paused", "completed", "cancelled"] as const
      statuses.forEach(status => {
        const { unmount } = render(<StatusBadge status={status} />)
        expect(screen.getByRole("status")).toBeInTheDocument()
        unmount()
      })
    })
  })

  describe("PriorityBadge", () => {
    it("renders correctly with project priority", () => {
      render(<PriorityBadge priority="high" data-testid="priority-badge" />)
      const badge = screen.getByTestId("priority-badge")
      expect(badge).toBeInTheDocument()
      expect(screen.getByText("High")).toBeInTheDocument()
    })

    it("supports all priority types", () => {
      const priorities = ["low", "medium", "high", "critical"] as const
      priorities.forEach(priority => {
        const { unmount } = render(<PriorityBadge priority={priority} />)
        expect(screen.getByRole("status")).toBeInTheDocument()
        unmount()
      })
    })
  })
})

// Convenience components tests
describe("Convenience Components", () => {
  it("ActiveBadge renders with correct props", () => {
    render(<ActiveBadge data-testid="active-badge" />)
    const badge = screen.getByTestId("active-badge")
    expect(badge).toBeInTheDocument()
    expect(screen.getByText("Active")).toBeInTheDocument()
  })

  it("InactiveBadge renders with correct props", () => {
    render(<InactiveBadge data-testid="inactive-badge" />)
    const badge = screen.getByTestId("inactive-badge")
    expect(badge).toBeInTheDocument()
    expect(screen.getByText("Inactive")).toBeInTheDocument()
  })

  it("PreferredBadge renders with correct props", () => {
    render(<PreferredBadge data-testid="preferred-badge" />)
    const badge = screen.getByTestId("preferred-badge")
    expect(badge).toBeInTheDocument()
    expect(screen.getByText("Preferred")).toBeInTheDocument()
  })
})

// Utility functions tests
describe("Utility Functions", () => {
  describe("useComponentBadgeProps", () => {
    it("returns inactive for inactive components", () => {
      const result = useComponentBadgeProps({ is_active: false })
      expect(result).toEqual({ type: "component", intent: "inactive" })
    })

    it("returns preferred for preferred components", () => {
      const result = useComponentBadgeProps({ 
        is_active: true,
        is_preferred: true 
      })
      expect(result).toEqual({ type: "component", intent: "preferred" })
    })

    it("maps stock status correctly", () => {
      const result = useComponentBadgeProps({ 
        is_active: true,
        stock_status: "limited"
      })
      expect(result).toEqual({ type: "component", intent: "limited" })
    })

    it("defaults to active for active components without specific status", () => {
      const result = useComponentBadgeProps({ is_active: true })
      expect(result).toEqual({ type: "component", intent: "active" })
    })
  })

  describe("useStatusBadgeProps", () => {
    it("returns correct status for valid statuses", () => {
      const result = useStatusBadgeProps("completed")
      expect(result).toEqual({ type: "status", intent: "completed" })
    })

    it("defaults to draft for invalid statuses", () => {
      const result = useStatusBadgeProps("invalid")
      expect(result).toEqual({ type: "status", intent: "draft" })
    })
  })

  describe("usePriorityBadgeProps", () => {
    it("returns correct priority for valid priorities", () => {
      const result = usePriorityBadgeProps("high")
      expect(result).toEqual({ type: "priority", intent: "high" })
    })

    it("defaults to medium for invalid priorities", () => {
      const result = usePriorityBadgeProps("invalid")
      expect(result).toEqual({ type: "priority", intent: "medium" })
    })
  })

  describe("Type Guards", () => {
    it("isValidComponentStatus validates component statuses", () => {
      expect(isValidComponentStatus("active")).toBe(true)
      expect(isValidComponentStatus("inactive")).toBe(true)
      expect(isValidComponentStatus("invalid")).toBe(false)
    })

    it("isValidProjectStatus validates project statuses", () => {
      expect(isValidProjectStatus("draft")).toBe(true)
      expect(isValidProjectStatus("completed")).toBe(true)
      expect(isValidProjectStatus("invalid")).toBe(false)
    })

    it("isValidProjectPriority validates project priorities", () => {
      expect(isValidProjectPriority("low")).toBe(true)
      expect(isValidProjectPriority("critical")).toBe(true)
      expect(isValidProjectPriority("invalid")).toBe(false)
    })
  })

  describe("getPriorityLevel", () => {
    it("returns correct numeric levels for priorities", () => {
      expect(getPriorityLevel("low")).toBe(1)
      expect(getPriorityLevel("medium")).toBe(2)
      expect(getPriorityLevel("high")).toBe(3)
      expect(getPriorityLevel("critical")).toBe(4)
    })
  })
})

// Performance tests
describe("Performance", () => {
  it("renders quickly with large numbers of badges", () => {
    const startTime = performance.now()
    
    const badges = Array.from({ length: 100 }, (_, i) => (
      <UnifiedBadge
        key={i}
        type="component"
        intent="active"
        data-testid={`badge-${i}`}
      />
    ))
    
    render(<div>{badges}</div>)
    
    const endTime = performance.now()
    const renderTime = endTime - startTime
    
    // Should render 100 badges in less than 200ms (more realistic timing)
    expect(renderTime).toBeLessThan(200)
    
    // Verify all badges are rendered
    expect(screen.getAllByRole("status")).toHaveLength(100)
  })
})

// Edge cases and error handling
describe("Edge Cases", () => {
  it("handles undefined intent gracefully", () => {
    const consoleSpy = vi.spyOn(console, "warn").mockImplementation(() => {})
    
    render(
      <UnifiedBadge
        // @ts-expect-error Testing undefined intent
        intent={undefined}
        data-testid="badge"
      />
    )
    
    expect(consoleSpy).toHaveBeenCalled()
    expect(screen.getByTestId("badge")).toBeInTheDocument()
    
    consoleSpy.mockRestore()
  })

  it("handles empty strings gracefully", () => {
    render(
      <UnifiedBadge
        type="component"
        intent="active"
        customLabel=""
        data-testid="badge"
      />
    )
    
    const badge = screen.getByTestId("badge")
    expect(badge).toBeInTheDocument()
  })

  it("handles very long labels appropriately", () => {
    const longLabel = "A".repeat(100)
    render(
      <UnifiedBadge
        type="component"
        intent="active"
        customLabel={longLabel}
        data-testid="badge"
      />
    )
    
    expect(screen.getByText(longLabel)).toBeInTheDocument()
  })
})