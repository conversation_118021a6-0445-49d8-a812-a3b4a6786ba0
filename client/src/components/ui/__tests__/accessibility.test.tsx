/**
 * Unified Components Accessibility Test Suite
 * 
 * Comprehensive WCAG 2.1 AA compliance validation for all unified components
 * Tests semantic markup, ARIA attributes, keyboard navigation, and screen reader support
 */

import React from "react"
import { render, screen } from "@testing-library/react"
import userEvent from "@testing-library/user-event"
import { axe } from "jest-axe"
import { vi } from "vitest"

// Extend expect with accessibility matchers
expect.extend({ toHaveNoViolations: (received) => ({ pass: received.violations.length === 0, message: () => 'Expected no a11y violations' }) })

import {
  UnifiedBadge,
  UnifiedButton,
  UnifiedFormField,
  UnifiedLoading,
  UnifiedEmptyState,
  UnifiedIcon,
} from "../index"

// Extend Jest matchers
expect.extend(toHaveNoViolations)

// Mock console.warn to avoid cluttering test output
const originalWarn = console.warn
beforeAll(() => {
  console.warn = vi.fn()
})

afterAll(() => {
  console.warn = originalWarn
})

describe("WCAG 2.1 AA Accessibility Compliance", () => {
  describe("UnifiedBadge Accessibility", () => {
    it("has no accessibility violations", async () => {
      const { container } = render(
        <div>
          <UnifiedBadge intent="active" />
          <UnifiedBadge intent="error" variant="solid" />
          <UnifiedBadge intent="high" type="priority" interactive />
        </div>
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    it("provides appropriate semantic roles", () => {
      render(<UnifiedBadge intent="active" data-testid="badge" />)
      const badge = screen.getByTestId("badge")
      expect(badge).toHaveAttribute("role", "status")
    })

    it("has descriptive ARIA labels", () => {
      render(
        <UnifiedBadge 
          intent="critical" 
          type="priority"
          customLabel="High Priority Item"
          data-testid="badge" 
        />
      )
      const badge = screen.getByTestId("badge")
      expect(badge).toHaveAttribute("aria-label", "priority critical: High Priority Item")
    })

    it("supports keyboard interaction when interactive", async () => {
      const user = userEvent.setup()
      const handleClick = vi.fn()
      
      render(
        <UnifiedBadge 
          intent="active" 
          interactive
          onClick={handleClick}
          data-testid="badge" 
        />
      )
      
      const badge = screen.getByTestId("badge")
      expect(badge).toHaveAttribute("tabIndex", "0")
      
      badge.focus()
      expect(badge).toHaveFocus()
      
      await user.keyboard(" ")
      expect(handleClick).toHaveBeenCalledTimes(1)
      
      await user.keyboard("{Enter}")
      expect(handleClick).toHaveBeenCalledTimes(2)
    })

    it("icons are hidden from screen readers", () => {
      render(
        <UnifiedBadge 
          intent="active" 
          showIcon={true}
          data-testid="badge" 
        />
      )
      
      const badge = screen.getByTestId("badge")
      const icons = badge.querySelectorAll('[aria-hidden="true"]')
      expect(icons.length).toBeGreaterThan(0)
      
      icons.forEach(icon => {
        expect(icon).toHaveAttribute("aria-hidden", "true")
      })
    })

    it("maintains proper contrast ratios", () => {
      // Test high contrast variants
      const { container } = render(
        <div>
          <UnifiedBadge intent="error" variant="solid" />
          <UnifiedBadge intent="success" variant="solid" />
          <UnifiedBadge intent="warning" variant="solid" />
        </div>
      )
      
      // Solid variants should have high contrast background colors
      const badges = container.querySelectorAll('[role="status"]')
      badges.forEach(badge => {
        // Should have solid background styling for contrast
        expect(badge).toHaveClass("border-transparent")
      })
    })
  })

  describe("UnifiedButton Accessibility", () => {
    it("has no accessibility violations", async () => {
      const { container } = render(
        <div>
          <UnifiedButton action="save" />
          <UnifiedButton action="delete" variant="destructive" />
          <UnifiedButton action="edit" display="both" />
          <UnifiedButton action="more" display="icon" />
        </div>
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    it("has proper semantic button element", () => {
      render(<UnifiedButton action="save" data-testid="button" />)
      const button = screen.getByTestId("button")
      expect(button.tagName).toBe("BUTTON")
      expect(button).toHaveAttribute("type", "button")
    })

    it("provides appropriate ARIA labels for icon-only buttons", () => {
      render(
        <UnifiedButton 
          action="edit" 
          display="icon"
          data-testid="button" 
        />
      )
      const button = screen.getByTestId("button")
      expect(button).toHaveAttribute("aria-label", "Edit")
    })

    it("handles disabled state accessibility", () => {
      render(
        <UnifiedButton 
          action="save" 
          disabled={true}
          data-testid="button" 
        />
      )
      const button = screen.getByTestId("button")
      expect(button).toBeDisabled()
      expect(button).toHaveAttribute("aria-disabled", "true")
    })

    it("handles loading state accessibility", () => {
      render(
        <UnifiedButton 
          action="save" 
          loading={true}
          loadingText="Saving..."
          data-testid="button" 
        />
      )
      const button = screen.getByTestId("button")
      expect(button).toBeDisabled()
      expect(screen.getByText("Saving...")).toBeInTheDocument()
    })

    it("supports keyboard navigation", async () => {
      const user = userEvent.setup()
      const handleClick = vi.fn()
      
      render(
        <div>
          <UnifiedButton action="create" onClick={handleClick} data-testid="button1" />
          <UnifiedButton action="edit" onClick={handleClick} data-testid="button2" />
        </div>
      )
      
      // Tab navigation
      await user.tab()
      expect(screen.getByTestId("button1")).toHaveFocus()
      
      await user.tab()
      expect(screen.getByTestId("button2")).toHaveFocus()
      
      // Enter and Space activation
      await user.keyboard("{Enter}")
      expect(handleClick).toHaveBeenCalledTimes(1)
      
      await user.keyboard(" ")
      expect(handleClick).toHaveBeenCalledTimes(2)
    })

    it("icons are hidden from screen readers", () => {
      render(
        <UnifiedButton 
          action="save" 
          display="both"
          data-testid="button" 
        />
      )
      
      const button = screen.getByTestId("button")
      const icon = button.querySelector('svg')
      expect(icon).toHaveAttribute("aria-hidden", "true")
    })
  })

  describe("UnifiedFormField Accessibility", () => {
    it("has no accessibility violations", async () => {
      const { container } = render(
        <div>
          <UnifiedFormField label="Name" required />
          <UnifiedFormField 
            label="Email" 
            type="email" 
            error="Invalid email"
            touched 
          />
          <UnifiedFormField 
            label="Password" 
            type="password" 
            showPasswordToggle 
          />
          <UnifiedFormField 
            label="Comments" 
            type="textarea" 
            helpText="Optional comments"
          />
        </div>
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    it("properly associates labels with inputs", () => {
      render(
        <UnifiedFormField 
          label="Test Field" 
          data-testid="form-field" 
        />
      )
      
      const input = screen.getByRole("textbox", { name: "Test Field" })
      const label = screen.getByText("Test Field")
      
      expect(input).toBeInTheDocument()
      expect(label.tagName).toBe("LABEL")
      expect(label).toHaveAttribute("for", input.id)
    })

    it("indicates required fields appropriately", () => {
      render(
        <UnifiedFormField 
          label="Required Field" 
          required={true}
          data-testid="form-field" 
        />
      )
      
      const input = screen.getByRole("textbox")
      expect(input).toHaveAttribute("aria-required", "true")
      
      // Visual required indicator
      expect(screen.getByText("*")).toBeInTheDocument()
    })

    it("provides error state accessibility", () => {
      render(
        <UnifiedFormField 
          label="Test Field" 
          error="This field is required"
          touched={true}
          data-testid="form-field" 
        />
      )
      
      const input = screen.getByRole("textbox")
      expect(input).toHaveAttribute("aria-invalid", "true")
      
      const errorMessage = screen.getByText("This field is required")
      expect(errorMessage).toBeInTheDocument()
      
      // Error should be associated with input
      expect(input).toHaveAttribute("aria-describedby", 
        expect.stringContaining(errorMessage.id)
      )
    })

    it("provides help text accessibility", () => {
      render(
        <UnifiedFormField 
          label="Test Field" 
          helpText="This is helpful information"
          data-testid="form-field" 
        />
      )
      
      const input = screen.getByRole("textbox")
      const helpText = screen.getByText("This is helpful information")
      
      expect(input).toHaveAttribute("aria-describedby", 
        expect.stringContaining(helpText.id)
      )
    })

    it("password toggle button has proper accessibility", () => {
      render(
        <UnifiedFormField 
          label="Password" 
          type="password"
          showPasswordToggle={true}
          data-testid="form-field" 
        />
      )
      
      const toggleButton = screen.getByRole("button", { name: /show password/i })
      expect(toggleButton).toBeInTheDocument()
      expect(toggleButton).toHaveAttribute("type", "button")
    })

    it("clear button has proper accessibility", () => {
      render(
        <UnifiedFormField 
          label="Test Field" 
          clearable={true}
          value="test value"
          onChange={() => {}}
          data-testid="form-field" 
        />
      )
      
      const clearButton = screen.getByRole("button", { name: /clear input/i })
      expect(clearButton).toBeInTheDocument()
      expect(clearButton).toHaveAttribute("type", "button")
    })

    it("supports keyboard navigation for interactive elements", async () => {
      const user = userEvent.setup()
      const handleChange = vi.fn()
      
      render(
        <UnifiedFormField 
          label="Test Field" 
          clearable={true}
          showPasswordToggle={true}
          type="password"
          value="test"
          onChange={handleChange}
          data-testid="form-field" 
        />
      )
      
      // Tab through interactive elements
      await user.tab() // Input field
      expect(screen.getByRole("textbox")).toHaveFocus()
      
      await user.tab() // Clear button
      expect(screen.getByRole("button", { name: /clear input/i })).toHaveFocus()
      
      await user.tab() // Password toggle button
      expect(screen.getByRole("button", { name: /show password/i })).toHaveFocus()
    })
  })

  describe("UnifiedLoading Accessibility", () => {
    it("has no accessibility violations", async () => {
      const { container } = render(
        <div>
          <UnifiedLoading text="Loading..." />
          <UnifiedLoading variant="dots" />
          <UnifiedLoading variant="pulse" size="lg" />
        </div>
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    it("provides appropriate status role", () => {
      render(<UnifiedLoading text="Loading content..." data-testid="loading" />)
      const loading = screen.getByTestId("loading")
      expect(loading).toHaveAttribute("role", "status")
    })

    it("has descriptive aria-label", () => {
      render(<UnifiedLoading text="Processing data..." data-testid="loading" />)
      const loading = screen.getByTestId("loading")
      expect(loading).toHaveAttribute("aria-label", "Processing data...")
    })

    it("defaults to 'Loading' when no text provided", () => {
      render(<UnifiedLoading data-testid="loading" />)
      const loading = screen.getByTestId("loading")
      expect(loading).toHaveAttribute("aria-label", "Loading")
    })

    it("is not focusable", () => {
      render(<UnifiedLoading data-testid="loading" />)
      const loading = screen.getByTestId("loading")
      expect(loading).not.toHaveAttribute("tabIndex")
    })
  })

  describe("UnifiedEmptyState Accessibility", () => {
    it("has no accessibility violations", async () => {
      const { container } = render(
        <div>
          <UnifiedEmptyState 
            title="No items found"
            description="Try adjusting your search"
          />
          <UnifiedEmptyState 
            variant="search"
            title="No results"
            action={<button>Add Item</button>}
          />
        </div>
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    it("uses proper heading hierarchy", () => {
      render(
        <UnifiedEmptyState 
          title="No items found"
          description="Try again later"
          data-testid="empty-state"
        />
      )
      
      const heading = screen.getByRole("heading", { level: 3 })
      expect(heading).toHaveTextContent("No items found")
    })

    it("provides descriptive content structure", () => {
      render(
        <UnifiedEmptyState 
          title="No items found"
          description="Try adjusting your search filters"
          data-testid="empty-state"
        />
      )
      
      expect(screen.getByRole("heading", { name: "No items found" })).toBeInTheDocument()
      expect(screen.getByText("Try adjusting your search filters")).toBeInTheDocument()
    })

    it("action buttons are properly accessible", async () => {
      const user = userEvent.setup()
      const handleAction = vi.fn()
      
      render(
        <UnifiedEmptyState 
          title="No items"
          action={
            <button onClick={handleAction}>
              Add New Item
            </button>
          }
          data-testid="empty-state"
        />
      )
      
      const actionButton = screen.getByRole("button", { name: "Add New Item" })
      expect(actionButton).toBeInTheDocument()
      
      await user.click(actionButton)
      expect(handleAction).toHaveBeenCalledTimes(1)
    })

    it("is not focusable by default", () => {
      render(
        <UnifiedEmptyState 
          title="No items"
          data-testid="empty-state"
        />
      )
      
      const emptyState = screen.getByTestId("empty-state")
      expect(emptyState).not.toHaveAttribute("tabIndex")
    })
  })

  describe("UnifiedIcon Accessibility", () => {
    it("has no accessibility violations", async () => {
      const { container } = render(
        <div>
          <UnifiedIcon type="resistor" />
          <UnifiedIcon type="add" color="primary" />
          <UnifiedIcon type="edit" size="lg" />
        </div>
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    it("provides appropriate img role", () => {
      render(<UnifiedIcon type="resistor" data-testid="icon" />)
      const icon = screen.getByTestId("icon")
      expect(icon).toHaveAttribute("role", "img")
    })

    it("has descriptive aria-label", () => {
      render(<UnifiedIcon type="temperature_sensor" data-testid="icon" />)
      const icon = screen.getByTestId("icon")
      expect(icon).toHaveAttribute("aria-label", "temperature_sensor icon")
    })

    it("SVG elements are hidden from screen readers", () => {
      render(<UnifiedIcon type="resistor" data-testid="icon" />)
      const icon = screen.getByTestId("icon")
      const svg = icon.querySelector('svg')
      expect(svg).toHaveAttribute("aria-hidden", "true")
    })

    it("is not focusable by default", () => {
      render(<UnifiedIcon type="resistor" data-testid="icon" />)
      const icon = screen.getByTestId("icon")
      expect(icon).not.toHaveAttribute("tabIndex")
    })
  })

  describe("Color Contrast and Visual Accessibility", () => {
    it("maintains proper contrast for error states", () => {
      render(
        <div>
          <UnifiedBadge intent="error" variant="solid" data-testid="error-badge" />
          <UnifiedButton action="delete" variant="destructive" data-testid="delete-button" />
          <UnifiedFormField 
            label="Test" 
            error="Error message"
            touched={true}
            data-testid="error-form"
          />
        </div>
      )
      
      // Error states should use high contrast colors
      const errorBadge = screen.getByTestId("error-badge")
      const deleteButton = screen.getByTestId("delete-button")
      
      expect(errorBadge).toHaveClass("bg-red-600") // High contrast error color
      expect(deleteButton).toHaveClass("bg-destructive") // High contrast destructive color
    })

    it("provides sufficient visual distinction for states", () => {
      render(
        <div>
          <UnifiedBadge intent="success" data-testid="success" />
          <UnifiedBadge intent="warning" data-testid="warning" />
          <UnifiedBadge intent="error" data-testid="error" />
          <UnifiedBadge intent="info" data-testid="info" />
        </div>
      )
      
      // Each state should have distinct styling
      const success = screen.getByTestId("success")
      const warning = screen.getByTestId("warning")
      const error = screen.getByTestId("error")
      const info = screen.getByTestId("info")
      
      // Colors should be visually distinct
      expect(success.className).not.toBe(warning.className)
      expect(warning.className).not.toBe(error.className)
      expect(error.className).not.toBe(info.className)
    })
  })

  describe("Screen Reader Experience", () => {
    it("provides logical reading order", () => {
      render(
        <div>
          <UnifiedFormField 
            label="Name"
            required
            description="Enter your full name"
            helpText="This will be displayed publicly"
            data-testid="form"
          />
          <UnifiedButton action="save" display="both" data-testid="button" />
        </div>
      )
      
      // Elements should be in logical tab order
      const input = screen.getByRole("textbox")
      const button = screen.getByRole("button")
      
      expect(input.tabIndex).toBeLessThan(button.tabIndex || 0)
    })

    it("provides meaningful text for status updates", () => {
      const { rerender } = render(
        <UnifiedLoading text="Loading..." data-testid="loading" />
      )
      
      let loading = screen.getByTestId("loading")
      expect(loading).toHaveAttribute("aria-label", "Loading...")
      
      rerender(
        <UnifiedEmptyState 
          title="Load complete - no items found"
          data-testid="empty"
        />
      )
      
      // Status change should be announced to screen readers
      const empty = screen.getByTestId("empty")
      const heading = empty.querySelector('h3')
      expect(heading).toHaveTextContent("Load complete - no items found")
    })
  })

  describe("Keyboard Navigation Patterns", () => {
    it("supports proper tab sequence", async () => {
      const user = userEvent.setup()
      
      render(
        <div>
          <UnifiedButton action="create" data-testid="btn1" />
          <UnifiedFormField label="Input" data-testid="input" />
          <UnifiedBadge intent="active" interactive data-testid="badge" />
          <UnifiedButton action="delete" data-testid="btn2" />
        </div>
      )
      
      // Tab through focusable elements in order
      await user.tab()
      expect(screen.getByTestId("btn1")).toHaveFocus()
      
      await user.tab()
      expect(screen.getByRole("textbox")).toHaveFocus()
      
      await user.tab()
      expect(screen.getByTestId("badge")).toHaveFocus()
      
      await user.tab()
      expect(screen.getByTestId("btn2")).toHaveFocus()
    })

    it("supports reverse tab navigation", async () => {
      const user = userEvent.setup()
      
      render(
        <div>
          <UnifiedButton action="create" data-testid="btn1" />
          <UnifiedButton action="delete" data-testid="btn2" />
        </div>
      )
      
      // Focus last button first
      screen.getByTestId("btn2").focus()
      expect(screen.getByTestId("btn2")).toHaveFocus()
      
      // Shift+Tab should go to previous element
      await user.keyboard("{Shift>}{Tab}{/Shift}")
      expect(screen.getByTestId("btn1")).toHaveFocus()
    })

    it("escape key handling for interactive elements", async () => {
      const user = userEvent.setup()
      const handleEscape = vi.fn()
      
      render(
        <UnifiedFormField 
          label="Test Field"
          onKeyDown={(e) => {
            if (e.key === 'Escape') handleEscape()
          }}
          data-testid="input"
        />
      )
      
      const input = screen.getByRole("textbox")
      input.focus()
      
      await user.keyboard("{Escape}")
      expect(handleEscape).toHaveBeenCalledTimes(1)
    })
  })
})