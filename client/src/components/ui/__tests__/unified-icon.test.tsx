/**
 * Unified Icon System - Comprehensive Test Suite
 * 
 * Tests following TDD methodology with complete coverage:
 * - Icon rendering and types
 * - Electrical component icons
 * - General UI icons
 * - Accessibility compliance (WCAG 2.1 AA)
 * - Backward compatibility
 * - Performance and edge cases
 */

import React from "react"
import { render, screen } from "@testing-library/react"
import { vi } from "vitest"

import {
  UnifiedIcon,
  ComponentIcon,
  ResistorIcon,
  CapacitorIcon,
  TransistorIcon,
  ICIcon,
  SensorIcon,
  SwitchIcon,
  ConnectorIcon,
  PowerSupplyIcon,
  MotorIcon,
  BatteryIcon,
  AddIcon,
  EditIcon,
  DeleteIcon,
  SearchIcon,
  SettingsIcon,
  UserIcon,
  HomeIcon,
  ElectricalIcon,
  UIIcon,
  getIconComponent,
  hasIcon,
  isElectricalComponent,
  isGeneralIcon,
  getIconsByCategory,
  useComponentIconProps,
  availableElectricalTypes,
  availableGeneralTypes,
  availableIconTypes,
} from "../unified-icon"

describe("UnifiedIcon", () => {
  // Basic rendering tests
  describe("Basic Rendering", () => {
    it("renders with default props", () => {
      render(<UnifiedIcon type="resistor" data-testid="icon" />)
      const icon = screen.getByTestId("icon")
      expect(icon).toBeInTheDocument()
      expect(icon).toHaveAttribute("role", "img")
      expect(icon).toHaveAttribute("aria-label", "resistor icon")
    })

    it("renders with electrical component type", () => {
      render(<UnifiedIcon type="transistor" data-testid="transistor-icon" />)
      const icon = screen.getByTestId("transistor-icon")
      expect(icon).toBeInTheDocument()
      
      // Should contain SVG element
      const svg = icon.querySelector('svg')
      expect(svg).toBeInTheDocument()
      expect(svg).toHaveAttribute("aria-hidden", "true")
    })

    it("renders with general UI icon type", () => {
      render(<UnifiedIcon type="add" data-testid="add-icon" />)
      const icon = screen.getByTestId("add-icon")
      expect(icon).toBeInTheDocument()
      
      const svg = icon.querySelector('svg')
      expect(svg).toBeInTheDocument()
    })

    it("falls back to default icon for invalid types", () => {
      render(<UnifiedIcon type="invalid-type" data-testid="icon" />)
      const icon = screen.getByTestId("icon")
      expect(icon).toBeInTheDocument()
      
      // Should render the default component icon
      const svg = icon.querySelector('svg')
      expect(svg).toBeInTheDocument()
    })
  })

  // Size tests
  describe("Sizes", () => {
    it("applies correct size classes", () => {
      const sizes = ["xs", "sm", "md", "lg", "xl", "2xl", "3xl"] as const
      const expectedClasses = ["w-3 h-3", "w-4 h-4", "w-5 h-5", "w-6 h-6", "w-8 h-8", "w-10 h-10", "w-12 h-12"]
      
      sizes.forEach((size, index) => {
        const { unmount } = render(
          <UnifiedIcon type="resistor" size={size} data-testid={`icon-${size}`} />
        )
        const icon = screen.getByTestId(`icon-${size}`)
        const expectedClass = expectedClasses[index]
        
        expectedClass.split(' ').forEach(cls => {
          expect(icon).toHaveClass(cls)
        })
        
        unmount()
      })
    })
  })

  // Color tests
  describe("Colors", () => {
    it("applies correct color classes", () => {
      const colors = [
        { color: "default", expectedClass: "text-foreground" },
        { color: "muted", expectedClass: "text-muted-foreground" },
        { color: "primary", expectedClass: "text-primary" },
        { color: "destructive", expectedClass: "text-destructive" },
        { color: "electrical", expectedClass: "text-blue-600" },
        { color: "power", expectedClass: "text-red-600" },
        { color: "signal", expectedClass: "text-green-600" },
      ] as const
      
      colors.forEach(({ color, expectedClass }) => {
        const { unmount } = render(
          <UnifiedIcon 
            type="resistor" 
            color={color} 
            data-testid={`icon-${color}`} 
          />
        )
        const icon = screen.getByTestId(`icon-${color}`)
        expect(icon).toHaveClass(expectedClass)
        unmount()
      })
    })
  })

  // Electrical component icons tests
  describe("Electrical Component Icons", () => {
    it("renders basic electrical components correctly", () => {
      const basicComponents = ["resistor", "capacitor", "inductor", "diode", "led", "transistor"] as const
      
      basicComponents.forEach(component => {
        const { unmount } = render(
          <UnifiedIcon type={component} data-testid={`${component}-icon`} />
        )
        const icon = screen.getByTestId(`${component}-icon`)
        expect(icon).toBeInTheDocument()
        expect(icon).toHaveAttribute("aria-label", `${component} icon`)
        unmount()
      })
    })

    it("renders power components correctly", () => {
      const powerComponents = ["battery", "power_supply", "transformer", "regulator"] as const
      
      powerComponents.forEach(component => {
        const { unmount } = render(
          <UnifiedIcon type={component} data-testid={`${component}-icon`} />
        )
        const icon = screen.getByTestId(`${component}-icon`)
        expect(icon).toBeInTheDocument()
        unmount()
      })
    })

    it("renders communication components correctly", () => {
      const commComponents = ["antenna", "bluetooth", "wifi", "router"] as const
      
      commComponents.forEach(component => {
        const { unmount } = render(
          <UnifiedIcon type={component} data-testid={`${component}-icon`} />
        )
        const icon = screen.getByTestId(`${component}-icon`)
        expect(icon).toBeInTheDocument()
        unmount()
      })
    })

    it("renders sensor components correctly", () => {
      const sensors = ["sensor", "temperature_sensor", "pressure_sensor", "proximity_sensor"] as const
      
      sensors.forEach(sensor => {
        const { unmount } = render(
          <UnifiedIcon type={sensor} data-testid={`${sensor}-icon`} />
        )
        const icon = screen.getByTestId(`${sensor}-icon`)
        expect(icon).toBeInTheDocument()
        unmount()
      })
    })
  })

  // General UI icons tests
  describe("General UI Icons", () => {
    it("renders action icons correctly", () => {
      const actions = ["add", "edit", "delete", "save", "copy"] as const
      
      actions.forEach(action => {
        const { unmount } = render(
          <UnifiedIcon type={action} data-testid={`${action}-icon`} />
        )
        const icon = screen.getByTestId(`${action}-icon`)
        expect(icon).toBeInTheDocument()
        unmount()
      })
    })

    it("renders navigation icons correctly", () => {
      const navIcons = ["home", "back", "forward", "menu"] as const
      
      navIcons.forEach(nav => {
        const { unmount } = render(
          <UnifiedIcon type={nav} data-testid={`${nav}-icon`} />
        )
        const icon = screen.getByTestId(`${nav}-icon`)
        expect(icon).toBeInTheDocument()
        unmount()
      })
    })

    it("renders file icons correctly", () => {
      const fileIcons = ["file", "folder", "download", "upload"] as const
      
      fileIcons.forEach(file => {
        const { unmount } = render(
          <UnifiedIcon type={file} data-testid={`${file}-icon`} />
        )
        const icon = screen.getByTestId(`${file}-icon`)
        expect(icon).toBeInTheDocument()
        unmount()
      })
    })

    it("renders status icons correctly", () => {
      const statusIcons = ["success", "error", "warning", "info"] as const
      
      statusIcons.forEach(status => {
        const { unmount } = render(
          <UnifiedIcon type={status} data-testid={`${status}-icon`} />
        )
        const icon = screen.getByTestId(`${status}-icon`)
        expect(icon).toBeInTheDocument()
        unmount()
      })
    })
  })

  // Custom icon tests
  describe("Custom Icons", () => {
    it("renders custom icon when provided", () => {
      const CustomIcon = ({ className }: { className?: string }) => (
        <div className={className} data-testid="custom-icon">★</div>
      )
      
      render(
        <UnifiedIcon 
          type="custom" 
          customIcon={CustomIcon}
          data-testid="icon"
        />
      )
      
      expect(screen.getByTestId("custom-icon")).toBeInTheDocument()
      expect(screen.getByText("★")).toBeInTheDocument()
    })

    it("custom icon receives correct className", () => {
      const CustomIcon = ({ className }: { className?: string }) => (
        <div className={className} data-testid="custom-icon">Custom</div>
      )
      
      render(
        <UnifiedIcon 
          type="custom" 
          customIcon={CustomIcon}
          size="lg"
          data-testid="icon"
        />
      )
      
      const customIcon = screen.getByTestId("custom-icon")
      expect(customIcon).toHaveClass("h-full", "w-full")
    })
  })

  // Accessibility tests
  describe("Accessibility", () => {
    it("has correct ARIA attributes", () => {
      render(<UnifiedIcon type="resistor" data-testid="icon" />)
      const icon = screen.getByTestId("icon")
      expect(icon).toHaveAttribute("role", "img")
      expect(icon).toHaveAttribute("aria-label", "resistor icon")
    })

    it("SVG icons are hidden from screen readers", () => {
      render(<UnifiedIcon type="resistor" data-testid="icon" />)
      const icon = screen.getByTestId("icon")
      const svg = icon.querySelector('svg')
      expect(svg).toHaveAttribute("aria-hidden", "true")
    })

    it("supports custom aria-label through type prop", () => {
      render(<UnifiedIcon type="temperature_sensor" data-testid="icon" />)
      const icon = screen.getByTestId("icon")
      expect(icon).toHaveAttribute("aria-label", "temperature_sensor icon")
    })
  })
})

// Backward compatibility component tests
describe("ComponentIcon (Backward Compatibility)", () => {
  it("renders identically to UnifiedIcon for electrical components", () => {
    render(<ComponentIcon type="resistor" data-testid="component-icon" />)
    const icon = screen.getByTestId("component-icon")
    expect(icon).toBeInTheDocument()
    expect(icon).toHaveClass("text-blue-600") // electrical color by default
  })

  it("supports all electrical component types", () => {
    const electricalTypes = ["resistor", "capacitor", "transistor", "ic"] as const
    
    electricalTypes.forEach(type => {
      const { unmount } = render(
        <ComponentIcon type={type} data-testid={`component-${type}`} />
      )
      const icon = screen.getByTestId(`component-${type}`)
      expect(icon).toBeInTheDocument()
      unmount()
    })
  })
})

// Convenience components tests
describe("Electrical Component Convenience Components", () => {
  it("ResistorIcon renders with correct props", () => {
    render(<ResistorIcon data-testid="resistor-icon" />)
    const icon = screen.getByTestId("resistor-icon")
    expect(icon).toBeInTheDocument()
    expect(icon).toHaveClass("text-blue-600")
  })

  it("CapacitorIcon renders with correct props", () => {
    render(<CapacitorIcon data-testid="capacitor-icon" />)
    const icon = screen.getByTestId("capacitor-icon")
    expect(icon).toBeInTheDocument()
  })

  it("TransistorIcon renders with correct props", () => {
    render(<TransistorIcon data-testid="transistor-icon" />)
    const icon = screen.getByTestId("transistor-icon")
    expect(icon).toBeInTheDocument()
  })

  it("ICIcon renders with correct props", () => {
    render(<ICIcon data-testid="ic-icon" />)
    const icon = screen.getByTestId("ic-icon")
    expect(icon).toBeInTheDocument()
  })

  it("SensorIcon renders with signal color", () => {
    render(<SensorIcon data-testid="sensor-icon" />)
    const icon = screen.getByTestId("sensor-icon")
    expect(icon).toBeInTheDocument()
    expect(icon).toHaveClass("text-green-600") // signal color
  })

  it("SwitchIcon renders with power color", () => {
    render(<SwitchIcon data-testid="switch-icon" />)
    const icon = screen.getByTestId("switch-icon")
    expect(icon).toBeInTheDocument()
    expect(icon).toHaveClass("text-red-600") // power color
  })

  it("PowerSupplyIcon renders with power color", () => {
    render(<PowerSupplyIcon data-testid="power-supply-icon" />)
    const icon = screen.getByTestId("power-supply-icon")
    expect(icon).toBeInTheDocument()
    expect(icon).toHaveClass("text-red-600") // power color
  })

  it("MotorIcon renders with mechanical color", () => {
    render(<MotorIcon data-testid="motor-icon" />)
    const icon = screen.getByTestId("motor-icon")
    expect(icon).toBeInTheDocument()
    expect(icon).toHaveClass("text-gray-600") // mechanical color
  })

  it("BatteryIcon renders with power color", () => {
    render(<BatteryIcon data-testid="battery-icon" />)
    const icon = screen.getByTestId("battery-icon")
    expect(icon).toBeInTheDocument()
    expect(icon).toHaveClass("text-red-600") // power color
  })
})

// UI convenience components tests
describe("UI Convenience Components", () => {
  it("AddIcon renders correctly", () => {
    render(<AddIcon data-testid="add-icon" />)
    const icon = screen.getByTestId("add-icon")
    expect(icon).toBeInTheDocument()
  })

  it("EditIcon renders correctly", () => {
    render(<EditIcon data-testid="edit-icon" />)
    const icon = screen.getByTestId("edit-icon")
    expect(icon).toBeInTheDocument()
  })

  it("DeleteIcon renders with destructive color", () => {
    render(<DeleteIcon data-testid="delete-icon" />)
    const icon = screen.getByTestId("delete-icon")
    expect(icon).toBeInTheDocument()
    expect(icon).toHaveClass("text-destructive")
  })

  it("SearchIcon renders correctly", () => {
    render(<SearchIcon data-testid="search-icon" />)
    const icon = screen.getByTestId("search-icon")
    expect(icon).toBeInTheDocument()
  })

  it("SettingsIcon renders correctly", () => {
    render(<SettingsIcon data-testid="settings-icon" />)
    const icon = screen.getByTestId("settings-icon")
    expect(icon).toBeInTheDocument()
  })

  it("UserIcon renders correctly", () => {
    render(<UserIcon data-testid="user-icon" />)
    const icon = screen.getByTestId("user-icon")
    expect(icon).toBeInTheDocument()
  })

  it("HomeIcon renders correctly", () => {
    render(<HomeIcon data-testid="home-icon" />)
    const icon = screen.getByTestId("home-icon")
    expect(icon).toBeInTheDocument()
  })
})

// Category components tests
describe("Category Components", () => {
  it("ElectricalIcon renders with electrical color by default", () => {
    render(<ElectricalIcon type="resistor" data-testid="electrical-icon" />)
    const icon = screen.getByTestId("electrical-icon")
    expect(icon).toBeInTheDocument()
    expect(icon).toHaveClass("text-blue-600")
  })

  it("UIIcon renders general icons correctly", () => {
    render(<UIIcon type="add" data-testid="ui-icon" />)
    const icon = screen.getByTestId("ui-icon")
    expect(icon).toBeInTheDocument()
  })
})

// Utility functions tests
describe("Utility Functions", () => {
  describe("getIconComponent", () => {
    it("returns correct icon component for valid types", () => {
      const ResistorComponent = getIconComponent("resistor")
      expect(ResistorComponent).toBeDefined()
      
      const AddComponent = getIconComponent("add")
      expect(AddComponent).toBeDefined()
    })

    it("returns default icon for invalid types", () => {
      const DefaultComponent = getIconComponent("invalid-type")
      expect(DefaultComponent).toBeDefined()
    })
  })

  describe("hasIcon", () => {
    it("returns true for existing icon types", () => {
      expect(hasIcon("resistor")).toBe(true)
      expect(hasIcon("add")).toBe(true)
      expect(hasIcon("edit")).toBe(true)
    })

    it("returns false for non-existing icon types", () => {
      expect(hasIcon("invalid")).toBe(false)
      expect(hasIcon("")).toBe(false)
    })
  })

  describe("isElectricalComponent", () => {
    it("identifies electrical components correctly", () => {
      expect(isElectricalComponent("resistor")).toBe(true)
      expect(isElectricalComponent("capacitor")).toBe(true)
      expect(isElectricalComponent("battery")).toBe(true)
    })

    it("identifies non-electrical components correctly", () => {
      expect(isElectricalComponent("add")).toBe(false)
      expect(isElectricalComponent("home")).toBe(false)
      expect(isElectricalComponent("invalid")).toBe(false)
    })
  })

  describe("isGeneralIcon", () => {
    it("identifies general icons correctly", () => {
      expect(isGeneralIcon("add")).toBe(true)
      expect(isGeneralIcon("edit")).toBe(true)
      expect(isGeneralIcon("home")).toBe(true)
    })

    it("identifies non-general icons correctly", () => {
      expect(isGeneralIcon("resistor")).toBe(false)
      expect(isGeneralIcon("invalid")).toBe(false)
    })
  })

  describe("getIconsByCategory", () => {
    it("returns categorized icons", () => {
      const categories = getIconsByCategory()
      expect(categories).toBeDefined()
      expect(categories.electrical).toBeDefined()
      expect(categories.ui).toBeDefined()
    })

    it("electrical categories contain expected components", () => {
      const categories = getIconsByCategory()
      expect(categories.electrical.basic).toContain("resistor")
      expect(categories.electrical.power).toContain("battery")
      expect(categories.electrical.sensors).toContain("sensor")
    })

    it("UI categories contain expected icons", () => {
      const categories = getIconsByCategory()
      expect(categories.ui.actions).toContain("add")
      expect(categories.ui.navigation).toContain("home")
      expect(categories.ui.files).toContain("file")
    })
  })

  describe("useComponentIconProps", () => {
    it("returns component type when available", () => {
      const result = useComponentIconProps({ 
        component_type: "Resistor" 
      })
      expect(result).toEqual({ type: "resistor" })
    })

    it("falls back to category when component_type unavailable", () => {
      const result = useComponentIconProps({ 
        category: "Power Supply" 
      })
      expect(result).toEqual({ type: "power supply" })
    })

    it("defaults to 'default' when nothing available", () => {
      const result = useComponentIconProps({})
      expect(result).toEqual({ type: "default" })
    })
  })

  describe("Available Types Arrays", () => {
    it("availableElectricalTypes contains electrical components", () => {
      expect(Array.isArray(availableElectricalTypes)).toBe(true)
      expect(availableElectricalTypes).toContain("resistor")
      expect(availableElectricalTypes).toContain("capacitor")
      expect(availableElectricalTypes).toContain("battery")
    })

    it("availableGeneralTypes contains UI icons", () => {
      expect(Array.isArray(availableGeneralTypes)).toBe(true)
      expect(availableGeneralTypes).toContain("add")
      expect(availableGeneralTypes).toContain("edit")
      expect(availableGeneralTypes).toContain("home")
    })

    it("availableIconTypes contains all icons", () => {
      expect(Array.isArray(availableIconTypes)).toBe(true)
      expect(availableIconTypes).toContain("resistor")
      expect(availableIconTypes).toContain("add")
      expect(availableIconTypes.length).toBeGreaterThan(availableElectricalTypes.length)
    })
  })
})

// Performance tests
describe("Performance", () => {
  it("renders multiple icons quickly", () => {
    const startTime = performance.now()
    
    const icons = Array.from({ length: 100 }, (_, i) => (
      <UnifiedIcon
        key={i}
        type={i % 2 === 0 ? "resistor" : "add"}
        data-testid={`icon-${i}`}
      />
    ))
    
    render(<div>{icons}</div>)
    
    const endTime = performance.now()
    const renderTime = endTime - startTime
    
    // Should render 100 icons in less than 100ms
    expect(renderTime).toBeLessThan(100)
    
    // Verify all icons are rendered
    const renderedIcons = screen.getAllByRole("img")
    expect(renderedIcons).toHaveLength(100)
  })
})

// Edge cases
describe("Edge Cases", () => {
  it("handles undefined type gracefully", () => {
    render(
      <UnifiedIcon 
        // @ts-expect-error Testing undefined type
        type={undefined}
        data-testid="icon" 
      />
    )
    const icon = screen.getByTestId("icon")
    expect(icon).toBeInTheDocument()
  })

  it("handles empty string type", () => {
    render(<UnifiedIcon type="" data-testid="icon" />)
    const icon = screen.getByTestId("icon")
    expect(icon).toBeInTheDocument()
  })

  it("handles very long type names", () => {
    const longType = "a".repeat(100)
    render(<UnifiedIcon type={longType} data-testid="icon" />)
    const icon = screen.getByTestId("icon")
    expect(icon).toBeInTheDocument()
    expect(icon).toHaveAttribute("aria-label", `${longType} icon`)
  })

  it("handles special characters in type names", () => {
    render(<UnifiedIcon type="test-icon_123" data-testid="icon" />)
    const icon = screen.getByTestId("icon")
    expect(icon).toBeInTheDocument()
  })
})