/**
 * Molecular Components - Composed Building Blocks
 * 
 * Atomic design molecules combining multiple atoms to create
 * more complex, reusable interface components.
 * 
 * Architecture:
 * - Composed of atomic components
 * - WCAG 2.1 AA accessibility compliance
 * - TypeScript strict mode compliance
 * - Consistent design system integration
 * - Performance optimized
 * - Engineering-grade quality
 */

// Core Molecules
export { 
  InputField, 
  type InputFieldProps 
} from "./InputField"

export { 
  ButtonGroup, 
  ActionButtonGroup,
  type ButtonGroupProps, 
  type ActionButtonGroupProps,
  type ButtonConfig,
  type ButtonGroupOrientation,
  type ButtonGroupSpacing,
  buttonGroupVariants
} from "./ButtonGroup"