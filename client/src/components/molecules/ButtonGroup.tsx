/**
 * ButtonGroup Molecule - Compound Component
 * 
 * Atomic design molecule for grouping multiple Button atoms with
 * consistent layout, spacing, and accessibility.
 * 
 * Features:
 * - Atomic design composition (multiple Buttons)
 * - WCAG 2.1 AA accessibility compliance
 * - TypeScript strict mode compliance
 * - Horizontal and vertical layouts
 * - Consistent spacing system
 * - Group ARIA labeling
 * - Performance optimized
 * - Engineering-grade quality
 */

import React from "react"
import type { VariantProps } from "class-variance-authority"
import { cva } from "class-variance-authority"

import { cn } from "@/lib/utils"
import { Button, type ButtonProps } from "../atoms/Button"

// ButtonGroup variants using CVA for consistent styling
const buttonGroupVariants = cva("flex", {
  variants: {
    orientation: {
      horizontal: "flex-row",
      vertical: "flex-col",
    },
    spacing: {
      none: "",
      sm: "",
      default: "",
      lg: "",
    },
    attached: {
      true: "",
      false: "",
    },
  },
  compoundVariants: [
    // Horizontal spacing
    {
      orientation: "horizontal",
      spacing: "sm",
      attached: false,
      class: "gap-1",
    },
    {
      orientation: "horizontal", 
      spacing: "default",
      attached: false,
      class: "gap-2",
    },
    {
      orientation: "horizontal",
      spacing: "lg", 
      attached: false,
      class: "gap-4",
    },
    // Vertical spacing
    {
      orientation: "vertical",
      spacing: "sm",
      attached: false,
      class: "gap-1",
    },
    {
      orientation: "vertical",
      spacing: "default",
      attached: false,
      class: "gap-2", 
    },
    {
      orientation: "vertical",
      spacing: "lg",
      attached: false,
      class: "gap-4",
    },
    // Attached horizontal styling
    {
      orientation: "horizontal",
      attached: true,
      class: "[&>button:not(:first-child)]:rounded-l-none [&>button:not(:last-child)]:rounded-r-none [&>button:not(:last-child)]:border-r-0",
    },
    // Attached vertical styling
    {
      orientation: "vertical",
      attached: true,
      class: "[&>button:not(:first-child)]:rounded-t-none [&>button:not(:last-child)]:rounded-b-none [&>button:not(:last-child)]:border-b-0",
    },
  ],
  defaultVariants: {
    orientation: "horizontal",
    spacing: "default",
    attached: false,
  },
})

export interface ButtonConfig extends ButtonProps {
  /** Unique key for the button */
  key?: string | number
  /** Button content */
  children: React.ReactNode
}

export interface ButtonGroupProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof buttonGroupVariants> {
  /** Array of button configurations */
  buttons: ButtonConfig[]
  /** ARIA label for the button group */
  "aria-label"?: string
  /** ARIA labelledby for the button group */
  "aria-labelledby"?: string
  /** Test identifier for automated testing */
  "data-testid"?: string
}

export const ButtonGroup = React.forwardRef<HTMLDivElement, ButtonGroupProps>(
  (
    {
      buttons,
      orientation,
      spacing,
      attached,
      className,
      role = "group",
      "aria-label": ariaLabel,
      "aria-labelledby": ariaLabelledby,
      "data-testid": testId,
      ...props
    },
    ref
  ) => {
    if (!buttons || buttons.length === 0) {
      console.warn("ButtonGroup: No buttons provided")
      return null
    }

    return (
      <div
        ref={ref}
        className={cn(
          buttonGroupVariants({ orientation, spacing, attached }),
          className
        )}
        role={role}
        aria-label={ariaLabel}
        aria-labelledby={ariaLabelledby}
        data-testid={testId}
        {...props}
      >
        {buttons.map((button, index) => {
          const { key, children, ...buttonProps } = button
          const buttonKey = key ?? index

          return (
            <Button
              key={buttonKey}
              data-testid={testId ? `${testId}-button-${buttonKey}` : undefined}
              {...buttonProps}
            >
              {children}
            </Button>
          )
        })}
      </div>
    )
  }
)

ButtonGroup.displayName = "ButtonGroup"

// Convenience component for common button group patterns
export interface ActionButtonGroupProps extends Omit<ButtonGroupProps, "buttons"> {
  /** Primary action configuration */
  primary?: ButtonConfig
  /** Secondary action configuration */
  secondary?: ButtonConfig
  /** Cancel action configuration */
  cancel?: ButtonConfig
  /** Additional action configurations */
  actions?: ButtonConfig[]
}

export const ActionButtonGroup = React.forwardRef<
  HTMLDivElement,
  ActionButtonGroupProps
>(
  (
    {
      primary,
      secondary,
      cancel,
      actions = [],
      ...props
    },
    ref
  ) => {
    const buttons: ButtonConfig[] = []

    // Add actions in order
    if (cancel) buttons.push({ ...cancel, variant: cancel.variant || "outline" })
    if (secondary) buttons.push({ ...secondary, variant: secondary.variant || "outline" })
    if (primary) buttons.push({ ...primary, variant: primary.variant || "default" })
    
    // Add additional actions
    buttons.push(...actions)

    return (
      <ButtonGroup
        ref={ref}
        buttons={buttons}
        {...props}
      />
    )
  }
)

ActionButtonGroup.displayName = "ActionButtonGroup"

// Export types for external use
export type ButtonGroupOrientation = NonNullable<ButtonGroupProps["orientation"]>
export type ButtonGroupSpacing = NonNullable<ButtonGroupProps["spacing"]>

// Export variants for external use
export { buttonGroupVariants }