"use client"

/**
 * TaskForm Component
 * 
 * A comprehensive form component for creating and editing tasks.
 * Uses React Hook Form for form management and validation.
 */
import * as React from "react"

import { zodResolver } from "@hookform/resolvers/zod"
import { format } from "date-fns"
import { CalendarIcon, Loader2, X } from "lucide-react"
import { useForm } from "react-hook-form"
import * as z from "zod"

import type { TaskCreate, TaskRead, TaskUpdate, UserRead } from "../../types/api"

import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar"
import { Badge } from "../ui/badge"
import { Button } from "../ui/button"
import { Calendar } from "../ui/calendar"
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "../ui/form"
import { Input } from "../ui/input"
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select"
import { Textarea } from "../ui/textarea"
import { useCreateTask, useUpdateTask } from "../../hooks/api/useTasks"
import { useUsersSummary } from "../../hooks/api/useUsers"
import { cn } from "../../lib/utils"

// Form validation schema
const taskFormSchema = z.object({
  title: z.string().min(1, "Title is required").max(255, "Title must be less than 255 characters"),
  description: z.string().max(2000, "Description must be less than 2000 characters").optional(),
  priority: z.enum(["Low", "Medium", "High", "Critical"]),
  status: z.enum(["Not Started", "In Progress", "On Hold", "Completed", "Blocked", "Overdue", "Review Pending", "Approved"]),
  due_date: z.date().optional(),
  assigned_user_ids: z.array(z.number()).optional(),
})

type TaskFormData = z.infer<typeof taskFormSchema>

// TaskForm Props
interface TaskFormProps {
  projectId: number
  task?: TaskRead
  onSuccess?: (task: TaskRead) => void
  onCancel?: () => void
  className?: string
}

/**
 * TaskForm - Main task form component
 */
function TaskForm({ projectId, task, onSuccess, onCancel, className }: TaskFormProps) {
  const isEditing = !!task
  const [selectedUsers, setSelectedUsers] = React.useState<UserRead[]>([])

  // React Query hooks
  const createTaskMutation = useCreateTask()
  const updateTaskMutation = useUpdateTask()
  const { data: users = [] } = useUsersSummary()

  // Form setup
  const form = useForm<TaskFormData>({
    resolver: zodResolver(taskFormSchema),
    defaultValues: {
      title: task?.title || "",
      description: task?.description || "",
      priority: task?.priority || "Medium",
      status: task?.status || "Not Started",
      due_date: task?.due_date ? new Date(task.due_date) : undefined,
      assigned_user_ids: task?.assignments?.map(a => a.user_id) || [],
    },
  })

  // Initialize selected users from task assignments
  React.useEffect(() => {
    if (task?.assignments && users.length > 0) {
      const assignedUsers = users.filter(user => 
        task.assignments?.some(assignment => assignment.user_id === user.id)
      )
      setSelectedUsers(assignedUsers)
    }
  }, [task?.assignments, users])

  // Handle form submission
  const onSubmit = async (data: TaskFormData) => {
    try {
      if (isEditing && task) {
        const updateData: TaskUpdate = {
          title: data.title,
          description: data.description,
          priority: data.priority,
          status: data.status,
          due_date: data.due_date,
        }

        const updatedTask = await updateTaskMutation.mutateAsync({
          projectId,
          taskId: task.task_id,
          data: updateData,
        })

        onSuccess?.(updatedTask)
      } else {
        const createData: TaskCreate = {
          project_id: projectId,
          title: data.title,
          description: data.description,
          priority: data.priority,
          status: data.status,
          due_date: data.due_date,
          assigned_user_ids: data.assigned_user_ids,
        }

        const newTask = await createTaskMutation.mutateAsync({
          projectId,
          data: createData,
        })

        onSuccess?.(newTask)
      }
    } catch (error) {
      console.error("Task form submission error:", error)
    }
  }

  // Handle user selection
  const handleUserSelect = (userId: string) => {
    const userIdNum = parseInt(userId)
    const user = users.find(u => u.id === userIdNum)
    
    if (user && !selectedUsers.find(u => u.id === userIdNum)) {
      const newSelectedUsers = [...selectedUsers, user]
      setSelectedUsers(newSelectedUsers)
      form.setValue("assigned_user_ids", newSelectedUsers.map(u => u.id))
    }
  }

  // Handle user removal
  const handleUserRemove = (userId: number) => {
    const newSelectedUsers = selectedUsers.filter(u => u.id !== userId)
    setSelectedUsers(newSelectedUsers)
    form.setValue("assigned_user_ids", newSelectedUsers.map(u => u.id))
  }

  const isSubmitting = createTaskMutation.isPending || updateTaskMutation.isPending

  return (
    <Card className={cn("w-full max-w-2xl", className)}>
      <CardHeader>
        <CardTitle>
          {isEditing ? "Edit Task" : "Create New Task"}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Title */}
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter task title"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Description */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter task description"
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Provide detailed information about the task
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Priority and Status */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="priority"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Priority</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select priority" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Low">Low</SelectItem>
                        <SelectItem value="Medium">Medium</SelectItem>
                        <SelectItem value="High">High</SelectItem>
                        <SelectItem value="Critical">Critical</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Not Started">Not Started</SelectItem>
                        <SelectItem value="In Progress">In Progress</SelectItem>
                        <SelectItem value="On Hold">On Hold</SelectItem>
                        <SelectItem value="Completed">Completed</SelectItem>
                        <SelectItem value="Blocked">Blocked</SelectItem>
                        <SelectItem value="Review Pending">Review Pending</SelectItem>
                        <SelectItem value="Approved">Approved</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Due Date */}
            <FormField
              control={form.control}
              name="due_date"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Due Date</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {field.value ? (
                            format(field.value, "PPP")
                          ) : (
                            <span>Pick a date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) => date < new Date("1900-01-01")}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormDescription>
                    Optional deadline for task completion
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* User Assignment (only for new tasks) */}
            {!isEditing && (
              <div className="space-y-3">
                <FormLabel>Assign Users</FormLabel>
                <div className="space-y-3">
                  <Select onValueChange={handleUserSelect}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select users to assign" />
                    </SelectTrigger>
                    <SelectContent>
                      {users
                        .filter(user => !selectedUsers.find(su => su.id === user.id))
                        .map((user) => (
                          <SelectItem key={user.id} value={user.id.toString()}>
                            <div className="flex items-center gap-2">
                              <Avatar className="h-6 w-6">
                                <AvatarImage src={`/api/users/${user.id}/avatar`} />
                                <AvatarFallback className="text-xs">
                                  {user.name.slice(0, 2).toUpperCase()}
                                </AvatarFallback>
                              </Avatar>
                              <span>{user.name}</span>
                              <span className="text-muted-foreground text-sm">({user.email})</span>
                            </div>
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>

                  {/* Selected Users */}
                  {selectedUsers.length > 0 && (
                    <div className="space-y-2">
                      <div className="text-sm font-medium">Assigned Users:</div>
                      <div className="flex flex-wrap gap-2">
                        {selectedUsers.map((user) => (
                          <Badge key={user.id} variant="secondary" className="flex items-center gap-1">
                            <Avatar className="h-4 w-4">
                              <AvatarImage src={`/api/users/${user.id}/avatar`} />
                              <AvatarFallback className="text-xs">
                                {user.name.slice(0, 2).toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                            <span>{user.name}</span>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                              onClick={() => handleUserRemove(user.id)}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Form Actions */}
            <div className="flex items-center justify-end gap-3 pt-4">
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
              )}
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isEditing ? "Update Task" : "Create Task"}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}

export { TaskForm }
