"use client"

/**
 * TaskList Component
 *
 * A comprehensive organism component for displaying and managing lists of tasks.
 * Integrates with React Query for data fetching and RBAC for permission-based UI.
 */
import * as React from "react"

import type { TaskFilters, TaskRead, TaskSortOptions } from "../../types/api"

import { useTasks } from "../../hooks/api/useTasks"
import { useAuth } from "../../hooks/useAuth"
import { useTaskWebSocket } from "../../hooks/useTaskWebSocket"
import { cn } from "../../lib/utils"
import { Alert, AlertDescription } from "../ui/alert"
import { Button } from "../ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card"
import { Input } from "../ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select"
import { Skeleton } from "../ui/skeleton"
import { TaskCard, TaskCardSkeleton } from "./TaskCard"

// Icons
const SearchIcon = ({ className, ...props }: { className?: string } & React.SVGProps<SVGSVGElement>) => (
  <svg
    className={cn("h-4 w-4", className)}
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
    {...props}
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
    />
  </svg>
)

const FilterIcon = () => (
  <svg
    className="h-4 w-4"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z"
    />
  </svg>
)

const PlusIcon = () => (
  <svg
    className="h-4 w-4"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M12 4v16m8-8H4"
    />
  </svg>
)

const RefreshIcon = () => (
  <svg
    className="h-4 w-4"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
    />
  </svg>
)

const WifiIcon = () => (
  <svg
    className="h-4 w-4"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0"
    />
  </svg>
)

const WifiOffIcon = () => (
  <svg
    className="h-4 w-4"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M18.364 5.636L5.636 18.364m12.728 0L5.636 5.636m7.071 7.071L9.879 9.879m2.828 2.828l2.829 2.829"
    />
  </svg>
)

// TaskList Props
interface TaskListProps {
  projectId: number
  onTaskClick?: (task: TaskRead) => void
  onTaskEdit?: (task: TaskRead) => void
  onTaskDelete?: (task: TaskRead) => void
  onTaskCreate?: () => void
  onTaskAssign?: (task: TaskRead) => void
  showFilters?: boolean
  showSearch?: boolean
  showActions?: boolean
  showCreateButton?: boolean
  compact?: boolean
  className?: string
  initialFilters?: TaskFilters
  initialSort?: TaskSortOptions
}

/**
 * TaskList - Main task list component
 */
function TaskList({
  projectId,
  onTaskClick,
  onTaskEdit,
  onTaskDelete,
  onTaskCreate,
  onTaskAssign,
  showFilters = true,
  showSearch = true,
  showActions = true,
  showCreateButton = true,
  compact = false,
  className,
  initialFilters,
  initialSort,
}: TaskListProps) {
  // State for filters and search
  const [filters, setFilters] = React.useState<TaskFilters>(
    initialFilters || {}
  )
  const [sort, setSort] = React.useState<TaskSortOptions>(
    initialSort || { field: "created_at", order: "desc" }
  )
  const [searchTerm, setSearchTerm] = React.useState("")
  const [showFiltersPanel, setShowFiltersPanel] = React.useState(false)

  // Auth and permissions
  const { hasRole } = useAuth()
  const canCreateTasks =
    hasRole("Project Manager") ||
    hasRole("Lead Engineer") ||
    hasRole("Administrator")
  const canEditTasks =
    hasRole("Project Manager") ||
    hasRole("Lead Engineer") ||
    hasRole("Administrator")
  const canDeleteTasks = hasRole("Project Manager") || hasRole("Administrator")
  const canAssignTasks =
    hasRole("Project Manager") ||
    hasRole("Lead Engineer") ||
    hasRole("Administrator")

  // Combine search term with filters
  const effectiveFilters = React.useMemo(() => {
    const combined = { ...filters }
    if (searchTerm.trim()) {
      combined.search = searchTerm.trim()
    }
    return combined
  }, [filters, searchTerm])

  // Fetch tasks using React Query
  const {
    data: tasks = [],
    isLoading,
    isError,
    error,
    refetch,
    isFetching,
  } = useTasks(projectId, effectiveFilters, sort)

  // WebSocket integration for real-time updates
  const { isConnected } = useTaskWebSocket(projectId, {
    enableLogging: process.env.NODE_ENV === "development",
  })

  // Handle filter changes
  const handleStatusFilter = (status: string) => {
    setFilters((prev) => ({
      ...prev,
      status: status === "all" ? undefined : [status],
    }))
  }

  const handlePriorityFilter = (priority: string) => {
    setFilters((prev) => ({
      ...prev,
      priority: priority === "all" ? undefined : [priority],
    }))
  }

  const handleSortChange = (field: string) => {
    setSort((prev) => ({
      field: field as TaskSortOptions["field"],
      order: prev.field === field && prev.order === "asc" ? "desc" : "asc",
    }))
  }

  const handleClearFilters = () => {
    setFilters({})
    setSearchTerm("")
  }

  const handleRefresh = () => {
    refetch()
  }

  // Loading state
  if (isLoading) {
    return (
      <div className={cn("space-y-4", className)}>
        {showSearch && (
          <div className="flex items-center gap-2">
            <Skeleton className="h-10 flex-1" />
            <Skeleton className="h-10 w-20" />
          </div>
        )}
        <div className="grid gap-4">
          {Array.from({ length: 6 }).map((_, i) => (
            <TaskCardSkeleton key={i} compact={compact} />
          ))}
        </div>
      </div>
    )
  }

  // Error state
  if (isError) {
    return (
      <div className={cn("space-y-4", className)}>
        <Alert variant="destructive">
          <AlertDescription>
            Failed to load tasks: {error?.message || "Unknown error"}
          </AlertDescription>
        </Alert>
        <div className="flex justify-center">
          <Button onClick={handleRefresh} variant="outline">
            <RefreshIcon />
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Header with search and actions */}
      <div className="flex items-center justify-between gap-4">
        <div className="flex flex-1 items-center gap-2">
          {showSearch && (
            <div className="relative max-w-md flex-1">
              <SearchIcon className="text-muted-foreground absolute top-1/2 left-3 -translate-y-1/2 transform" />
              <Input
                placeholder="Search tasks..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          )}

          {showFilters && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFiltersPanel(!showFiltersPanel)}
              className={cn({ "bg-accent": showFiltersPanel })}
            >
              <FilterIcon />
              Filters
            </Button>
          )}

          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isFetching}
            className={cn({ "animate-spin": isFetching })}
          >
            <RefreshIcon />
          </Button>

          {/* WebSocket Connection Indicator */}
          <div className="flex items-center gap-1">
            {isConnected ? (
              <div className="flex items-center gap-1 text-green-600 dark:text-green-400">
                <WifiIcon />
                <span className="text-xs">Live</span>
              </div>
            ) : (
              <div className="flex items-center gap-1 text-gray-400">
                <WifiOffIcon />
                <span className="text-xs">Offline</span>
              </div>
            )}
          </div>
        </div>

        {showCreateButton && canCreateTasks && onTaskCreate && (
          <Button onClick={onTaskCreate} size="sm">
            <PlusIcon />
            New Task
          </Button>
        )}
      </div>

      {/* Filters Panel */}
      {showFilters && showFiltersPanel && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Filters</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
              {/* Status Filter */}
              <div>
                <label className="mb-2 block text-sm font-medium">Status</label>
                <Select onValueChange={handleStatusFilter} defaultValue="all">
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="Not Started">Not Started</SelectItem>
                    <SelectItem value="In Progress">In Progress</SelectItem>
                    <SelectItem value="On Hold">On Hold</SelectItem>
                    <SelectItem value="Completed">Completed</SelectItem>
                    <SelectItem value="Blocked">Blocked</SelectItem>
                    <SelectItem value="Overdue">Overdue</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Priority Filter */}
              <div>
                <label className="mb-2 block text-sm font-medium">
                  Priority
                </label>
                <Select onValueChange={handlePriorityFilter} defaultValue="all">
                  <SelectTrigger>
                    <SelectValue placeholder="All priorities" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Priorities</SelectItem>
                    <SelectItem value="Low">Low</SelectItem>
                    <SelectItem value="Medium">Medium</SelectItem>
                    <SelectItem value="High">High</SelectItem>
                    <SelectItem value="Critical">Critical</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Sort */}
              <div>
                <label className="mb-2 block text-sm font-medium">
                  Sort By
                </label>
                <Select
                  onValueChange={handleSortChange}
                  defaultValue={sort.field}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="title">Title</SelectItem>
                    <SelectItem value="status">Status</SelectItem>
                    <SelectItem value="priority">Priority</SelectItem>
                    <SelectItem value="due_date">Due Date</SelectItem>
                    <SelectItem value="created_at">Created</SelectItem>
                    <SelectItem value="updated_at">Updated</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex justify-end">
              <Button variant="outline" size="sm" onClick={handleClearFilters}>
                Clear Filters
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Task List */}
      <div className="space-y-4">
        {tasks.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <div className="space-y-2 text-center">
                <h3 className="text-lg font-medium">No tasks found</h3>
                <p className="text-muted-foreground">
                  {searchTerm || Object.keys(effectiveFilters).length > 0
                    ? "Try adjusting your search or filters"
                    : "Get started by creating your first task"}
                </p>
                {canCreateTasks && onTaskCreate && (
                  <Button onClick={onTaskCreate} className="mt-4">
                    <PlusIcon />
                    Create Task
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-4">
            {tasks.map((task) => (
              <TaskCard
                key={task.task_id}
                task={task}
                onClick={onTaskClick}
                onEdit={canEditTasks ? onTaskEdit : undefined}
                onDelete={canDeleteTasks ? onTaskDelete : undefined}
                onAssign={canAssignTasks ? onTaskAssign : undefined}
                showActions={showActions}
                compact={compact}
              />
            ))}
          </div>
        )}
      </div>

      {/* Results summary */}
      {tasks.length > 0 && (
        <div className="text-muted-foreground text-center text-sm">
          Showing {tasks.length} task{tasks.length !== 1 ? "s" : ""}
          {searchTerm && ` matching "${searchTerm}"`}
        </div>
      )}
    </div>
  )
}

/**
 * TaskListSkeleton - Loading skeleton for TaskList
 */
function TaskListSkeleton({ compact = false }: { compact?: boolean }) {
  return (
    <div className="space-y-4">
      {/* Header skeleton */}
      <div className="flex items-center justify-between gap-4">
        <div className="flex flex-1 items-center gap-2">
          <Skeleton className="h-10 max-w-md flex-1" />
          <Skeleton className="h-10 w-20" />
          <Skeleton className="h-10 w-10" />
        </div>
        <Skeleton className="h-10 w-24" />
      </div>

      {/* Task cards skeleton */}
      <div className="grid gap-4">
        {Array.from({ length: 6 }).map((_, i) => (
          <TaskCardSkeleton key={i} compact={compact} />
        ))}
      </div>
    </div>
  )
}

export { TaskList, TaskListSkeleton }
