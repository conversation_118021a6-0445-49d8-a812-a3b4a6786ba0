/**
 * TaskCard Unit Tests
 * Comprehensive testing for the TaskCard component
 */

import type { TaskRead } from "../../../types/api"

import { render, screen } from "@testing-library/react"
import userEvent from "@testing-library/user-event"
import { describe, expect, it, vi } from "vitest"

import { TaskCard, TaskCardSkeleton } from "../TaskCard"

// Mock task data
const mockTask: TaskRead = {
  id: 1,
  task_id: "task-123",
  project_id: 1,
  title: "Test Task",
  description: "This is a test task description",
  status: "In Progress",
  priority: "High",
  due_date: new Date("2024-12-31T23:59:59Z"),
  created_at: "2024-01-01T00:00:00Z",
  updated_at: "2024-01-15T12:00:00Z",
  assignments: [
    {
      id: 1,
      task_id: 1,
      user_id: 101,
      assigned_at: new Date("2024-01-01T00:00:00Z"),
      assigned_by_user_id: null,
      is_active: true,
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-01T00:00:00Z",
    },
    {
      id: 2,
      task_id: 1,
      user_id: 102,
      assigned_at: new Date("2024-01-01T00:00:00Z"),
      assigned_by_user_id: null,
      is_active: true,
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-01T00:00:00Z",
    },
  ],
}

const mockTaskWithoutAssignments: TaskRead = {
  ...mockTask,
  assignments: [],
}

const mockOverdueTask: TaskRead = {
  ...mockTask,
  task_id: "overdue-task",
  title: "Overdue Task",
  due_date: new Date("2023-12-31T23:59:59Z"), // Past date
  status: "Overdue",
}

const mockTaskDueToday: TaskRead = {
  ...mockTask,
  task_id: "due-today-task",
  title: "Due Today Task",
  due_date: new Date(), // Today
}

describe("TaskCard", () => {
  describe("Rendering", () => {
    it("renders task title and description", () => {
      render(<TaskCard task={mockTask} />)

      expect(screen.getByText("Test Task")).toBeInTheDocument()
      expect(
        screen.getByText("This is a test task description")
      ).toBeInTheDocument()
    })

    it("renders status and priority badges", () => {
      render(<TaskCard task={mockTask} />)

      expect(screen.getByText("In Progress")).toBeInTheDocument()
      expect(screen.getByText("High")).toBeInTheDocument()
    })

    it("renders due date when present", () => {
      render(<TaskCard task={mockTask} />)

      expect(screen.getByText(/Due Jan 1, 2025/)).toBeInTheDocument()
    })

    it("renders created date", () => {
      render(<TaskCard task={mockTask} />)

      expect(screen.getByText(/Created Jan 1/)).toBeInTheDocument()
    })

    it("renders assignees when present", () => {
      render(<TaskCard task={mockTask} />)

      // Should show avatar buttons for assigned users
      const avatarButtons = screen.getAllByText(/^0[12]$/)
      expect(avatarButtons).toHaveLength(2) // Two assignees
    })

    it("does not render assignees section when no assignments", () => {
      render(<TaskCard task={mockTaskWithoutAssignments} />)

      // Should not show the user icon or avatars
      expect(screen.queryByText(/^0[12]$/)).not.toBeInTheDocument()
    })
  })

  describe("Conditional Rendering", () => {
    it("hides description when showDescription is false", () => {
      render(<TaskCard task={mockTask} showDescription={false} />)

      expect(screen.getByText("Test Task")).toBeInTheDocument()
      expect(
        screen.queryByText("This is a test task description")
      ).not.toBeInTheDocument()
    })

    it("hides status badge when showStatus is false", () => {
      render(<TaskCard task={mockTask} showStatus={false} />)

      expect(screen.queryByText("In Progress")).not.toBeInTheDocument()
      expect(screen.getByText("High")).toBeInTheDocument() // Priority should still show
    })

    it("hides priority badge when showPriority is false", () => {
      render(<TaskCard task={mockTask} showPriority={false} />)

      expect(screen.getByText("In Progress")).toBeInTheDocument() // Status should still show
      expect(screen.queryByText("High")).not.toBeInTheDocument()
    })

    it("hides due date when showDueDate is false", () => {
      render(<TaskCard task={mockTask} showDueDate={false} />)

      expect(screen.queryByText(/Due Dec 31, 2024/)).not.toBeInTheDocument()
    })

    it("hides assignees when showAssignees is false", () => {
      render(<TaskCard task={mockTask} showAssignees={false} />)

      expect(screen.queryByRole("img")).not.toBeInTheDocument()
    })

    it("hides action buttons when showActions is false", () => {
      const mockOnEdit = vi.fn()
      const mockOnDelete = vi.fn()

      render(
        <TaskCard
          task={mockTask}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          showActions={false}
        />
      )

      expect(screen.queryByText("Edit")).not.toBeInTheDocument()
      expect(screen.queryByText("Delete")).not.toBeInTheDocument()
    })
  })

  describe("Compact Mode", () => {
    it("renders in compact mode", () => {
      render(<TaskCard task={mockTask} compact={true} />)

      // Title should still be visible
      expect(screen.getByText("Test Task")).toBeInTheDocument()

      // Description should be hidden in compact mode
      expect(
        screen.queryByText("This is a test task description")
      ).not.toBeInTheDocument()

      // Footer should be hidden in compact mode
      expect(screen.queryByText(/Created Jan 1/)).not.toBeInTheDocument()
    })
  })

  describe("Visual States", () => {
    it("applies overdue styling for overdue tasks", () => {
      render(<TaskCard task={mockOverdueTask} />)

      const card = screen
        .getByText("Overdue Task")
        .closest("[class*='border-red']")
      expect(card).toBeInTheDocument()

      expect(screen.getAllByText("Overdue")).toHaveLength(2) // Status badge and overdue badge
    })

    it("applies due today styling", () => {
      render(<TaskCard task={mockTaskDueToday} />)

      expect(screen.getByText("Due Today")).toBeInTheDocument()
    })
  })

  describe("Interactions", () => {
    it("calls onClick when card is clicked", async () => {
      const mockOnClick = vi.fn()
      const user = userEvent.setup()

      render(<TaskCard task={mockTask} onClick={mockOnClick} />)

      const card = screen.getByText("Test Task").closest("[data-slot='card']")
      await user.click(card!)

      expect(mockOnClick).toHaveBeenCalledWith(mockTask)
    })

    it("calls onEdit when edit button is clicked", async () => {
      const mockOnEdit = vi.fn()
      const user = userEvent.setup()

      render(<TaskCard task={mockTask} onEdit={mockOnEdit} />)

      const editButton = screen.getByText("Edit")
      await user.click(editButton)

      expect(mockOnEdit).toHaveBeenCalledWith(mockTask)
    })

    it("calls onDelete when delete button is clicked", async () => {
      const mockOnDelete = vi.fn()
      const user = userEvent.setup()

      render(<TaskCard task={mockTask} onDelete={mockOnDelete} />)

      const deleteButton = screen.getByText("Delete")
      await user.click(deleteButton)

      expect(mockOnDelete).toHaveBeenCalledWith(mockTask)
    })

    it("calls onAssign when assign button is clicked", async () => {
      const mockOnAssign = vi.fn()
      const user = userEvent.setup()

      render(<TaskCard task={mockTask} onAssign={mockOnAssign} />)

      const assignButton = screen.getByText("Assign")
      await user.click(assignButton)

      expect(mockOnAssign).toHaveBeenCalledWith(mockTask)
    })

    it("stops propagation when action buttons are clicked", async () => {
      const mockOnClick = vi.fn()
      const mockOnEdit = vi.fn()
      const user = userEvent.setup()

      render(
        <TaskCard task={mockTask} onClick={mockOnClick} onEdit={mockOnEdit} />
      )

      const editButton = screen.getByText("Edit")
      await user.click(editButton)

      // Card onClick should not be called when edit button is clicked
      expect(mockOnEdit).toHaveBeenCalledWith(mockTask)
      expect(mockOnClick).not.toHaveBeenCalled()
    })
  })

  describe("Action Button Visibility", () => {
    it("shows edit button when onEdit is provided", () => {
      const mockOnEdit = vi.fn()

      render(<TaskCard task={mockTask} onEdit={mockOnEdit} />)

      expect(screen.getByText("Edit")).toBeInTheDocument()
    })

    it("shows delete button when onDelete is provided", () => {
      const mockOnDelete = vi.fn()

      render(<TaskCard task={mockTask} onDelete={mockOnDelete} />)

      expect(screen.getByText("Delete")).toBeInTheDocument()
    })

    it("shows assign button when onAssign is provided", () => {
      const mockOnAssign = vi.fn()

      render(<TaskCard task={mockTask} onAssign={mockOnAssign} />)

      expect(screen.getByText("Assign")).toBeInTheDocument()
    })

    it("does not show buttons when handlers are not provided", () => {
      render(<TaskCard task={mockTask} />)

      expect(screen.queryByText("Edit")).not.toBeInTheDocument()
      expect(screen.queryByText("Delete")).not.toBeInTheDocument()
      expect(screen.queryByText("Assign")).not.toBeInTheDocument()
    })
  })

  describe("Assignee Display", () => {
    it("shows multiple assignees with avatars", () => {
      render(<TaskCard task={mockTask} />)

      const avatars = screen.getAllByText(/^0[12]$/)
      expect(avatars).toHaveLength(2)
    })

    it("shows overflow indicator for many assignees", () => {
      const taskWithManyAssignees: TaskRead = {
        ...mockTask,
        assignments: [
          {
            id: 1,
            task_id: 1,
            user_id: 101,
            assigned_at: new Date("2024-01-01T00:00:00Z"),
            assigned_by_user_id: null,
            is_active: true,
            created_at: "2024-01-01T00:00:00Z",
            updated_at: "2024-01-01T00:00:00Z",
          },
          {
            id: 2,
            task_id: 1,
            user_id: 102,
            assigned_at: new Date("2024-01-01T00:00:00Z"),
            assigned_by_user_id: null,
            is_active: true,
            created_at: "2024-01-01T00:00:00Z",
            updated_at: "2024-01-01T00:00:00Z",
          },
          {
            id: 3,
            task_id: 1,
            user_id: 103,
            assigned_at: new Date("2024-01-01T00:00:00Z"),
            assigned_by_user_id: null,
            is_active: true,
            created_at: "2024-01-01T00:00:00Z",
            updated_at: "2024-01-01T00:00:00Z",
          },
          {
            id: 4,
            task_id: 1,
            user_id: 104,
            assigned_at: new Date("2024-01-01T00:00:00Z"),
            assigned_by_user_id: null,
            is_active: true,
            created_at: "2024-01-01T00:00:00Z",
            updated_at: "2024-01-01T00:00:00Z",
          },
          {
            id: 5,
            task_id: 1,
            user_id: 105,
            assigned_at: new Date("2024-01-01T00:00:00Z"),
            assigned_by_user_id: null,
            is_active: true,
            created_at: "2024-01-01T00:00:00Z",
            updated_at: "2024-01-01T00:00:00Z",
          },
        ],
      }

      render(<TaskCard task={taskWithManyAssignees} />)

      // Should show first 3 avatars plus overflow indicator
      const avatars = screen.getAllByText(/^0[123]$/)
      expect(avatars).toHaveLength(3)
      expect(screen.getByText("+2")).toBeInTheDocument()
    })
  })

  describe("Custom Styling", () => {
    it("applies custom className", () => {
      render(<TaskCard task={mockTask} className="custom-task-card" />)

      const card = screen.getByText("Test Task").closest(".custom-task-card")
      expect(card).toBeInTheDocument()
    })
  })
})

describe("TaskCardSkeleton", () => {
  it("renders loading skeleton", () => {
    render(<TaskCardSkeleton />)

    // Should render skeleton elements with animate-pulse class
    const skeletonElements = document.querySelectorAll(".animate-pulse")
    expect(skeletonElements.length).toBeGreaterThan(0)
  })

  it("renders compact skeleton when compact prop is true", () => {
    render(<TaskCardSkeleton compact={true} />)

    const skeletonElements = document.querySelectorAll(".animate-pulse")
    expect(skeletonElements.length).toBeGreaterThan(0)
  })
})
