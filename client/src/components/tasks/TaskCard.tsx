"use client"

/**
 * TaskCard Component
 * 
 * A comprehensive card component for displaying task information.
 * Uses shadcn-ui Card components with task-specific styling and functionality.
 */
import * as React from "react"

import { format, isAfter, isBefore, isToday } from "date-fns"

import type { TaskRead, TaskWithAssignments } from "../../types/api"

import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar"
import { Badge } from "../ui/badge"
import { But<PERSON> } from "../ui/button"
import { Card, CardContent, CardFooter, CardHeader } from "../ui/card"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "../ui/tooltip"
import { TaskStatusBadge, TaskPriorityBadge } from "./TaskStatusBadge"
import { cn } from "../../lib/utils"

// Icons (you can replace with your preferred icon library)
const CalendarIcon = () => (
  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
  </svg>
)

const UserIcon = () => (
  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
  </svg>
)

const ClockIcon = () => (
  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
)

const MoreIcon = () => (
  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
  </svg>
)

// Task Card Props
interface TaskCardProps {
  task: TaskRead | TaskWithAssignments
  onClick?: (task: TaskRead | TaskWithAssignments) => void
  onEdit?: (task: TaskRead | TaskWithAssignments) => void
  onDelete?: (task: TaskRead | TaskWithAssignments) => void
  onAssign?: (task: TaskRead | TaskWithAssignments) => void
  showActions?: boolean
  showAssignees?: boolean
  showDescription?: boolean
  showDueDate?: boolean
  showPriority?: boolean
  showStatus?: boolean
  compact?: boolean
  className?: string
}

/**
 * TaskCard - Main task card component
 */
function TaskCard({
  task,
  onClick,
  onEdit,
  onDelete,
  onAssign,
  showActions = true,
  showAssignees = true,
  showDescription = true,
  showDueDate = true,
  showPriority = true,
  showStatus = true,
  compact = false,
  className,
}: TaskCardProps) {
  const isOverdue = task.due_date && isAfter(new Date(), new Date(task.due_date))
  const isDueToday = task.due_date && isToday(new Date(task.due_date))
  const isDueSoon = task.due_date && isBefore(new Date(), new Date(task.due_date)) && 
    isBefore(new Date(task.due_date), new Date(Date.now() + 3 * 24 * 60 * 60 * 1000)) // 3 days

  const handleCardClick = () => {
    if (onClick) {
      onClick(task)
    }
  }

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (onEdit) {
      onEdit(task)
    }
  }

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (onDelete) {
      onDelete(task)
    }
  }

  const handleAssign = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (onAssign) {
      onAssign(task)
    }
  }

  return (
    <Card
      className={cn(
        "cursor-pointer transition-all duration-200 hover:shadow-md",
        {
          "border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950": isOverdue,
          "border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950": isDueToday,
          "border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950": isDueSoon,
          "p-3": compact,
        },
        className
      )}
      onClick={handleCardClick}
    >
      <CardHeader className={cn("pb-2", { "pb-1": compact })}>
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <h3 className={cn(
              "font-semibold text-foreground truncate",
              compact ? "text-sm" : "text-base"
            )}>
              {task.title}
            </h3>
            {showDescription && task.description && !compact && (
              <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                {task.description}
              </p>
            )}
          </div>
          
          {showActions && (
            <div className="flex items-center gap-1 ml-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleEdit}
                className="h-8 w-8 p-0"
              >
                <span className="sr-only">Edit task</span>
                <MoreIcon />
              </Button>
            </div>
          )}
        </div>

        {/* Status and Priority Badges */}
        <div className="flex items-center gap-2 mt-2">
          {showStatus && (
            <TaskStatusBadge status={task.status} />
          )}
          {showPriority && (
            <TaskPriorityBadge priority={task.priority} />
          )}
        </div>
      </CardHeader>

      <CardContent className={cn("pt-0", { "py-1": compact })}>
        {/* Due Date */}
        {showDueDate && task.due_date && (
          <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
            <CalendarIcon />
            <span className={cn({
              "text-red-600 dark:text-red-400 font-medium": isOverdue,
              "text-yellow-600 dark:text-yellow-400 font-medium": isDueToday,
              "text-blue-600 dark:text-blue-400": isDueSoon,
            })}>
              Due {format(new Date(task.due_date), "MMM d, yyyy")}
            </span>
            {isOverdue && <Badge variant="destructive" className="text-xs">Overdue</Badge>}
            {isDueToday && <Badge className="text-xs bg-yellow-500">Due Today</Badge>}
          </div>
        )}

        {/* Assignees */}
        {showAssignees && task.assignments && task.assignments.length > 0 && (
          <div className="flex items-center gap-2">
            <UserIcon />
            <div className="flex items-center gap-1">
              {task.assignments.slice(0, 3).map((assignment) => (
                <TooltipProvider key={assignment.id}>
                  <Tooltip>
                    <TooltipTrigger>
                      <Avatar className="h-6 w-6">
                        <AvatarImage src={`/api/users/${assignment.user_id}/avatar`} />
                        <AvatarFallback className="text-xs">
                          {assignment.user_id.toString().slice(-2)}
                        </AvatarFallback>
                      </Avatar>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>User {assignment.user_id}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              ))}
              {task.assignments.length > 3 && (
                <Badge variant="secondary" className="text-xs">
                  +{task.assignments.length - 3}
                </Badge>
              )}
            </div>
          </div>
        )}
      </CardContent>

      {!compact && (
        <CardFooter className="pt-0 flex items-center justify-between">
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <ClockIcon />
            <span>
              Created {format(new Date(task.created_at), "MMM d")}
            </span>
          </div>

          {showActions && (
            <div className="flex items-center gap-1">
              {onAssign && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleAssign}
                  className="h-7 text-xs"
                >
                  Assign
                </Button>
              )}
              {onEdit && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleEdit}
                  className="h-7 text-xs"
                >
                  Edit
                </Button>
              )}
              {onDelete && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDelete}
                  className="h-7 text-xs text-red-600 hover:text-red-700"
                >
                  Delete
                </Button>
              )}
            </div>
          )}
        </CardFooter>
      )}
    </Card>
  )
}

/**
 * TaskCardSkeleton - Loading skeleton for TaskCard
 */
function TaskCardSkeleton({ compact = false }: { compact?: boolean }) {
  return (
    <Card className={cn("animate-pulse", { "p-3": compact })}>
      <CardHeader className={cn("pb-2", { "pb-1": compact })}>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className={cn(
              "h-4 bg-muted rounded w-3/4",
              compact ? "h-3" : "h-4"
            )} />
            {!compact && (
              <div className="h-3 bg-muted rounded w-1/2 mt-2" />
            )}
          </div>
          <div className="h-8 w-8 bg-muted rounded ml-2" />
        </div>
        <div className="flex items-center gap-2 mt-2">
          <div className="h-6 bg-muted rounded-full w-16" />
          <div className="h-6 bg-muted rounded-full w-12" />
        </div>
      </CardHeader>

      <CardContent className={cn("pt-0", { "py-1": compact })}>
        <div className="flex items-center gap-2 mb-2">
          <div className="h-4 w-4 bg-muted rounded" />
          <div className="h-4 bg-muted rounded w-24" />
        </div>
        <div className="flex items-center gap-2">
          <div className="h-4 w-4 bg-muted rounded" />
          <div className="flex gap-1">
            <div className="h-6 w-6 bg-muted rounded-full" />
            <div className="h-6 w-6 bg-muted rounded-full" />
          </div>
        </div>
      </CardContent>

      {!compact && (
        <CardFooter className="pt-0 flex items-center justify-between">
          <div className="h-4 bg-muted rounded w-20" />
          <div className="flex gap-1">
            <div className="h-7 bg-muted rounded w-12" />
            <div className="h-7 bg-muted rounded w-12" />
          </div>
        </CardFooter>
      )}
    </Card>
  )
}

export { TaskCard, TaskCardSkeleton }
