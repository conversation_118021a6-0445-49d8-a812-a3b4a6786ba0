/**
 * Input Atom - Universal Foundational Component
 * 
 * Atomic design input component providing core input functionality
 * with validation states, accessibility, and engineering-grade quality.
 * 
 * Features:
 * - Atomic design principles (single responsibility)
 * - WCAG 2.1 AA accessibility compliance
 * - TypeScript strict mode compliance
 * - Validation state support (error, warning, success)
 * - Consistent sizing system
 * - Performance optimized with minimal footprint
 * - Full backward compatibility support
 */

import React from "react"
import type { VariantProps } from "class-variance-authority"
import { cva } from "class-variance-authority"

import { cn } from "@/lib/utils"

// Input variants using CVA for consistent styling
const inputVariants = cva(
  "border-input file:text-foreground placeholder:text-muted-foreground/70 flex w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-sm shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]",
  {
    variants: {
      inputSize: {
        sm: "h-8 px-2.5 py-1 text-xs",
        default: "h-9 px-3 py-1 text-sm", 
        lg: "h-12 px-4 py-2 text-base",
      },
      state: {
        default: "",
        error: "border-destructive focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",
        warning: "border-yellow-400 focus-visible:ring-yellow-400/50", 
        success: "border-green-400 focus-visible:ring-green-400/50",
      },
      variant: {
        default: "bg-transparent",
        filled: "bg-muted/50",
        ghost: "border-transparent bg-transparent shadow-none focus-visible:border-ring",
      },
    },
    compoundVariants: [
      {
        variant: "ghost",
        state: "error",
        class: "focus-visible:border-destructive",
      },
      {
        variant: "ghost", 
        state: "warning",
        class: "focus-visible:border-yellow-400",
      },
      {
        variant: "ghost",
        state: "success", 
        class: "focus-visible:border-green-400",
      },
    ],
    defaultVariants: {
      inputSize: "default",
      state: "default",
      variant: "default",
    },
  }
)

export interface InputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, "size">,
    VariantProps<typeof inputVariants> {
  /** Validation state of the input */
  state?: "default" | "error" | "warning" | "success"
  /** Test identifier for automated testing */
  "data-testid"?: string
}

export const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      type = "text",
      inputSize,
      state,
      variant,
      "data-testid": testId,
      ...props
    },
    ref
  ) => {
    return (
      <input
        ref={ref}
        type={type}
        className={cn(
          inputVariants({ inputSize, state, variant }),
          // Type-specific styles
          type === "search" &&
            "[&::-webkit-search-cancel-button]:appearance-none [&::-webkit-search-decoration]:appearance-none [&::-webkit-search-results-button]:appearance-none [&::-webkit-search-results-decoration]:appearance-none",
          type === "file" &&
            "text-muted-foreground/70 file:border-input file:text-foreground p-0 pr-3 italic file:me-3 file:h-full file:border-0 file:border-r file:border-solid file:bg-transparent file:px-3 file:text-sm file:font-medium file:not-italic",
          className
        )}
        data-testid={testId}
        aria-invalid={state === "error"}
        {...props}
      />
    )
  }
)

Input.displayName = "Input"

// Export types for external use
export type InputSize = NonNullable<InputProps["inputSize"]>
export type InputState = NonNullable<InputProps["state"]>
export type InputVariant = NonNullable<InputProps["variant"]>

// Export variants for external use
export { inputVariants }