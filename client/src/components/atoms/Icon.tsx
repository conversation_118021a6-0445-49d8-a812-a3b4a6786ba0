/**
 * Icon Atom - Universal Foundational Component
 * 
 * Atomic design icon component providing core icon functionality
 * with consistent sizing, accessibility, and engineering-grade quality.
 * 
 * Features:
 * - Atomic design principles (single responsibility)
 * - WCAG 2.1 AA accessibility compliance
 * - TypeScript strict mode compliance
 * - Lucide React integration
 * - Consistent sizing system
 * - Performance optimized with tree shaking
 * - Full backward compatibility support
 */

import React from "react"
import type { VariantProps } from "class-variance-authority"
import { cva } from "class-variance-authority"
import type { LucideIcon, LucideProps } from "lucide-react"
import {
  // Common UI Icons
  AlertCircle,
  ArrowDown,
  ArrowLeft,
  ArrowRight,
  ArrowUp,
  Check,
  CheckCircle,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ChevronUp,
  Copy,
  Download,
  Edit,
  Eye,
  EyeOff,
  File,
  Filter,
  Heart,
  Home,
  Info,
  Loader2,
  Lock,
  Mail,
  Menu,
  MoreHorizontal,
  MoreVertical,
  Plus,
  PlusCircle,
  Search,
  Settings,
  Star,
  Trash2,
  Upload,
  User,
  X,
  XCircle,
  // Electrical/Technical Icons  
  Battery,
  CircuitBoard,
  Component,
  Cpu,
  HardDrive,
  Lightbulb,
  Microchip,
  Monitor,
  Plug,
  Power,
  Shield,
  Smartphone,
  Thermometer,
  Wifi,
  Wrench,
  Zap,
} from "lucide-react"

import { cn } from "@/lib/utils"

// Icon variants using CVA for consistent styling
const iconVariants = cva("shrink-0", {
  variants: {
    size: {
      xs: "size-3",
      sm: "size-4", 
      default: "size-5",
      lg: "size-6",
      xl: "size-8",
      "2xl": "size-10",
    },
    color: {
      default: "text-current",
      muted: "text-muted-foreground",
      primary: "text-primary",
      secondary: "text-secondary-foreground", 
      accent: "text-accent-foreground",
      destructive: "text-destructive",
      success: "text-green-600",
      warning: "text-yellow-600",
      info: "text-blue-600",
    },
  },
  defaultVariants: {
    size: "default",
    color: "default",
  },
})

// Icon mapping for common icons
const iconMap = {
  // Common UI
  "alert-circle": AlertCircle,
  "arrow-down": ArrowDown,
  "arrow-left": ArrowLeft,
  "arrow-right": ArrowRight,
  "arrow-up": ArrowUp,
  check: Check,
  "check-circle": CheckCircle,
  "chevron-down": ChevronDown,
  "chevron-left": ChevronLeft,
  "chevron-right": ChevronRight,
  "chevron-up": ChevronUp,
  copy: Copy,
  download: Download,
  edit: Edit,
  eye: Eye,
  "eye-off": EyeOff,
  file: File,
  filter: Filter,
  heart: Heart,
  home: Home,
  info: Info,
  loader: Loader2,
  lock: Lock,
  mail: Mail,
  menu: Menu,
  "more-horizontal": MoreHorizontal,
  "more-vertical": MoreVertical,
  plus: Plus,
  "plus-circle": PlusCircle,
  search: Search,
  settings: Settings,
  star: Star,
  trash: Trash2,
  upload: Upload,
  user: User,
  x: X,
  "x-circle": XCircle,
  
  // Electrical/Technical
  battery: Battery,
  "circuit-board": CircuitBoard,
  component: Component,
  cpu: Cpu,
  "hard-drive": HardDrive,
  lightbulb: Lightbulb,
  microchip: Microchip,
  monitor: Monitor,
  plug: Plug,
  power: Power,
  shield: Shield,
  smartphone: Smartphone,
  thermometer: Thermometer,
  wifi: Wifi,
  wrench: Wrench,
  zap: Zap,
} as const

export type IconName = keyof typeof iconMap

export interface IconProps
  extends Omit<LucideProps, "size" | "color">,
    VariantProps<typeof iconVariants> {
  /** Icon name from the available icon set */
  name: IconName
  /** Custom icon component (overrides name) */
  icon?: LucideIcon
  /** Test identifier for automated testing */
  "data-testid"?: string
}

export const Icon = React.forwardRef<SVGSVGElement, IconProps>(
  (
    {
      name,
      icon: CustomIcon,
      size,
      color,
      className,
      "data-testid": testId,
      ...props
    },
    ref
  ) => {
    const IconComponent = CustomIcon || iconMap[name]

    if (!IconComponent) {
      console.warn(`Icon: Invalid icon name "${name}". Available icons: ${Object.keys(iconMap).join(", ")}`)
      return null
    }

    return (
      <IconComponent
        ref={ref}
        className={cn(iconVariants({ size, color }), className)}
        data-testid={testId || `icon-${name}`}
        aria-hidden="true"
        {...props}
      />
    )
  }
)

Icon.displayName = "Icon"

// Export types for external use
export type IconSize = NonNullable<IconProps["size"]>
export type IconColor = NonNullable<IconProps["color"]>

// Export variants and icon map for external use
export { iconVariants, iconMap }

// Utility functions
export const hasIcon = (name: string): name is IconName => {
  return name in iconMap
}

export const getAvailableIcons = (): IconName[] => {
  return Object.keys(iconMap) as IconName[]
}