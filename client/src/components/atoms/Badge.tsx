/**
 * Badge Atom - Universal Foundational Component
 * 
 * Atomic design badge component providing core badge functionality
 * with consistent styling, accessibility, and engineering-grade quality.
 * 
 * Features:
 * - Atomic design principles (single responsibility)
 * - WCAG 2.1 AA accessibility compliance
 * - TypeScript strict mode compliance
 * - Consistent variant system
 * - Performance optimized with minimal footprint
 * - Full backward compatibility support
 */

import React from "react"
import type { VariantProps } from "class-variance-authority"
import { cva } from "class-variance-authority"

import { cn } from "@/lib/utils"

// Badge variants using CVA for consistent styling
const badgeVariants = cva(
  "inline-flex items-center gap-1 rounded-md border px-2 py-1 text-xs font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default: "border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",
        secondary: "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive: "border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",
        outline: "text-foreground",
        success: "border-transparent bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900 dark:text-green-100",
        warning: "border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200 dark:bg-yellow-900 dark:text-yellow-100",
        info: "border-transparent bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-100",
      },
      size: {
        sm: "px-1.5 py-0.5 text-xs",
        default: "px-2 py-1 text-xs",
        lg: "px-3 py-1.5 text-sm",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLSpanElement>,
    VariantProps<typeof badgeVariants> {
  /** Interactive badge (clickable) */
  interactive?: boolean
  /** Pulse animation */
  pulse?: boolean
  /** Test identifier for automated testing */
  "data-testid"?: string
}

export const Badge = React.forwardRef<HTMLSpanElement, BadgeProps>(
  (
    {
      className,
      variant,
      size,
      interactive = false,
      pulse = false,
      role = "status",
      tabIndex,
      "data-testid": testId,
      ...props
    },
    ref
  ) => {
    return (
      <span
        ref={ref}
        className={cn(
          badgeVariants({ variant, size }),
          interactive && "cursor-pointer hover:opacity-80 focus:ring-2 focus:ring-ring focus:ring-offset-2",
          pulse && "animate-pulse",
          className
        )}
        role={role}
        tabIndex={interactive ? (tabIndex ?? 0) : undefined}
        data-testid={testId}
        {...props}
      />
    )
  }
)

Badge.displayName = "Badge"

// Export types for external use
export type BadgeVariant = NonNullable<BadgeProps["variant"]>
export type BadgeSize = NonNullable<BadgeProps["size"]>

// Export variants for external use
export { badgeVariants }