/**
 * Button Atom - Universal Foundational Component
 * 
 * Atomic design button component providing core button functionality
 * with consistent styling, accessibility, and engineering-grade quality.
 * 
 * Features:
 * - Atomic design principles (single responsibility)
 * - WCAG 2.1 AA accessibility compliance
 * - TypeScript strict mode compliance
 * - Performance optimized with minimal footprint
 * - Consistent design system integration
 * - Full backward compatibility support
 */

import React from "react"
import type { VariantProps } from "class-variance-authority"
import { cva } from "class-variance-authority"
import { Slot } from "@radix-ui/react-slot"
import { Loader2 } from "lucide-react"

import { cn } from "@/lib/utils"

// Button variants using CVA for consistent styling
const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]",
  {
    variants: {
      variant: {
        default:
          "bg-primary text-primary-foreground shadow-sm hover:bg-primary/90",
        destructive:
          "bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",
        outline:
          "border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground",
        secondary:
          "bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default: "h-9 px-4 py-2",
        sm: "h-8 rounded-md px-3 text-xs",
        lg: "h-10 rounded-md px-8",
        icon: "size-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  /** Render as a different component while preserving button styling */
  asChild?: boolean
  /** Show loading state with spinner */
  loading?: boolean
  /** Custom loading text to display */
  loadingText?: string
  /** Make button full width */
  fullWidth?: boolean
  /** Test identifier for automated testing */
  "data-testid"?: string
}

export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant,
      size,
      asChild = false,
      loading = false,
      loadingText,
      fullWidth = false,
      disabled = false,
      children,
      "data-testid": testId,
      ...props
    },
    ref
  ) => {
    const Comp = asChild ? Slot : "button"

    const isDisabled = disabled || loading

    return (
      <Comp
        ref={ref}
        className={cn(
          buttonVariants({ variant, size }),
          fullWidth && "w-full",
          loading && "cursor-not-allowed",
          className
        )}
        disabled={isDisabled}
        aria-disabled={isDisabled}
        data-testid={testId}
        {...props}
      >
        {loading && (
          <>
            <Loader2 className="size-4 animate-spin" aria-hidden="true" />
            {loadingText ? (
              <span className="truncate">{loadingText}</span>
            ) : (
              children && <span className="truncate">{children}</span>
            )}
          </>
        )}
        {!loading && children}
      </Comp>
    )
  }
)

Button.displayName = "Button"

// Export types for external use
export type ButtonVariant = NonNullable<ButtonProps["variant"]>
export type ButtonSize = NonNullable<ButtonProps["size"]>

// Export variants for external use
export { buttonVariants }