// Mock UserRole enum instead of importing it
enum UserRole {
  VIEWER = 'VIEWER',
  EDITOR = 'EDITOR',
  ADMIN = 'ADMIN',
}
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { render, RenderOptions } from '@testing-library/react'
import { ReactElement } from 'react'
import { vi } from 'vitest'

// Create a custom render function that includes providers
const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  })

interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  queryClient?: QueryClient
}

export function renderWithProviders(ui: ReactElement, options: CustomRenderOptions = {}) {
  const { queryClient = createTestQueryClient(), ...renderOptions } = options

  function Wrapper({ children }: { children: React.ReactNode }) {
    return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  }

  return {
    ...render(ui, { wrapper: Wrapper, ...renderOptions }),
    queryClient,
  }
}

// Mock data factories
// Import UserRole from '@/types/api' at the top of the file
export const mockUser = {
  id: 1,
  name: 'Test User',
  email: '<EMAIL>',
  role: UserRole.VIEWER,
  is_active: true,
  is_superuser: false,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  last_login: new Date('2024-01-01T00:00:00Z'),
}

export const mockAdminUser = {
  ...mockUser,
  id: 2,
  name: 'Admin User',
  email: '<EMAIL>',
  role: UserRole.ADMIN,
  is_superuser: true,
}

export const mockLoginResponse = {
  access_token: 'mock-token',
  token_type: 'bearer',
  expires_in: 3600,
  user: mockUser,
}

export const mockApiErrorObject = {
  detail: 'Test error message',
  error_code: 'TEST_ERROR',
  timestamp: '2024-01-01T00:00:00Z',
}

export const mockApiError = (message: string = 'Test error', code: string = 'TEST_ERROR') => ({
  detail: message,
  error_code: code,
  timestamp: '2024-01-01T00:00:00Z',
})

// API response factories
export const mockApiSuccess = <T,>(data: T) => ({
  data,
  error: undefined,
  status: 200,
})

export const mockApiErrorResponse = (
  message: string = 'Test error',
  code: string = 'TEST_ERROR'
) => ({
  data: undefined,
  error: {
    detail: message,
    error_code: code,
    timestamp: '2024-01-01T00:00:00Z',
  },
  status: 400,
})

// Mock API responses
export const mockFetch = (response: any, status = 200) => {
  return vi.fn().mockResolvedValue({
    ok: status >= 200 && status < 300,
    status,
    json: () => Promise.resolve(response),
  })
}

export const mockFetchError = (error: any, status = 400) => {
  return vi.fn().mockResolvedValue({
    ok: false,
    status,
    json: () => Promise.resolve(error),
  })
}

// Re-export everything from testing-library
export * from '@testing-library/react'
export { act } from '@testing-library/react'
export { default as userEvent } from '@testing-library/user-event'

// Re-export component test factories
export * from './factories/componentFactories'
