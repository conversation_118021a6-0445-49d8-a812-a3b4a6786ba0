/**
 * Integration tests for IndexedDBPersister with mocked idb library
 *
 * These tests verify the IndexedDBPersister works with a mocked IndexedDB instance
 * using vitest mocking for consistent test behavior.
 */

import { afterEach, beforeEach, describe, expect, it } from "vitest"
import type { PersistedClient } from "@tanstack/react-query-persist-client"
import { IndexedDBPersister } from "../indexed_db_persister"

// idb library is mocked globally in vitest.setup.ts

// Only run integration tests in browser-like environment
describe("IndexedDBPersister Integration Tests", () => {
  let persister: IndexedDBPersister
  const testDbName = "test-integration-db"

  beforeEach(async () => {
    // Create persister with unique database name for each test
    persister = new IndexedDBPersister({
      dbName: `${testDbName}-${Date.now()}`,
      dbVersion: 1,
      storeName: "integration-cache",
      cacheKey: "integration-key",
    })
  })

  afterEach(async () => {
    // Clean up test database
    try {
      await persister.clearAll()
    } catch (error) {
      // Ignore cleanup errors
    }
  })

  it("should handle complete persist and restore cycle", async () => {
    const testClient: PersistedClient = {
      clientState: {
        queries: [
          {
            queryKey: ["test", "query"],
            queryHash: '["test","query"]',
            state: {
              data: { id: 1, name: "Test Item" },
              dataUpdateCount: 1,
              dataUpdatedAt: Date.now(),
              error: null,
              errorUpdateCount: 0,
              errorUpdatedAt: 0,
              fetchFailureCount: 0,
              fetchFailureReason: null,
              fetchMeta: null,
              isInvalidated: false,
              status: "success" as const,
              fetchStatus: "idle" as const,
            },
          },
        ],
        mutations: [],
      },
      buster: "integration-test-buster",
      timestamp: Date.now(),
    }

    // Persist the client data
    await persister.persistClient(testClient)

    // Restore the client data
    const restoredClient = await persister.restoreClient()

    // Verify the data was properly restored
    expect(restoredClient).toBeDefined()
    expect(restoredClient?.buster).toBe(testClient.buster)
    expect(restoredClient?.clientState.queries).toHaveLength(1)
    expect(restoredClient?.clientState.queries[0].queryKey).toEqual([
      "test",
      "query",
    ])
    expect(restoredClient?.clientState.queries[0].state.data).toEqual({
      id: 1,
      name: "Test Item",
    })
  })

  it("should return undefined when no data has been persisted", async () => {
    const restoredClient = await persister.restoreClient()
    expect(restoredClient).toBeUndefined()
  })

  it("should handle removeClient operation", async () => {
    const testClient: PersistedClient = {
      clientState: {
        queries: [],
        mutations: [],
      },
      buster: "remove-test-buster",
      timestamp: Date.now(),
    }

    // Persist then remove
    await persister.persistClient(testClient)
    await persister.removeClient()

    // Should return undefined after removal
    const restoredClient = await persister.restoreClient()
    expect(restoredClient).toBeUndefined()
  })

  it("should provide accurate cache statistics", async () => {
    // Initial stats should show empty cache
    let stats = await persister.getCacheStats()
    expect(stats.isSupported).toBe(true)
    expect(stats.cacheSize).toBe(0)
    expect(stats.lastUpdated).toBeUndefined()

    // Persist some data
    const testClient: PersistedClient = {
      clientState: {
        queries: [],
        mutations: [],
      },
      buster: "stats-test-buster",
      timestamp: Date.now(),
    }

    const beforePersist = Date.now()
    await persister.persistClient(testClient)
    const afterPersist = Date.now()

    // Stats should now reflect the persisted data
    stats = await persister.getCacheStats()
    expect(stats.isSupported).toBe(true)
    expect(stats.cacheSize).toBeGreaterThan(0)
    expect(stats.lastUpdated).toBeGreaterThanOrEqual(beforePersist)
    expect(stats.lastUpdated).toBeLessThanOrEqual(afterPersist)
  })

  it("should handle multiple persist operations with latest data winning", async () => {
    const firstClient: PersistedClient = {
      clientState: {
        queries: [],
        mutations: [],
      },
      buster: "first-buster",
      timestamp: Date.now(),
    }

    const secondClient: PersistedClient = {
      clientState: {
        queries: [],
        mutations: [],
      },
      buster: "second-buster",
      timestamp: Date.now(),
    }

    // Persist first client
    await persister.persistClient(firstClient)

    // Persist second client (should overwrite)
    await persister.persistClient(secondClient)

    // Should restore the second client
    const restoredClient = await persister.restoreClient()
    expect(restoredClient?.buster).toBe("second-buster")
  })

  it("should handle clearAll operation", async () => {
    const testClient: PersistedClient = {
      clientState: {
        queries: [],
        mutations: [],
      },
      buster: "clear-test-buster",
      timestamp: Date.now(),
    }

    // Persist data
    await persister.persistClient(testClient)

    // Verify data exists
    let restoredClient = await persister.restoreClient()
    expect(restoredClient).toBeDefined()

    // Clear all data
    await persister.clearAll()

    // Verify data is gone
    restoredClient = await persister.restoreClient()
    expect(restoredClient).toBeUndefined()
  })
})

describe("IndexedDBPersister Browser Compatibility", () => {
  it("should detect IndexedDB support correctly", async () => {
    // In jsdom environment, IndexedDB should be available
    const persister = new IndexedDBPersister()
    const stats = await persister.getCacheStats()

    // Should report as supported in test environment
    expect(stats.isSupported).toBe(true)
  })
})
