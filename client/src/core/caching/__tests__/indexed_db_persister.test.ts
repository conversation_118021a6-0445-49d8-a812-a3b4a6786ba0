/**
 * Unit tests for IndexedDBPersister
 *
 * These tests verify the IndexedDBPersister implementation using mock IndexedDB
 * to ensure it properly implements the Persister interface for React Query.
 */

import { afterEach, beforeEach, describe, expect, it, vi } from "vitest"
import type { PersistedClient } from "@tanstack/react-query-persist-client"
import {
  createIndexedDBPersister,
  IndexedDBPersister,
  isIndexedDBSupported,
} from "../indexed_db_persister"

// idb library is mocked globally in vitest.setup.ts

// Mock IndexedDB for browser environment simulation
const mockDB = {
  objectStoreNames: {
    contains: vi.fn(() => false),
  },
  createObjectStore: vi.fn((_name, _options) => ({
    createIndex: vi.fn(),
  })),
  get: vi.fn(),
  put: vi.fn(),
  delete: vi.fn(),
  count: vi.fn(),
  clear: vi.fn(),
  transaction: vi.fn(() => ({
    objectStore: vi.fn(() => ({
      clear: vi.fn(),
    })),
    done: Promise.resolve(),
  })),
}

// Get the mocked openDB function from the global mock
const idbModule = vi.mocked(await import("idb"))
const { openDB } = idbModule

describe("IndexedDBPersister", () => {
  let persister: IndexedDBPersister

  beforeEach(() => {
    vi.clearAllMocks()

    // Setup mock database
    openDB.mockResolvedValue(mockDB as any)

    // Create persister instance with test configuration
    persister = new IndexedDBPersister({
      dbName: "test-cache-db",
      dbVersion: 1,
      storeName: "test-cache",
      cacheKey: "test-key",
    })
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe("constructor and initialization", () => {
    it("should create persister with default configuration", () => {
      const defaultPersister = new IndexedDBPersister()
      expect(defaultPersister).toBeInstanceOf(IndexedDBPersister)
    })

    it("should create persister with custom configuration", () => {
      const customConfig = {
        dbName: "custom-db",
        dbVersion: 2,
        storeName: "custom-store",
        cacheKey: "custom-key",
      }

      const customPersister = new IndexedDBPersister(customConfig)
      expect(customPersister).toBeInstanceOf(IndexedDBPersister)
    })

    it("should initialize IndexedDB with correct parameters", async () => {
      // Trigger database initialization by calling a method
      await persister.restoreClient()

      expect(openDB).toHaveBeenCalledWith(
        "test-cache-db",
        1,
        expect.any(Object)
      )
    })

    it("should create required object stores during upgrade", async () => {
      const upgradeCallback = openDB.mock.calls[0]?.[2]?.upgrade

      if (upgradeCallback) {
        // Simulate upgrade scenario
        upgradeCallback(mockDB as any, 0, 1, {} as any, {} as any)

        expect(mockDB.createObjectStore).toHaveBeenCalledWith("query-cache")
        expect(mockDB.createObjectStore).toHaveBeenCalledWith(
          "mutation-outbox",
          {
            keyPath: "id",
            autoIncrement: true,
          }
        )
      }
    })
  })

  describe("persistClient", () => {
    it("should persist client data successfully", async () => {
      const testClient: PersistedClient = {
        clientState: {
          queries: [],
          mutations: [],
        },
        buster: "test-buster",
        timestamp: Date.now(),
      }

      mockDB.put.mockResolvedValueOnce(undefined)

      await persister.persistClient(testClient)

      expect(mockDB.put).toHaveBeenCalledWith(
        "test-cache",
        expect.objectContaining({
          ...testClient,
          timestamp: expect.any(Number),
        }),
        "test-key"
      )
    })

    it("should handle persistence errors gracefully", async () => {
      const testClient: PersistedClient = {
        clientState: {
          queries: [],
          mutations: [],
        },
        buster: "test-buster",
        timestamp: Date.now(),
      }

      mockDB.put.mockRejectedValueOnce(new Error("Storage full"))

      // Should not throw error - graceful degradation
      await expect(persister.persistClient(testClient)).resolves.toBeUndefined()
    })

    it("should add timestamp to persisted data", async () => {
      const testClient: PersistedClient = {
        clientState: {
          queries: [],
          mutations: [],
        },
        buster: "test-buster",
        timestamp: Date.now(),
      }

      const beforeTimestamp = Date.now()
      await persister.persistClient(testClient)
      const afterTimestamp = Date.now()

      const putCall = mockDB.put.mock.calls[0]
      const persistedData = putCall[1]

      expect(persistedData.timestamp).toBeGreaterThanOrEqual(beforeTimestamp)
      expect(persistedData.timestamp).toBeLessThanOrEqual(afterTimestamp)
    })
  })

  describe("restoreClient", () => {
    it("should restore client data successfully", async () => {
      const storedData = {
        clientState: {
          queries: [],
          mutations: [],
        },
        buster: "test-buster",
        timestamp: Date.now(),
      }

      mockDB.get.mockResolvedValueOnce(storedData)

      const result = await persister.restoreClient()

      expect(mockDB.get).toHaveBeenCalledWith("test-cache", "test-key")
      expect(result).toEqual({
        clientState: storedData.clientState,
        buster: storedData.buster,
      })
      // Timestamp should be removed from returned data
      expect(result).not.toHaveProperty("timestamp")
    })

    it("should return undefined when no data is found", async () => {
      mockDB.get.mockResolvedValueOnce(undefined)

      const result = await persister.restoreClient()

      expect(result).toBeUndefined()
    })

    it("should handle restoration errors gracefully", async () => {
      mockDB.get.mockRejectedValueOnce(new Error("Database corrupt"))

      const result = await persister.restoreClient()

      expect(result).toBeUndefined()
    })

    it("should call database initialization on first access", async () => {
      await persister.restoreClient()

      expect(openDB).toHaveBeenCalledTimes(1)
    })
  })

  describe("removeClient", () => {
    it("should remove client data successfully", async () => {
      mockDB.delete.mockResolvedValueOnce(undefined)

      await persister.removeClient()

      expect(mockDB.delete).toHaveBeenCalledWith("test-cache", "test-key")
    })

    it("should handle removal errors gracefully", async () => {
      mockDB.delete.mockRejectedValueOnce(new Error("Delete failed"))

      // Should not throw error
      await expect(persister.removeClient()).resolves.toBeUndefined()
    })
  })

  describe("getCacheStats", () => {
    it("should return cache statistics when data exists", async () => {
      const mockCacheData = {
        clientState: { queries: [], mutations: [] },
        buster: "test-buster",
        timestamp: 1234567890,
      }

      mockDB.get.mockResolvedValueOnce(mockCacheData)
      mockDB.count.mockResolvedValueOnce(5)

      const stats = await persister.getCacheStats()

      expect(stats).toEqual({
        isSupported: true,
        cacheSize: JSON.stringify(mockCacheData).length,
        lastUpdated: 1234567890,
        outboxCount: 5,
      })
    })

    it("should return stats with zero values when no data exists", async () => {
      mockDB.get.mockResolvedValueOnce(undefined)
      mockDB.count.mockResolvedValueOnce(0)

      const stats = await persister.getCacheStats()

      expect(stats).toEqual({
        isSupported: true,
        cacheSize: 0,
        lastUpdated: undefined,
        outboxCount: 0,
      })
    })

    it("should handle errors and return unsupported status", async () => {
      mockDB.get.mockRejectedValueOnce(new Error("Stats failed"))

      const stats = await persister.getCacheStats()

      expect(stats).toEqual({
        isSupported: false,
      })
    })
  })

  describe("clearAll", () => {
    it("should clear all cache data successfully", async () => {
      const mockTransaction = {
        objectStore: vi.fn(() => ({
          clear: vi.fn(),
        })),
        done: Promise.resolve(),
      }

      mockDB.transaction.mockReturnValueOnce(mockTransaction)

      await persister.clearAll()

      expect(mockDB.transaction).toHaveBeenCalledWith(
        ["query-cache", "mutation-outbox"],
        "readwrite"
      )
      expect(mockTransaction.objectStore).toHaveBeenCalledWith("query-cache")
      expect(mockTransaction.objectStore).toHaveBeenCalledWith(
        "mutation-outbox"
      )
    })

    it("should throw error when clearing fails", async () => {
      mockDB.transaction.mockImplementationOnce(() => {
        throw new Error("Clear failed")
      })

      await expect(persister.clearAll()).rejects.toThrow("Clear failed")
    })
  })
})

describe("createIndexedDBPersister factory function", () => {
  it("should create persister with default configuration", () => {
    const persister = createIndexedDBPersister()
    expect(persister).toBeInstanceOf(IndexedDBPersister)
  })

  it("should create persister with custom configuration", () => {
    const config = {
      dbName: "factory-db",
      storeName: "factory-store",
    }

    const persister = createIndexedDBPersister(config)
    expect(persister).toBeInstanceOf(IndexedDBPersister)
  })
})

describe("isIndexedDBSupported utility", () => {
  it("should return true when IndexedDB is supported", () => {
    // Mock window and indexedDB
    Object.defineProperty(global, "window", {
      value: {
        indexedDB: {},
      },
      writable: true,
    })

    expect(isIndexedDBSupported()).toBe(true)
  })

  it("should return false when window is undefined", () => {
    // Mock server environment
    Object.defineProperty(global, "window", {
      value: undefined,
      writable: true,
    })

    expect(isIndexedDBSupported()).toBe(false)
  })

  it("should return false when indexedDB is not available", () => {
    // Mock window without IndexedDB
    Object.defineProperty(global, "window", {
      value: {},
      writable: true,
    })

    expect(isIndexedDBSupported()).toBe(false)
  })
})

describe("IndexedDB database event handlers", () => {
  let persister: IndexedDBPersister

  beforeEach(() => {
    persister = new IndexedDBPersister()
  })

  it("should handle blocked upgrade event", async () => {
    const consoleSpy = vi.spyOn(console, "warn").mockImplementation(() => {})

    // Trigger database initialization to get event handlers
    await persister.restoreClient()

    const blockedHandler = openDB.mock.calls[0]?.[2]?.blocked
    if (blockedHandler) {
      blockedHandler(1, 2, {} as any)
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining("IndexedDB upgrade blocked"),
        expect.any(Object)
      )
    }

    consoleSpy.mockRestore()
  })

  it("should handle blocking upgrade event", async () => {
    const consoleSpy = vi.spyOn(console, "warn").mockImplementation(() => {})

    // Trigger database initialization to get event handlers
    await persister.restoreClient()

    const blockingHandler = openDB.mock.calls[0]?.[2]?.blocking
    if (blockingHandler) {
      blockingHandler(1, 2, {} as any)
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining("IndexedDB blocking upgrade"),
        expect.any(Object)
      )
    }

    consoleSpy.mockRestore()
  })

  it("should handle terminated connection event", async () => {
    const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {})

    // Trigger database initialization to get event handlers
    await persister.restoreClient()

    const terminatedHandler = openDB.mock.calls[0]?.[2]?.terminated
    if (terminatedHandler) {
      terminatedHandler()
      expect(consoleSpy).toHaveBeenCalledWith(
        "IndexedDB connection terminated unexpectedly"
      )
    }

    consoleSpy.mockRestore()
  })
})
