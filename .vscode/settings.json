{
  "// Ultimate Electrical Designer - VS Code Configuration": "",
  "// Optimized for type safety development with SQLAlchemy workarounds": "",

  "// General workspace settings": "",

  "// Auto-formatting on save": "",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.addMissingImports": "never",
    "source.organizeImports": "never",
    "source.fixAll.markdownlint": "explicit"
  },

  "// File associations": "",
  "files.associations": {
    "*.py": "python",
    "*.pyi": "python",
    "mypy.ini": "ini",
    ".pre-commit-config.yaml": "yaml"
  },

  "// Read-only files and folders": "",
  "files.readonlyInclude": {
    "**/.venv/**": true
  },

  "// Exclude files and folders from the workspace": "",
  "files.exclude": {
    "**/__pycache__": true,
    "**/*.pyc": true,
    "**/.mypy_cache": true,
    "**/.pytest_cache": true,
    "**/node_modules": true,
    "**/.git": true,
    "**/.DS_Store": true,
    "**/Thumbs.db": true,
    "**/.history": true,
    "**/.ruff_cache": true,
    "**/~": true
  },

  "// Exclude files and folders from watcher": "",
  "files.watcherExclude": {
    "**/.benchmarks/**": true,
    "**/.next/**": true,
    "**/.tmp/**": true,
    "**/.vscode-remote/**": true,
    "**/build/**": true,
    "**/dist/**": true,
    "**/node_modules/**": true,
    "**/out/**": true,
    "**/tmp/**": true,
    "**/.history/**": true,
    "**/.ruff_cache/**": true,
    "**/.venv/**": true,
    "**/~": true
  },

  "// Exclude files and folders from search": "",
  "search.exclude": {
    "**/__pycache__": true,
    "**/.mypy_cache": true,
    "**/.pytest_cache": true,
    "**/*.code-search": true,
    "**/bower_components": true,
    "**/node_modules": true,
    "**/.history": true,
    "**/.ruff_cache": true,
    "**/.next": true,
    "**/~": true
  },

  "// Exclude files and folders from explorer auto-reveal": "",
  "explorer.autoRevealExclude": {
    "**/.history": true,
    "**/.ruff_cache": true,
    "**/.venv": true,
    "**/~": true
  },

  "// Python": "",
  "[python]": {
    "editor.tabSize": 4,
    "editor.insertSpaces": true,
    "editor.rulers": [120],
    "editor.wordWrap": "off"
  },
  "python.defaultInterpreterPath": "./server/.venv/bin/python",
  "python.terminal.activateEnvironment": true,
  "python.terminal.activateEnvInCurrentTerminal": true,
  "pytest.command": "./server/.venv/bin/pytest",
  "pytest_runner.pytest_exec": "./server/.venv/bin/pytest",

  "// Python analysis": "",
  "python.analysis.typeCheckingMode": "basic",
  "python.analysis.autoImportCompletions": true,
  "python.analysis.completeFunctionParens": true,
  "python.analysis.include": ["./server/"],
  "python.analysis.exclude": [
    // "**/src/core/models/**",
    // "**/src/core/repositories/**",
    // "**/src/core/services/**",
    // "**/src/api/**"
  ],

  "// Code Quality Settings": "",
  "python.linting.enabled": true,
  "python.linting.pylintEnabled": false,
  "python.linting.ruffEnabled": true,
  "python.linting.mypyEnabled": true,
  "python.linting.banditEnabled": true,

  "// MyPy Configuration": "",
  "python.linting.mypyArgs": [
    "--config-file=mypy.ini",
    "--show-error-codes",
    "--ignore-missing-imports",
    "--no-error-summary"
  ],

  "// Ruff Configuration": "",
  "python.linting.ruffArgs": ["--fix"],
  "// Bandit Configuration": "",
  "python.linting.banditArgs": ["-r", "server/src"],
  "// Formatting Configuration": "",
  "python.formatting.provider": "ruff",

  "// TypeScript": "",
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "typescript.tsdk": "node_modules/typescript/lib",

  "// Other": "",
  "[markdown]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "// Markdownlint Configuration": "",
  "markdownlint.configFile": "./.markdownlint.json",
  "markdownlint.lintWorkspaceGlobs": [
    "**/*.{md,mkd,mdwn,mdown,markdown,markdn,mdtxt,mdtext,workbook}",
    "!**/*.code-search",
    "!**/bower_components",
    "!**/node_modules",
    "!**/.git",
    "!**/vendor"
  ],
  "[yaml]": {
    "editor.tabSize": 2,
    "editor.insertSpaces": true
  },
  "[json]": {
    "editor.tabSize": 2,
    "editor.insertSpaces": true
  },

  "// Terminal configuration": "",
  "terminal.integrated.cwd": "",
  "terminal.integrated.env.linux": {
    "PYTHONPATH": "${workspaceFolder}/server/src"
  },
  "terminal.integrated.env.osx": {
    "PYTHONPATH": "${workspaceFolder}/server/src"
  },
  "terminal.integrated.env.windows": {
    "PYTHONPATH": "${workspaceFolder}/server/src"
  },
  "terminal.integrated.cursorStyle": "underline",
  "terminal.integrated.hideOnStartup": "whenEmpty",
  "terminal.integrated.persistentSessionReviveProcess": "onExitAndWindowClose",
  "terminal.integrated.profiles.windows": {
    "PowerShell": {
      "source": "PowerShell",
      "icon": "terminal-powershell"
    },
    "Command Prompt": {
      "path": ["${env:windir}\\Sysnative\\cmd.exe", "${env:windir}\\System32\\cmd.exe"],
      "args": [],
      "icon": "terminal-cmd"
    },
    "Git Bash": {
      "source": "Git Bash"
    }
  },

  "// Git configuration": "",
  "git.ignoreLimitWarning": true,

  "// Error Lens Configuration": "",
  "errorLens.lintFilePaths": {
    "ruff": ["**./*pyproject.toml"],
    "mypy": ["**./*mypy.ini"],
    "eslint": ["**/*.eslintrc.{js,cjs,yaml,yml,json}", "**/*package.json"],
    "Stylelint": [
      "**/*.stylelintrc",
      "**/*.stylelintrc.{cjs,js,json,yaml,yml}",
      "**/*stylelint.config.{cjs,js}",
      "**/*package.json"
    ]
  }
}
